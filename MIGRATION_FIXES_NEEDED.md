# 🔧 إصلاحات الانتقال المطلوبة - Migration Fixes Needed

## 📋 الملفات التي تحتاج إصلاح

### **الملفات التي تستورد الملفات المحذوفة**:

#### 1. **lib/features/auth/screens/login_screen.dart**
- ❌ `import '../../../core/utils/responsive_helper.dart';`
- ✅ **تم الإصلاح**: `import '../../../core/utils/unified_layout.dart';`
- 🔧 **يحتاج**: استبدال جميع `ResponsiveHelper` بـ `UnifiedLayout`

#### 2. **lib/features/accounts/screens/accounting_system_screen.dart**
- ❌ `import '../../../core/utils/responsive_helper.dart';`
- ✅ **الإصلاح**: `import '../../../core/utils/unified_layout.dart';`

#### 3. **lib/features/accounts/screens/journal_entries_screen.dart**
- ❌ `import '../../../core/utils/responsive_helper.dart';`
- ✅ **الإصلاح**: `import '../../../core/utils/unified_layout.dart';`

#### 4. **lib/features/accounts/screens/simple_account_form_screen.dart**
- ❌ `import '../../../core/utils/responsive_helper.dart';`
- ✅ **الإصلاح**: `import '../../../core/utils/unified_layout.dart';`

#### 5. **lib/features/auth/screens/register_screen.dart**
- ❌ `import '../../../core/utils/responsive_helper.dart';`
- ✅ **الإصلاح**: `import '../../../core/utils/unified_layout.dart';`

#### 6. **lib/features/warehouses/screens/warehouse_details_screen.dart**
- ❌ `import '../../../core/utils/layout_utils.dart';`
- ❌ `import '../../../core/utils/responsive_helper.dart';`
- ✅ **الإصلاح**: `import '../../../core/utils/unified_layout.dart';`

#### 7. **lib/features/warehouses/screens/warehouse_home_screen.dart**
- ❌ `import '../../../core/utils/responsive_helper.dart';`
- ✅ **الإصلاح**: `import '../../../core/utils/unified_layout.dart';`

#### 8. **lib/features/vouchers/presenters/voucher_presenter.dart**
- ❌ `import '../../../core/utils/context_helper.dart';`
- ✅ **الإصلاح**: `import '../../../core/utils/unified_helpers.dart';`

---

## 🔄 الاستبدالات المطلوبة

### **ResponsiveHelper → UnifiedLayout**:
```dart
// القديم
ResponsiveHelper.w(6) → UnifiedLayout.w(6)
ResponsiveHelper.h(2) → UnifiedLayout.h(2)
ResponsiveHelper.getResponsiveFontSize(16) → UnifiedLayout.getResponsiveFontSize(16)
ResponsiveHelper.getResponsiveIconSize(22) → UnifiedLayout.getResponsiveIconSize(22)
ResponsiveHelper.isMobile() → UnifiedLayout.isMobile()
Layout.isTablet() → UnifiedLayout.isTablet()
Layout.isDesktop() → UnifiedLayout.isDesktop()
```

### **LayoutUtils → UnifiedLayout**:
```dart
// القديم
LayoutUtils.init(context) → UnifiedLayout.init(context)
LayoutUtils.safeScreen(...) → UnifiedLayout.safeColumn(...)
LayoutUtils.safeCard(...) → UnifiedLayout.safeCard(...)
LayoutUtils.safeText(...) → UnifiedLayout.safeText(...)
LayoutUtils.safeRow(...) → UnifiedLayout.flexibleRow(...)
LayoutUtils.safeColumn(...) → UnifiedLayout.safeColumn(...)
LayoutUtils.getSafeMargin(...) → UnifiedLayout.getResponsiveMargin(...)
LayoutUtils.getSafePadding(...) → UnifiedLayout.getResponsivePadding(...)
```

### **ContextHelper → UnifiedHelpers**:
```dart
// القديم
ContextHelper.safeExecute(...) → UnifiedHelpers.safeExecute(...)
ContextHelper.safeShowDialog(...) → UnifiedHelpers.showConfirmDialog(...)
```

---

## 🎯 خطة الإصلاح

### **المرحلة 1**: إصلاح الاستيرادات
- [x] ✅ `login_screen.dart` - تم إصلاح الاستيراد
- [ ] ⏳ `accounting_system_screen.dart`
- [ ] ⏳ `journal_entries_screen.dart`
- [ ] ⏳ `simple_account_form_screen.dart`
- [ ] ⏳ `register_screen.dart`
- [ ] ⏳ `warehouse_details_screen.dart`
- [ ] ⏳ `warehouse_home_screen.dart`
- [ ] ⏳ `voucher_presenter.dart`

### **المرحلة 2**: استبدال الاستخدامات
- [ ] ⏳ استبدال جميع `ResponsiveHelper` بـ `UnifiedLayout`
- [ ] ⏳ استبدال جميع `LayoutUtils` بـ `UnifiedLayout`
- [ ] ⏳ استبدال جميع `ContextHelper` بـ `UnifiedHelpers`

### **المرحلة 3**: إضافة التهيئة
- [ ] ⏳ إضافة `UnifiedLayout.init(context)` في بداية كل `build` method

---

## 🚀 الأولوية

### **عالية الأولوية**:
1. **login_screen.dart** - شاشة رئيسية
2. **register_screen.dart** - شاشة رئيسية
3. **warehouse_details_screen.dart** - يستخدم LayoutUtils

### **متوسطة الأولوية**:
4. **accounting_system_screen.dart**
5. **journal_entries_screen.dart**
6. **simple_account_form_screen.dart**

### **منخفضة الأولوية**:
7. **warehouse_home_screen.dart**
8. **voucher_presenter.dart**

---

## 📝 ملاحظات

### **تحذيرات**:
- ⚠️ تأكد من إضافة `UnifiedLayout.init(context)` في بداية كل `build` method
- ⚠️ بعض الدوال قد تحتاج تعديل في المعاملات
- ⚠️ اختبر كل ملف بعد الإصلاح

### **نصائح**:
- 💡 استخدم البحث والاستبدال في IDE للسرعة
- 💡 اختبر الملفات واحد تلو الآخر
- 💡 تأكد من عدم وجود أخطاء compilation

---

**الحالة**: قيد التنفيذ ⏳  
**التقدم**: 1/8 ملفات (12.5%)  
**المطلوب**: إكمال الإصلاحات المتبقية
