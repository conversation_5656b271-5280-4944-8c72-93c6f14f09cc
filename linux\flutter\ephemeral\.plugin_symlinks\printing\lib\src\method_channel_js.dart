/*
 * Copyright (C) 2017, <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import 'dart:typed_data';

import 'print_job.dart';

/// Set the Pdf document data
void setDocumentFfi(PrintJob job, Uint8List data) {
  throw UnimplementedError('Not using FFI');
}

/// Set the Pdf Error message
void setErrorFfi(PrintJob job, String message) {
  throw UnimplementedError('Not using FFI');
}
