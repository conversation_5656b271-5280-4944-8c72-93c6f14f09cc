import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';
import 'package:crypto/crypto.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';
import '../auth/permission_initializer.dart';
import '../auth/roles_schema.dart';

/// مُهيئ البيانات الأساسية
/// يستخدم هذا الصف لتهيئة البيانات الأساسية بطريقة محسنة
/// تم توحيد جميع وظائف تهيئة البيانات في هذا الملف
class BasicDataInitializer {
  // تخزين معرفات الأدوار للاستخدام في إنشاء المستخدم الافتراضي
  static Map<String, String> _roleIds = {};

  // تخزين معرف مجموعة المدراء للاستخدام في إنشاء المستخدم الافتراضي
  static String? _adminGroupId;

  /// التحقق من وجود البيانات الأساسية
  /// هذه الدالة عامة يمكن استدعاؤها من أي مكان في التطبيق
  /// للتحقق من وجود البيانات الأساسية اللازمة لتشغيل التطبيق
  static Future<bool> checkBasicDataExists(Database db) async {
    try {
      AppLogger.info('🔍 جاري التحقق من وجود البيانات الأساسية (دالة عامة)...');
      final result = await _checkIfDataExists(db);

      // لا نحتاج إلى طباعة رسائل هنا لأن الدالة _checkIfDataExists تقوم بذلك بالفعل

      return result;
    } catch (e, stackTrace) {
      AppLogger.warning('⚠️ خطأ أثناء التحقق من وجود البيانات الأساسية: $e');
      ErrorTracker.captureError(
        'فشل في التحقق من وجود البيانات الأساسية',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'check_basic_data_exists'},
      );
      return false;
    }
  }

  /// تهيئة البيانات الأساسية
  static Future<void> initialize(Database db) async {
    try {
      AppLogger.info(
          '🚀 بدء تهيئة البيانات الأساسية باستخدام المُهيئ المحسن...');

      // قياس وقت التنفيذ الكلي
      final totalStopwatch = Stopwatch()..start();

      // التحقق من وجود بيانات بشكل دقيق
      final checkStopwatch = Stopwatch()..start();
      final hasData = await checkBasicDataExists(db);
      checkStopwatch.stop();
      AppLogger.info(
          '⏱️ استغرق التحقق من وجود البيانات: ${checkStopwatch.elapsedMilliseconds} مللي ثانية');

      if (hasData) {
        AppLogger.info('✅ البيانات الأساسية موجودة بالفعل، تخطي التهيئة');
        totalStopwatch.stop();
        AppLogger.info(
            '⏱️ إجمالي وقت العملية: ${totalStopwatch.elapsedMilliseconds} مللي ثانية');
        return;
      }

      AppLogger.info(
          '⚠️ البيانات الأساسية غير موجودة أو غير مكتملة، سيتم تهيئتها الآن...');

      // تنفيذ التهيئة في معاملة واحدة لضمان تكامل البيانات
      // استخدام Future.microtask لإعطاء فرصة للواجهة للتحديث
      await Future.microtask(() async {
        await db.transaction((txn) async {
          AppLogger.info('🔄 بدء معاملة تهيئة البيانات الأساسية...');

          // قياس وقت التنفيذ لكل خطوة
          final authStopwatch = Stopwatch()..start();

          // تهيئة بيانات المصادقة والصلاحيات (الأدوار، الصلاحيات، مجموعات المستخدمين، المستخدم الافتراضي)
          await _initializeAuthData(txn);
          authStopwatch.stop();
          AppLogger.info(
              '⏱️ استغرقت تهيئة بيانات المصادقة: ${authStopwatch.elapsedMilliseconds} مللي ثانية');

          // إعطاء فرصة للواجهة للتحديث
          await Future.delayed(Duration.zero);

          // تهيئة وحدات القياس الأساسية
          final unitsStopwatch = Stopwatch()..start();
          await _initializeBasicUnits(txn);
          unitsStopwatch.stop();
          AppLogger.info(
              '⏱️ استغرقت تهيئة وحدات القياس: ${unitsStopwatch.elapsedMilliseconds} مللي ثانية');

          // إعطاء فرصة للواجهة للتحديث
          await Future.delayed(Duration.zero);

          // تهيئة المخزن الافتراضي
          final warehouseStopwatch = Stopwatch()..start();
          await _initializeDefaultWarehouse(txn);
          warehouseStopwatch.stop();
          AppLogger.info(
              '⏱️ استغرقت تهيئة المخزن الافتراضي: ${warehouseStopwatch.elapsedMilliseconds} مللي ثانية');

          // إعطاء فرصة للواجهة للتحديث
          await Future.delayed(Duration.zero);

          // تهيئة طرق الدفع الأساسية
          final paymentStopwatch = Stopwatch()..start();
          await _initializeBasicPaymentMethods(txn);
          paymentStopwatch.stop();
          AppLogger.info(
              '⏱️ استغرقت تهيئة طرق الدفع: ${paymentStopwatch.elapsedMilliseconds} مللي ثانية');

          // إعطاء فرصة للواجهة للتحديث
          await Future.delayed(Duration.zero);

          // تهيئة إعدادات النظام الأساسية
          final settingsStopwatch = Stopwatch()..start();
          await _initializeBasicSettings(txn);
          settingsStopwatch.stop();
          AppLogger.info(
              '⏱️ استغرقت تهيئة الإعدادات: ${settingsStopwatch.elapsedMilliseconds} مللي ثانية');

          // إعطاء فرصة للواجهة للتحديث
          await Future.delayed(Duration.zero);

          // تهيئة الفرع الافتراضي
          final branchStopwatch = Stopwatch()..start();
          await _initializeDefaultBranch(txn);
          branchStopwatch.stop();
          AppLogger.info(
              '⏱️ استغرقت تهيئة الفرع الافتراضي: ${branchStopwatch.elapsedMilliseconds} مللي ثانية');

          // إعطاء فرصة للواجهة للتحديث
          await Future.delayed(Duration.zero);

          // ربط المستخدم الافتراضي بالفرع الافتراضي
          final linkStopwatch = Stopwatch()..start();
          await _linkAdminUserToDefaultBranch(txn);
          linkStopwatch.stop();
          AppLogger.info(
              '⏱️ استغرق ربط المستخدم بالفرع: ${linkStopwatch.elapsedMilliseconds} مللي ثانية');

          // إعطاء فرصة للواجهة للتحديث
          await Future.delayed(Duration.zero);

          // تهيئة العملات الأساسية
          final currenciesStopwatch = Stopwatch()..start();
          await _initializeBasicCurrencies(txn);
          currenciesStopwatch.stop();
          AppLogger.info(
              '⏱️ استغرقت تهيئة العملات الأساسية: ${currenciesStopwatch.elapsedMilliseconds} مللي ثانية');

          // تحليل أداء كل خطوة
          final steps = [
            {
              'name': 'المصادقة والصلاحيات',
              'time': authStopwatch.elapsedMilliseconds
            },
            {
              'name': 'وحدات القياس',
              'time': unitsStopwatch.elapsedMilliseconds
            },
            {
              'name': 'المخزن الافتراضي',
              'time': warehouseStopwatch.elapsedMilliseconds
            },
            {'name': 'طرق الدفع', 'time': paymentStopwatch.elapsedMilliseconds},
            {
              'name': 'الإعدادات',
              'time': settingsStopwatch.elapsedMilliseconds
            },
            {
              'name': 'الفرع الافتراضي',
              'time': branchStopwatch.elapsedMilliseconds
            },
          ];

          // ترتيب الخطوات حسب الوقت المستغرق (من الأطول إلى الأقصر)
          steps.sort((a, b) => (b['time'] as int).compareTo(a['time'] as int));

          // عرض تحليل الأداء
          AppLogger.info('📊 تحليل أداء خطوات التهيئة (من الأطول إلى الأقصر):');
          for (final step in steps) {
            AppLogger.info('   - ${step['name']}: ${step['time']} مللي ثانية');
          }

          AppLogger.info(
              '✅ تمت تهيئة جميع البيانات الأساسية داخل المعاملة بنجاح');
        });
      });

      // التحقق مرة أخرى للتأكد من نجاح التهيئة
      final verifyStopwatch = Stopwatch()..start();
      final dataInitialized = await checkBasicDataExists(db);
      verifyStopwatch.stop();
      AppLogger.info(
          '⏱️ استغرق التحقق النهائي: ${verifyStopwatch.elapsedMilliseconds} مللي ثانية');

      if (dataInitialized) {
        AppLogger.info('✅ تم التحقق من تهيئة البيانات الأساسية بنجاح');
      } else {
        AppLogger.warning('⚠️ لم يتم تهيئة بعض البيانات الأساسية بشكل صحيح');

        // محاولة إصلاح البيانات المفقودة
        final repairStopwatch = Stopwatch()..start();
        await _repairMissingData(db);
        repairStopwatch.stop();
        AppLogger.info(
            '⏱️ استغرق إصلاح البيانات المفقودة: ${repairStopwatch.elapsedMilliseconds} مللي ثانية');
      }

      // إيقاف قياس الوقت الكلي
      totalStopwatch.stop();
      AppLogger.info(
          '⏱️ إجمالي وقت تهيئة البيانات الأساسية: ${totalStopwatch.elapsedMilliseconds} مللي ثانية');

      AppLogger.info('✅ تم الانتهاء من تهيئة البيانات الأساسية');
    } catch (e, stackTrace) {
      AppLogger.error('❌ فشل في تهيئة البيانات الأساسية: $e');
      ErrorTracker.captureError(
        'فشل في تهيئة البيانات الأساسية',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'initialize_basic_data'},
      );
      rethrow;
    }
  }

  /// إصلاح البيانات المفقودة (واجهة عامة)
  /// تستخدم هذه الدالة لإصلاح البيانات الأساسية المفقودة
  static Future<void> repairMissingData(Database db) async {
    return _repairMissingData(db);
  }

  /// إصلاح البيانات المفقودة (تنفيذ داخلي)
  /// تستخدم هذه الدالة لإصلاح البيانات الأساسية المفقودة
  static Future<void> _repairMissingData(Database db) async {
    try {
      AppLogger.info('🔧 محاولة إصلاح البيانات الأساسية المفقودة...');

      // قياس وقت التنفيذ
      final stopwatch = Stopwatch()..start();

      // استخدام استعلام واحد للتحقق من وجود البيانات الأساسية
      // هذا يحسن الأداء بشكل كبير عن طريق تقليل عدد الاستعلامات
      final countsStopwatch = Stopwatch()..start();

      // استعلام واحد للحصول على عدد السجلات في كل جدول
      final counts = await Future.wait([
        db.rawQuery('SELECT COUNT(*) FROM users WHERE is_deleted = 0'),
        db.rawQuery('SELECT COUNT(*) FROM roles WHERE is_deleted = 0'),
        db.rawQuery('SELECT COUNT(*) FROM permissions WHERE is_deleted = 0'),
        db.rawQuery(
            'SELECT COUNT(*) FROM role_permissions WHERE is_deleted = 0'),
        db.rawQuery('SELECT COUNT(*) FROM units WHERE is_deleted = 0'),
        db.rawQuery(
            'SELECT COUNT(*) FROM warehouses WHERE is_default = 1 AND is_deleted = 0'),
        db.rawQuery(
            'SELECT COUNT(*) FROM payment_methods WHERE is_deleted = 0'),
        db.rawQuery('SELECT COUNT(*) FROM settings'),
        db.rawQuery('SELECT COUNT(*) FROM branches WHERE is_deleted = 0'),
        db.rawQuery('SELECT COUNT(*) FROM user_groups WHERE is_deleted = 0'),
      ]);

      countsStopwatch.stop();
      AppLogger.info(
          '⏱️ استغرق استعلام العد: ${countsStopwatch.elapsedMilliseconds} مللي ثانية');

      // استخراج الأعداد من نتائج الاستعلامات
      final usersCount = Sqflite.firstIntValue(counts[0]) ?? 0;
      final rolesCount = Sqflite.firstIntValue(counts[1]) ?? 0;
      final permissionsCount = Sqflite.firstIntValue(counts[2]) ?? 0;
      final rolePermissionsCount = Sqflite.firstIntValue(counts[3]) ?? 0;
      final unitsCount = Sqflite.firstIntValue(counts[4]) ?? 0;
      final warehouseCount = Sqflite.firstIntValue(counts[5]) ?? 0;
      final paymentMethodsCount = Sqflite.firstIntValue(counts[6]) ?? 0;
      final settingsCount = Sqflite.firstIntValue(counts[7]) ?? 0;
      final branchesCount = Sqflite.firstIntValue(counts[8]) ?? 0;
      final userGroupsCount = Sqflite.firstIntValue(counts[9]) ?? 0;

      // تجميع نتائج العد في مصفوفة للتحليل
      final dataStatus = [
        {
          'name': 'المصادقة والصلاحيات',
          'exists': rolesCount > 0 &&
              permissionsCount > 0 &&
              rolePermissionsCount > 0 &&
              userGroupsCount > 0 &&
              usersCount > 0,
          'repair': _initializeAuthData
        },
        {
          'name': 'وحدات القياس',
          'exists': unitsCount > 0,
          'repair': _initializeBasicUnits
        },
        {
          'name': 'المخازن',
          'exists': warehouseCount > 0,
          'repair': _initializeDefaultWarehouse
        },
        {
          'name': 'طرق الدفع',
          'exists': paymentMethodsCount > 0,
          'repair': _initializeBasicPaymentMethods
        },
        {
          'name': 'الإعدادات',
          'exists': settingsCount > 0,
          'repair': _initializeBasicSettings
        },
        {
          'name': 'الفروع',
          'exists': branchesCount > 0,
          'repair': _initializeDefaultBranch
        },
        {
          'name': 'العملات',
          'exists': await _checkCurrenciesExist(db),
          'repair': _initializeBasicCurrencies
        },
      ];

      // تحديد البيانات المفقودة
      final missingData =
          dataStatus.where((item) => item['exists'] == false).toList();

      if (missingData.isEmpty) {
        AppLogger.info('✅ لا توجد بيانات مفقودة تحتاج إلى إصلاح');
        stopwatch.stop();
        AppLogger.info(
            '⏱️ إجمالي وقت التحقق: ${stopwatch.elapsedMilliseconds} مللي ثانية');
        return;
      }

      // طباعة البيانات المفقودة
      AppLogger.info(
          '🔧 البيانات المفقودة التي سيتم إصلاحها (${missingData.length}):');
      for (final item in missingData) {
        AppLogger.info('   - ${item['name']}');
      }

      // إصلاح البيانات المفقودة
      final repairStopwatch = Stopwatch()..start();
      await db.transaction((txn) async {
        for (final item in missingData) {
          final itemStopwatch = Stopwatch()..start();
          AppLogger.info('🔧 إصلاح ${item['name']}...');

          // استدعاء دالة الإصلاح المناسبة
          final repairFunction =
              item['repair'] as Future<void> Function(Transaction);
          await repairFunction(txn);

          itemStopwatch.stop();
          AppLogger.info(
              '✅ تم إصلاح ${item['name']} في ${itemStopwatch.elapsedMilliseconds} مللي ثانية');
        }
      });
      repairStopwatch.stop();

      AppLogger.info(
          '⏱️ استغرق إصلاح البيانات المفقودة: ${repairStopwatch.elapsedMilliseconds} مللي ثانية');

      // التحقق من نجاح الإصلاح
      final verifyStopwatch = Stopwatch()..start();
      final dataInitialized = await _checkIfDataExists(db);
      verifyStopwatch.stop();

      if (dataInitialized) {
        AppLogger.info('✅ تم إصلاح جميع البيانات المفقودة بنجاح');
      } else {
        AppLogger.warning('⚠️ لا تزال بعض البيانات مفقودة بعد محاولة الإصلاح');
      }

      stopwatch.stop();
      AppLogger.info(
          '⏱️ إجمالي وقت عملية الإصلاح: ${stopwatch.elapsedMilliseconds} مللي ثانية');
    } catch (e, stackTrace) {
      AppLogger.error('❌ فشل في إصلاح البيانات الأساسية المفقودة: $e');
      ErrorTracker.captureError(
        'فشل في إصلاح البيانات الأساسية المفقودة',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'repair_missing_data'},
      );
    }
  }

  /// التحقق من وجود بيانات أساسية
  static Future<bool> _checkIfDataExists(Database db) async {
    try {
      AppLogger.info('🔍 جاري التحقق من وجود البيانات الأساسية...');

      // قياس وقت التنفيذ
      final stopwatch = Stopwatch()..start();

      // التحقق من وجود جداول أساسية أولاً
      final tablesStopwatch = Stopwatch()..start();
      final tables = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name IN ('users', 'roles', 'units', 'warehouses', 'payment_methods', 'settings', 'branches', 'user_groups')");
      tablesStopwatch.stop();

      final tablesFound = tables.length;
      AppLogger.info(
          '🔍 عدد الجداول الأساسية الموجودة: $tablesFound من أصل 8 (${tablesStopwatch.elapsedMilliseconds} مللي ثانية)');

      if (tablesFound < 8) {
        AppLogger.warning('⚠️ بعض الجداول الأساسية غير موجودة');
        stopwatch.stop();
        AppLogger.info(
            '⏱️ إجمالي وقت التحقق: ${stopwatch.elapsedMilliseconds} مللي ثانية');
        return false;
      }

      // استخدام استعلام واحد للتحقق من وجود البيانات الأساسية
      // هذا يحسن الأداء بشكل كبير عن طريق تقليل عدد الاستعلامات
      final countsStopwatch = Stopwatch()..start();

      // استعلام واحد للحصول على عدد السجلات في كل جدول
      final counts = await Future.wait([
        db.rawQuery('SELECT COUNT(*) FROM users WHERE is_deleted = 0'),
        db.rawQuery('SELECT COUNT(*) FROM roles WHERE is_deleted = 0'),
        db.rawQuery('SELECT COUNT(*) FROM permissions WHERE is_deleted = 0'),
        db.rawQuery(
            'SELECT COUNT(*) FROM role_permissions WHERE is_deleted = 0'),
        db.rawQuery('SELECT COUNT(*) FROM units WHERE is_deleted = 0'),
        db.rawQuery(
            'SELECT COUNT(*) FROM warehouses WHERE is_default = 1 AND is_deleted = 0'),
        db.rawQuery(
            'SELECT COUNT(*) FROM payment_methods WHERE is_deleted = 0'),
        db.rawQuery('SELECT COUNT(*) FROM settings'),
        db.rawQuery('SELECT COUNT(*) FROM branches WHERE is_deleted = 0'),
        db.rawQuery('SELECT COUNT(*) FROM user_groups WHERE is_deleted = 0'),
      ]);

      countsStopwatch.stop();
      AppLogger.info(
          '⏱️ استغرق استعلام العد: ${countsStopwatch.elapsedMilliseconds} مللي ثانية');

      // استخراج الأعداد من نتائج الاستعلامات
      final usersCount = Sqflite.firstIntValue(counts[0]) ?? 0;
      final rolesCount = Sqflite.firstIntValue(counts[1]) ?? 0;
      final permissionsCount = Sqflite.firstIntValue(counts[2]) ?? 0;
      final rolePermissionsCount = Sqflite.firstIntValue(counts[3]) ?? 0;
      final unitsCount = Sqflite.firstIntValue(counts[4]) ?? 0;
      final warehouseCount = Sqflite.firstIntValue(counts[5]) ?? 0;
      final paymentMethodsCount = Sqflite.firstIntValue(counts[6]) ?? 0;
      final settingsCount = Sqflite.firstIntValue(counts[7]) ?? 0;
      final branchesCount = Sqflite.firstIntValue(counts[8]) ?? 0;
      final userGroupsCount = Sqflite.firstIntValue(counts[9]) ?? 0;

      // تجميع نتائج العد في مصفوفة للتحليل
      final countResults = [
        {'name': 'المستخدمين', 'count': usersCount},
        {'name': 'الأدوار', 'count': rolesCount},
        {'name': 'الصلاحيات', 'count': permissionsCount},
        {'name': 'ربط الأدوار بالصلاحيات', 'count': rolePermissionsCount},
        {'name': 'وحدات القياس', 'count': unitsCount},
        {'name': 'المخازن الافتراضية', 'count': warehouseCount},
        {'name': 'طرق الدفع', 'count': paymentMethodsCount},
        {'name': 'الإعدادات', 'count': settingsCount},
        {'name': 'الفروع', 'count': branchesCount},
        {'name': 'مجموعات المستخدمين', 'count': userGroupsCount},
      ];

      // تحديد العناصر المفقودة
      final missingItems =
          countResults.where((item) => item['count'] == 0).toList();

      // طباعة ملخص العناصر
      if (missingItems.isEmpty) {
        AppLogger.info('✅ جميع العناصر الأساسية موجودة');
      } else {
        // التحقق إذا كانت جميع العناصر مفقودة (قاعدة بيانات جديدة)
        final allMissing = missingItems.length == countResults.length;

        if (allMissing) {
          AppLogger.info(
              'ℹ️ قاعدة بيانات جديدة: جميع العناصر الأساسية مفقودة (${missingItems.length})');
        } else {
          AppLogger.warning('⚠️ العناصر المفقودة (${missingItems.length}):');
        }

        for (final item in missingItems) {
          if (allMissing) {
            AppLogger.info('   - ${item['name']}');
          } else {
            AppLogger.warning('   - ${item['name']}');
          }
        }
      }

      // طباعة تفاصيل العد
      AppLogger.info('📊 تفاصيل العد:');
      for (final item in countResults) {
        AppLogger.info('   - ${item['name']}: ${item['count']}');
      }

      // نعتبر البيانات موجودة إذا كانت جميع العناصر الأساسية موجودة
      final allDataExists = missingItems.isEmpty;

      stopwatch.stop();
      AppLogger.info(
          '⏱️ إجمالي وقت التحقق: ${stopwatch.elapsedMilliseconds} مللي ثانية');

      if (allDataExists) {
        AppLogger.info('✅ جميع البيانات الأساسية موجودة');
      } else {
        // التحقق إذا كانت جميع العناصر مفقودة (قاعدة بيانات جديدة)
        final allMissing = missingItems.length == countResults.length;

        if (allMissing) {
          AppLogger.info(
              'ℹ️ قاعدة بيانات جديدة: تحتاج إلى تهيئة البيانات الأساسية');
        } else {
          AppLogger.warning(
              '⚠️ بعض البيانات الأساسية غير موجودة أو غير مكتملة');
        }
      }

      return allDataExists;
    } catch (e) {
      AppLogger.warning('⚠️ خطأ أثناء التحقق من وجود بيانات: $e');
      return false;
    }
  }

  /// تهيئة بيانات المصادقة والصلاحيات (واجهة عامة)
  static Future<void> initializeAuthData(Transaction txn) async {
    return _initializeAuthData(txn);
  }

  /// تهيئة بيانات المصادقة والصلاحيات (تنفيذ داخلي)
  static Future<void> _initializeAuthData(Transaction txn) async {
    AppLogger.info('🔄 بدء تهيئة بيانات المصادقة والصلاحيات...');

    try {
      // 1. تهيئة الصلاحيات والأدوار وربطها باستخدام PermissionInitializer الموحد
      await PermissionInitializer.initializeDatabase(txn);

      // استخراج معرفات الأدوار من PermissionInitializer
      _roleIds = Map.from(PermissionInitializer.getRoleIds());

      // 2. تهيئة مجموعات المستخدمين
      await _initializeUserGroups(txn);

      // 3. تهيئة المستخدم الافتراضي (مدير النظام)
      await _initializeDefaultAdmin(txn);

      AppLogger.info('✅ تم تهيئة بيانات المصادقة والصلاحيات بنجاح');
    } catch (e) {
      AppLogger.error('❌ فشل في تهيئة بيانات المصادقة والصلاحيات: $e');
      rethrow;
    }
  }

  /// تهيئة مجموعات المستخدمين
  static Future<void> _initializeUserGroups(Transaction txn) async {
    AppLogger.info('🔄 تهيئة مجموعات المستخدمين...');

    try {
      // إنشاء مجموعة المدراء
      final adminGroupId = const Uuid().v4();
      _adminGroupId = adminGroupId;

      // تخزين صلاحيات الدور في حقل permissions كـ JSON
      final adminPermissions = jsonEncode({
        'role_id': _roleIds['admin'],
        'permissions': RolesSchema.defaultRolePermissions['admin']
      });

      await txn.insert('user_groups', {
        'id': adminGroupId,
        'name': 'مدراء النظام',
        'description': 'مجموعة المستخدمين ذوي الصلاحيات الكاملة',
        'permissions': adminPermissions,
        'is_active': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'is_deleted': 0,
      });

      // إنشاء مجموعة المستخدمين العاديين
      final userPermissions = jsonEncode({
        'role_id': _roleIds['user'],
        'permissions': RolesSchema
            .defaultRolePermissions['viewer'] // استخدام دور المشاهد كبديل
      });

      await txn.insert('user_groups', {
        'id': const Uuid().v4(),
        'name': 'مستخدمون عاديون',
        'description': 'مجموعة المستخدمين ذوي الصلاحيات المحدودة',
        'permissions': userPermissions,
        'is_active': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'is_deleted': 0,
      });

      // إنشاء مجموعة المبيعات
      final salesPermissions = jsonEncode({
        'role_id': _roleIds['sales'],
        'permissions': RolesSchema
            .defaultRolePermissions['salesperson'] // استخدام دور مندوب المبيعات
      });

      await txn.insert('user_groups', {
        'id': const Uuid().v4(),
        'name': 'مسؤولو المبيعات',
        'description': 'مجموعة المستخدمين المسؤولين عن المبيعات',
        'permissions': salesPermissions,
        'is_active': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'is_deleted': 0,
      });

      // إنشاء مجموعة المخازن
      final inventoryPermissions = jsonEncode({
        'role_id': _roleIds['inventory'],
        'permissions': RolesSchema.defaultRolePermissions[
            'inventory_manager'] // استخدام دور مدير المخزون
      });

      await txn.insert('user_groups', {
        'id': const Uuid().v4(),
        'name': 'مسؤولو المخازن',
        'description': 'مجموعة المستخدمين المسؤولين عن المخازن',
        'permissions': inventoryPermissions,
        'is_active': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'is_deleted': 0,
      });

      AppLogger.info('✅ تم تهيئة مجموعات المستخدمين بنجاح');
    } catch (e) {
      AppLogger.error('❌ فشل في تهيئة مجموعات المستخدمين: $e');
      rethrow;
    }
  }

  /// تهيئة المستخدم الافتراضي (مدير النظام)
  static Future<void> _initializeDefaultAdmin(Transaction txn) async {
    AppLogger.info('🔄 تهيئة المستخدم الافتراضي (مدير النظام)...');

    try {
      // الحصول على معرف الفرع الافتراضي
      final branchResult = await txn.query(
        'branches',
        where: 'is_default = 1 AND is_deleted = 0',
        limit: 1,
      );

      String? defaultBranchId;
      String? defaultBranchName;

      if (branchResult.isNotEmpty) {
        defaultBranchId = branchResult.first['id'] as String?;
        defaultBranchName = branchResult.first['name'] as String?;
        AppLogger.info('✅ تم العثور على الفرع الافتراضي: $defaultBranchName');
      } else {
        AppLogger.info('ℹ️ لم يتم العثور على فرع افتراضي (سيتم إنشاؤه لاحقاً)');
      }

      // إنشاء كلمة مرور آمنة (admin123)
      const password = 'admin123';
      // تشفير كلمة المرور مباشرة بدون salt منفصل
      final hashedPassword = sha256.convert(utf8.encode(password)).toString();

      // إنشاء المستخدم الافتراضي مع ربط احترافي بالجداول الأخرى (بدون تكرار البيانات)
      final adminUserId = const Uuid().v4();
      await txn.insert('users', {
        'id': adminUserId,
        'username': 'admin',
        'email': '<EMAIL>',
        'password': hashedPassword,
        'full_name': 'مدير النظام',
        'phone': '0000000000',
        'user_group_id': _adminGroupId, // ربط بمجموعة المستخدمين
        'role_id': _roleIds['admin'], // ربط بالدور باستخدام ID
        'branch_id': defaultBranchId, // ربط بالفرع الافتراضي
        'is_active': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'created_by': 'system',
        'is_deleted': 0,
      });

      AppLogger.info('✅ تم تهيئة المستخدم الافتراضي بنجاح');
    } catch (e) {
      AppLogger.error('❌ فشل في تهيئة المستخدم الافتراضي: $e');
      rethrow;
    }
  }

  /// تهيئة وحدات القياس الأساسية (واجهة عامة)
  static Future<void> initializeBasicUnits(Transaction txn) async {
    return _initializeBasicUnits(txn);
  }

  /// تهيئة وحدات القياس الأساسية (تنفيذ داخلي)
  static Future<void> _initializeBasicUnits(Transaction txn) async {
    AppLogger.info('🔄 تهيئة وحدات القياس الأساسية...');

    try {
      // قائمة وحدات القياس الأساسية
      final units = [
        {
          'name': 'قطعة',
          'code': 'PC',
          'unit_type': 'count',
          'description': 'وحدة قياس للعناصر الفردية',
        },
        {
          'name': 'كيلوجرام',
          'code': 'KG',
          'unit_type': 'weight',
          'description': 'وحدة قياس للوزن',
        },
        {
          'name': 'لتر',
          'code': 'L',
          'unit_type': 'volume',
          'description': 'وحدة قياس للسوائل',
        },
        {
          'name': 'متر',
          'code': 'M',
          'unit_type': 'length',
          'description': 'وحدة قياس للطول',
        },
        {
          'name': 'صندوق',
          'code': 'BOX',
          'unit_type': 'count',
          'description': 'وحدة قياس للعناصر المجمعة',
        },
        {
          'name': 'جرام',
          'code': 'G',
          'unit_type': 'weight',
          'description': 'وحدة قياس للوزن',
        },
      ];

      // إدخال وحدات القياس
      for (final unit in units) {
        await txn.insert('units', {
          'id': const Uuid().v4(),
          'name': unit['name'],
          'code': unit['code'],
          'unit_type': unit['unit_type'],
          'description': unit['description'],
          'is_active': 1,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
          'is_deleted': 0,
        });
      }

      AppLogger.info('✅ تم تهيئة وحدات القياس الأساسية بنجاح');
    } catch (e) {
      AppLogger.error('❌ فشل في تهيئة وحدات القياس الأساسية: $e');
      rethrow;
    }
  }

  /// تهيئة المخزن الافتراضي (واجهة عامة)
  static Future<void> initializeDefaultWarehouse(Transaction txn) async {
    return _initializeDefaultWarehouse(txn);
  }

  /// تهيئة المخزن الافتراضي (تنفيذ داخلي)
  static Future<void> _initializeDefaultWarehouse(Transaction txn) async {
    AppLogger.info('🔄 تهيئة المخزن الافتراضي...');

    try {
      // الحصول على معرف الفرع الافتراضي للربط
      final branchResult = await txn.query(
        'branches',
        where: 'is_default = 1 AND is_deleted = 0',
        limit: 1,
      );

      String? defaultBranchId;
      if (branchResult.isNotEmpty) {
        defaultBranchId = branchResult.first['id'] as String?;
        AppLogger.info('✅ سيتم ربط المخزن بالفرع الافتراضي');
      }

      // إنشاء المخزن الافتراضي مع ربط احترافي
      final warehouseId = const Uuid().v4();
      await txn.insert('warehouses', {
        'id': warehouseId,
        'name': 'المخزن الرئيسي',
        'code': 'WH-MAIN-001',
        'description':
            'المخزن الرئيسي لشركة تاجر بلس - يحتوي على جميع المنتجات الأساسية',
        'location': 'الرياض، المملكة العربية السعودية',
        'address': 'شارع الملك فهد، الرياض',
        'phone': '+************',
        'email': '<EMAIL>',
        'manager_id': null, // سيتم ربطه بمدير المخزن لاحقاً
        'branch_id': defaultBranchId, // ربط بالفرع الافتراضي
        'capacity': 10000.0, // السعة بالمتر المكعب
        'current_usage': 0.0, // الاستخدام الحالي
        'is_default': 1,
        'is_active': 1,
        'created_at': DateTime.now().toIso8601String(),
        'created_by': 'system',
        'updated_at': DateTime.now().toIso8601String(),
        'is_deleted': 0,
      });

      AppLogger.info('✅ تم تهيئة المخزن الافتراضي بنجاح (ID: $warehouseId)');
    } catch (e) {
      AppLogger.error('❌ فشل في تهيئة المخزن الافتراضي: $e');
      rethrow;
    }
  }

  /// تهيئة طرق الدفع الأساسية (واجهة عامة)
  static Future<void> initializeBasicPaymentMethods(Transaction txn) async {
    return _initializeBasicPaymentMethods(txn);
  }

  /// تهيئة طرق الدفع الأساسية (تنفيذ داخلي)
  static Future<void> _initializeBasicPaymentMethods(Transaction txn) async {
    AppLogger.info('🔄 تهيئة طرق الدفع الأساسية...');

    try {
      // قائمة طرق الدفع الأساسية مع بيانات احترافية
      final paymentMethods = [
        {
          'name': 'نقدي',
          'code': 'CASH',
          'description': 'الدفع النقدي المباشر - العملة المحلية',
          'type': 'cash',
          'is_default': 1,
          'requires_approval': 0,
          'max_amount': 50000.0,
          'min_amount': 0.0,
          'processing_fee': 0.0,
          'currency': 'YER',
          'is_active': 1,
        },
        {
          'name': 'بطاقة ائتمان',
          'code': 'CREDIT_CARD',
          'description': 'الدفع بالبطاقة الائتمانية (فيزا، ماستركارد)',
          'type': 'card',
          'is_default': 0,
          'requires_approval': 0,
          'max_amount': 100000.0,
          'min_amount': 1.0,
          'processing_fee': 2.5,
          'currency': 'YER',
          'is_active': 1,
        },
        {
          'name': 'بطاقة مدى',
          'code': 'MADA',
          'description': 'الدفع ببطاقة مدى السعودية',
          'type': 'card',
          'is_default': 0,
          'requires_approval': 0,
          'max_amount': 50000.0,
          'min_amount': 1.0,
          'processing_fee': 1.0,
          'currency': 'YER',
          'is_active': 1,
        },
        {
          'name': 'تحويل بنكي',
          'code': 'BANK_TRANSFER',
          'description': 'التحويل البنكي المباشر',
          'type': 'transfer',
          'is_default': 0,
          'requires_approval': 1,
          'max_amount': 1000000.0,
          'min_amount': 100.0,
          'processing_fee': 5.0,
          'currency': 'YER',
          'is_active': 1,
        },
        {
          'name': 'شيك',
          'code': 'CHECK',
          'description': 'الدفع بالشيك البنكي',
          'type': 'check',
          'is_default': 0,
          'requires_approval': 1,
          'max_amount': 500000.0,
          'min_amount': 1000.0,
          'processing_fee': 10.0,
          'currency': 'YER',
          'is_active': 1,
        },
        {
          'name': 'محفظة إلكترونية',
          'code': 'E_WALLET',
          'description': 'الدفع عبر المحافظ الإلكترونية (STC Pay, Apple Pay)',
          'type': 'digital',
          'is_default': 0,
          'requires_approval': 0,
          'max_amount': 20000.0,
          'min_amount': 1.0,
          'processing_fee': 1.5,
          'currency': 'YER',
          'is_active': 1,
        },
      ];

      // إدخال طرق الدفع مع البيانات المحسنة
      for (final method in paymentMethods) {
        final paymentMethodId = const Uuid().v4();
        await txn.insert('payment_methods', {
          'id': paymentMethodId,
          'name': method['name'],
          'code': method['code'],
          'description': method['description'],
          'type': method['type'],
          'is_default': method['is_default'],
          'requires_approval': method['requires_approval'],
          'max_amount': method['max_amount'],
          'min_amount': method['min_amount'],
          'processing_fee': method['processing_fee'],
          'currency': method['currency'],
          'is_active': method['is_active'],
          'created_at': DateTime.now().toIso8601String(),
          'created_by': 'system',
          'updated_at': DateTime.now().toIso8601String(),
          'is_deleted': 0,
        });

        AppLogger.info(
            '✅ تم إنشاء طريقة دفع: ${method['name']} (ID: $paymentMethodId)');
      }

      AppLogger.info(
          '✅ تم تهيئة طرق الدفع الأساسية بنجاح (${paymentMethods.length} طريقة)');
    } catch (e) {
      AppLogger.error('❌ فشل في تهيئة طرق الدفع الأساسية: $e');
      rethrow;
    }
  }

  /// تهيئة إعدادات النظام الأساسية (واجهة عامة)
  static Future<void> initializeBasicSettings(Transaction txn) async {
    return _initializeBasicSettings(txn);
  }

  /// تهيئة إعدادات النظام الأساسية (تنفيذ داخلي)
  static Future<void> _initializeBasicSettings(Transaction txn) async {
    AppLogger.info('🔄 تهيئة إعدادات النظام الأساسية...');

    try {
      // قائمة الإعدادات الأساسية المحسنة
      final settings = [
        // إعدادات الشركة الأساسية
        {
          'key': 'company_name',
          'value': 'تاجر بلس',
          'description': 'اسم الشركة الرسمي',
          'category': 'company',
          'type': 'text',
          'is_required': 1,
        },
        {
          'key': 'company_logo',
          'value': '',
          'description': 'مسار شعار الشركة',
          'category': 'company',
          'type': 'file',
          'is_required': 0,
        },
        {
          'key': 'company_address',
          'value': 'المملكة العربية السعودية، الرياض',
          'description': 'عنوان الشركة الرئيسي',
          'category': 'company',
          'type': 'text',
          'is_required': 1,
        },
        {
          'key': 'company_phone',
          'value': '+************',
          'description': 'رقم هاتف الشركة',
          'category': 'company',
          'type': 'phone',
          'is_required': 1,
        },
        {
          'key': 'company_email',
          'value': '<EMAIL>',
          'description': 'البريد الإلكتروني للشركة',
          'category': 'company',
          'type': 'email',
          'is_required': 1,
        },
        {
          'key': 'company_website',
          'value': 'https://tajerplus.com',
          'description': 'موقع الشركة الإلكتروني',
          'category': 'company',
          'type': 'url',
          'is_required': 0,
        },
        {
          'key': 'company_tax_number',
          'value': '',
          'description': 'الرقم الضريبي للشركة',
          'category': 'company',
          'type': 'text',
          'is_required': 0,
        },
        {
          'key': 'company_commercial_register',
          'value': '',
          'description': 'رقم السجل التجاري',
          'category': 'company',
          'type': 'text',
          'is_required': 0,
        },

        // إعدادات العملة والضرائب
        {
          'key': 'currency_code',
          'value': 'YER',
          'description': 'رمز العملة الافتراضية',
          'category': 'financial',
          'type': 'select',
          'is_required': 1,
        },
        {
          'key': 'currency_name',
          'value': 'ريال يمني',
          'description': 'اسم العملة الافتراضية',
          'category': 'financial',
          'type': 'text',
          'is_required': 1,
        },
        {
          'key': 'currency_symbol',
          'value': 'ر.ي',
          'description': 'رمز العملة للعرض',
          'category': 'financial',
          'type': 'text',
          'is_required': 1,
        },
        {
          'key': 'tax_rate',
          'value': '15',
          'description': 'نسبة ضريبة القيمة المضافة (%)',
          'category': 'financial',
          'type': 'number',
          'is_required': 1,
        },
        {
          'key': 'tax_enabled',
          'value': 'true',
          'description': 'تفعيل حساب الضريبة',
          'category': 'financial',
          'type': 'boolean',
          'is_required': 1,
        },

        // إعدادات النظام
        {
          'key': 'language',
          'value': 'ar',
          'description': 'اللغة الافتراضية للنظام',
          'category': 'system',
          'type': 'select',
          'is_required': 1,
        },
        {
          'key': 'theme',
          'value': 'light',
          'description': 'السمة الافتراضية للواجهة',
          'category': 'system',
          'type': 'select',
          'is_required': 1,
        },
        {
          'key': 'timezone',
          'value': 'Asia/Riyadh',
          'description': 'المنطقة الزمنية',
          'category': 'system',
          'type': 'select',
          'is_required': 1,
        },
        {
          'key': 'date_format',
          'value': 'dd/MM/yyyy',
          'description': 'تنسيق التاريخ',
          'category': 'system',
          'type': 'select',
          'is_required': 1,
        },
        {
          'key': 'time_format',
          'value': '24',
          'description': 'تنسيق الوقت (12 أو 24 ساعة)',
          'category': 'system',
          'type': 'select',
          'is_required': 1,
        },

        // إعدادات التطبيق
        {
          'key': 'app_version',
          'value': '1.0.0',
          'description': 'إصدار التطبيق',
          'category': 'app',
          'type': 'text',
          'is_required': 1,
        },
        {
          'key': 'database_version',
          'value': '1.0.0',
          'description': 'إصدار قاعدة البيانات',
          'category': 'app',
          'type': 'text',
          'is_required': 1,
        },
        {
          'key': 'setup_completed',
          'value': 'false',
          'description': 'هل تم إكمال الإعداد الأولي',
          'category': 'app',
          'type': 'boolean',
          'is_required': 1,
        },
        {
          'key': 'first_run',
          'value': 'true',
          'description': 'هل هذا أول تشغيل للتطبيق',
          'category': 'app',
          'type': 'boolean',
          'is_required': 1,
        },

        // إعدادات الأمان
        {
          'key': 'password_min_length',
          'value': '6',
          'description': 'الحد الأدنى لطول كلمة المرور',
          'category': 'security',
          'type': 'number',
          'is_required': 1,
        },
        {
          'key': 'session_timeout',
          'value': '30',
          'description': 'مهلة انتهاء الجلسة (بالدقائق)',
          'category': 'security',
          'type': 'number',
          'is_required': 1,
        },
        {
          'key': 'auto_backup_enabled',
          'value': 'true',
          'description': 'تفعيل النسخ الاحتياطي التلقائي',
          'category': 'security',
          'type': 'boolean',
          'is_required': 1,
        },
        {
          'key': 'remember_login',
          'value': 'true',
          'description': 'تذكر تسجيل الدخول (عدم طلب تسجيل الدخول في كل مرة)',
          'category': 'security',
          'type': 'boolean',
          'is_required': 1,
        },
        {
          'key': 'auto_logout_enabled',
          'value': 'false',
          'description': 'تفعيل تسجيل الخروج التلقائي',
          'category': 'security',
          'type': 'boolean',
          'is_required': 1,
        },
        {
          'key': 'auto_logout_duration',
          'value': '60',
          'description': 'مدة عدم النشاط قبل تسجيل الخروج التلقائي (بالدقائق)',
          'category': 'security',
          'type': 'number',
          'is_required': 1,
        },
      ];

      // إدخال الإعدادات المحسنة
      for (final setting in settings) {
        final settingId = const Uuid().v4();
        await txn.insert('settings', {
          'id': settingId,
          'key': setting['key'],
          'value': setting['value'],
          'description': setting['description'],
          'category': setting['category'],
          'type': setting['type'],
          'is_required': setting['is_required'],
          'created_at': DateTime.now().toIso8601String(),
          'created_by': 'system',
          'updated_at': DateTime.now().toIso8601String(),
          'is_deleted': 0,
        });

        AppLogger.info(
            '✅ تم إنشاء إعداد: ${setting['key']} (${setting['category']})');
      }

      AppLogger.info(
          '✅ تم تهيئة إعدادات النظام الأساسية بنجاح (${settings.length} إعداد)');
    } catch (e) {
      AppLogger.error('❌ فشل في تهيئة إعدادات النظام الأساسية: $e');
      rethrow;
    }
  }

  /// تهيئة الفرع الافتراضي (واجهة عامة)
  static Future<void> initializeDefaultBranch(Transaction txn) async {
    return _initializeDefaultBranch(txn);
  }

  /// تهيئة الفرع الافتراضي (تنفيذ داخلي)
  static Future<void> _initializeDefaultBranch(Transaction txn) async {
    AppLogger.info('🔄 تهيئة الفرع الافتراضي...');

    try {
      // إنشاء الفرع الافتراضي مع بيانات احترافية
      final branchId = const Uuid().v4();
      await txn.insert('branches', {
        'id': branchId,
        'name': 'الفرع الرئيسي',
        'code': 'MAIN-001',
        'description': 'الفرع الرئيسي لشركة تاجر بلس',
        'address': 'المملكة العربية السعودية، الرياض',
        'phone': '+************',
        'email': '<EMAIL>',
        'city': 'الرياض',
        'state': 'منطقة الرياض',
        'country': 'المملكة العربية السعودية',
        'postal_code': '11564',
        'is_active': 1,
        'is_main': 1, // الفرع الرئيسي
        'is_default': 1, // الفرع الافتراضي
        'manager_id': null, // سيتم ربطه بالمدير لاحقاً
        'metadata':
            '{"timezone": "Asia/Aden", "currency": "YER", "language": "ar", "country": "Yemen"}',
        'created_at': DateTime.now().toIso8601String(),
        'created_by': 'system',
        'updated_at': DateTime.now().toIso8601String(),
        'is_deleted': 0,
      });

      AppLogger.info('✅ تم تهيئة الفرع الافتراضي بنجاح (ID: $branchId)');
    } catch (e) {
      AppLogger.error('❌ فشل في تهيئة الفرع الافتراضي: $e');
      rethrow;
    }
  }

  /// ربط المستخدم الافتراضي بالفرع الافتراضي (تنفيذ داخلي)
  static Future<void> _linkAdminUserToDefaultBranch(Transaction txn) async {
    AppLogger.info('🔗 ربط المستخدم الافتراضي بالفرع الافتراضي...');

    try {
      // الحصول على معرف الفرع الافتراضي
      final branchResult = await txn.query(
        'branches',
        where: 'is_default = 1 AND is_deleted = 0',
        limit: 1,
      );

      if (branchResult.isEmpty) {
        AppLogger.error(
            '❌ لم يتم العثور على فرع افتراضي للربط - هذا خطأ في تسلسل التهيئة');
        return;
      }

      final defaultBranchId = branchResult.first['id'] as String;
      final defaultBranchName = branchResult.first['name'] as String;

      // الحصول على المستخدم الافتراضي (admin)
      final userResult = await txn.query(
        'users',
        where: 'username = ? AND is_deleted = 0',
        whereArgs: ['admin'],
        limit: 1,
      );

      if (userResult.isEmpty) {
        AppLogger.error(
            '❌ لم يتم العثور على المستخدم الافتراضي للربط - هذا خطأ في تسلسل التهيئة');
        return;
      }

      final adminUserId = userResult.first['id'] as String;

      // تحديث المستخدم الافتراضي لربطه بالفرع (محسن - إزالة branch_name)
      await txn.update(
        'users',
        {
          'branch_id': defaultBranchId,
          'updated_at': DateTime.now().toIso8601String(),
          'updated_by': 'system',
        },
        where: 'id = ?',
        whereArgs: [adminUserId],
      );

      // تحديث الفرع لربطه بالمدير (بدون تكرار البيانات)
      await txn.update(
        'branches',
        {
          'manager_id': adminUserId,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [defaultBranchId],
      );

      // تحديث المخزن الافتراضي لربطه بالمدير أيضاً
      final warehouseResult = await txn.query(
        'warehouses',
        where: 'is_default = 1 AND is_deleted = 0',
        limit: 1,
      );

      if (warehouseResult.isNotEmpty) {
        final warehouseId = warehouseResult.first['id'] as String;
        await txn.update(
          'warehouses',
          {
            'manager_id': adminUserId,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [warehouseId],
        );
        AppLogger.info('✅ تم ربط المخزن الافتراضي بالمدير');
      }

      AppLogger.info('✅ تم ربط المستخدم الافتراضي بالفرع الافتراضي بنجاح');
      AppLogger.info('🔗 المستخدم: admin -> الفرع: $defaultBranchName');
    } catch (e) {
      AppLogger.error('❌ فشل في ربط المستخدم بالفرع: $e');
      rethrow;
    }
  }

  /// التحقق من وجود العملات الأساسية
  static Future<bool> _checkCurrenciesExist(Database db) async {
    try {
      final currenciesCount = await db.rawQuery(
          'SELECT COUNT(*) as count FROM currencies WHERE is_deleted = 0');
      final count = currenciesCount.first['count'] as int;
      return count >= 3; // نتوقع على الأقل 3 عملات أساسية
    } catch (e) {
      AppLogger.error('خطأ في التحقق من وجود العملات: $e');
      return false;
    }
  }

  /// تهيئة العملات الأساسية (واجهة عامة)
  static Future<void> initializeBasicCurrencies(Transaction txn) async {
    return _initializeBasicCurrencies(txn);
  }

  /// تهيئة العملات الأساسية (تنفيذ داخلي)
  static Future<void> _initializeBasicCurrencies(Transaction txn) async {
    AppLogger.info('🔄 تهيئة العملات الأساسية...');

    try {
      // قائمة العملات الأساسية مع بيانات شاملة
      final currencies = [
        {
          'name': 'ريال يمني',
          'code': 'YER',
          'symbol': 'ر.ي',
          'is_default': 1, // العملة الافتراضية
          'exchange_rate': 1.0,
          'country': 'اليمن',
          'country_code': 'YE',
          'decimal_places': 0, // الريال اليمني لا يحتوي على كسور عادة
          'description': 'الريال اليمني - العملة الرسمية لجمهورية اليمن',
          'is_active': 1,
        },
        {
          'name': 'ريال سعودي',
          'code': 'SAR',
          'symbol': 'ر.س',
          'is_default': 0,
          'exchange_rate': 0.133, // تقريباً 1 ريال يمني = 0.133 ريال سعودي
          'country': 'المملكة العربية السعودية',
          'country_code': 'SA',
          'decimal_places': 2,
          'description':
              'الريال السعودي - العملة الرسمية للمملكة العربية السعودية',
          'is_active': 1,
        },
        {
          'name': 'دولار أمريكي',
          'code': 'USD',
          'symbol': '\$',
          'is_default': 0,
          'exchange_rate': 0.004, // تقريباً 1 ريال يمني = 0.004 دولار أمريكي
          'country': 'الولايات المتحدة الأمريكية',
          'country_code': 'US',
          'decimal_places': 2,
          'description': 'الدولار الأمريكي - العملة الاحتياطية العالمية',
          'is_active': 1,
        },
        {
          'name': 'يورو',
          'code': 'EUR',
          'symbol': '€',
          'is_default': 0,
          'exchange_rate': 0.0037, // تقريباً 1 ريال يمني = 0.0037 يورو
          'country': 'الاتحاد الأوروبي',
          'country_code': 'EU',
          'decimal_places': 2,
          'description': 'اليورو - العملة الموحدة للاتحاد الأوروبي',
          'is_active': 1,
        },
        {
          'name': 'جنيه مصري',
          'code': 'EGP',
          'symbol': 'ج.م',
          'is_default': 0,
          'exchange_rate': 0.123, // تقريباً 1 ريال يمني = 0.123 جنيه مصري
          'country': 'جمهورية مصر العربية',
          'country_code': 'EG',
          'decimal_places': 2,
          'description': 'الجنيه المصري - العملة الرسمية لجمهورية مصر العربية',
          'is_active': 1,
        },
      ];

      // إدخال العملات مع البيانات المحسنة
      for (final currency in currencies) {
        final currencyId = const Uuid().v4();
        await txn.insert('currencies', {
          'id': currencyId,
          'name': currency['name'],
          'code': currency['code'],
          'symbol': currency['symbol'],
          'is_default': currency['is_default'],
          'exchange_rate': currency['exchange_rate'],
          'country': currency['country'],
          'country_code': currency['country_code'],
          'decimal_places': currency['decimal_places'],
          'description': currency['description'],
          'is_active': currency['is_active'],
          'created_at': DateTime.now().toIso8601String(),
          'created_by': 'system',
          'updated_at': DateTime.now().toIso8601String(),
          'is_deleted': 0,
        });

        AppLogger.info(
            '✅ تم إنشاء عملة: ${currency['name']} (${currency['code']}) - ID: $currencyId');
      }

      AppLogger.info(
          '✅ تم تهيئة العملات الأساسية بنجاح (${currencies.length} عملة)');
    } catch (e) {
      AppLogger.error('❌ فشل في تهيئة العملات الأساسية: $e');
      rethrow;
    }
  }
}
