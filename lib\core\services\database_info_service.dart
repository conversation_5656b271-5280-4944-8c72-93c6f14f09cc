import 'dart:io';
import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../utils/app_logger.dart';
import '../theme/index.dart';

/// خدمة معلومات قاعدة البيانات
/// توفر واجهة لعرض معلومات قاعدة البيانات للمستخدم
class DatabaseInfoService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على مسار قاعدة البيانات
  Future<String> getDatabasePath() async {
    return await _databaseHelper.getDatabasePath();
  }

  /// الحصول على حجم قاعدة البيانات
  Future<String> getDatabaseSize() async {
    try {
      final path = await _databaseHelper.getDatabasePath();
      final file = File(path);

      if (await file.exists()) {
        final size = await file.length();

        if (size < 1024) {
          return '$size بايت';
        } else if (size < 1024 * 1024) {
          return '${(size / 1024).toStringAsFixed(2)} كيلوبايت';
        } else {
          return '${(size / (1024 * 1024)).toStringAsFixed(2)} ميجابايت';
        }
      } else {
        return 'ملف قاعدة البيانات غير موجود';
      }
    } catch (e) {
      AppLogger.error('خطأ في الحصول على حجم قاعدة البيانات: $e');
      return 'غير متاح';
    }
  }

  /// الحصول على عدد الجداول في قاعدة البيانات
  Future<int> getTableCount() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
          "SELECT count(*) as count FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE 'android_%'");

      return result.first['count'] as int;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على عدد الجداول: $e');
      return -1;
    }
  }

  /// الحصول على قائمة الجداول في قاعدة البيانات
  Future<List<String>> getTableList() async {
    try {
      final db = await _databaseHelper.database;
      final result = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE 'android_%' ORDER BY name");

      return result.map((row) => row['name'] as String).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على قائمة الجداول: $e');
      return [];
    }
  }

  /// الحصول على عدد السجلات في جدول
  Future<int> getRowCount(String tableName) async {
    try {
      final db = await _databaseHelper.database;
      final result =
          await db.rawQuery('SELECT count(*) as count FROM $tableName');

      return result.first['count'] as int;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على عدد السجلات في جدول $tableName: $e');
      return -1;
    }
  }

  /// عرض حوار معلومات قاعدة البيانات
  Future<void> showDatabaseInfoDialog(BuildContext context) async {
    final path = await getDatabasePath();
    final size = await getDatabaseSize();
    final tableCount = await getTableCount();
    final tables = await getTableList();

    if (!context.mounted) return;

    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات قاعدة البيانات'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('مسار قاعدة البيانات:',
                  style: AppTypography(fontWeight: FontWeight.bold)),
              Text(path),
              const SizedBox(height: 8),
              const Text('حجم قاعدة البيانات:',
                  style: AppTypography(fontWeight: FontWeight.bold)),
              Text(size),
              const SizedBox(height: 8),
              const Text('عدد الجداول:',
                  style: AppTypography(fontWeight: FontWeight.bold)),
              Text('$tableCount'),
              const SizedBox(height: 8),
              const Text('قائمة الجداول:',
                  style: AppTypography(fontWeight: FontWeight.bold)),
              ...tables.map((table) => FutureBuilder<int>(
                    future: getRowCount(table),
                    builder: (context, snapshot) {
                      final rowCount = snapshot.data ?? 0;
                      return Padding(
                        padding: const EdgeInsets.only(left: 16.0),
                        child: Text('$table ($rowCount سجل)'),
                      );
                    },
                  )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
