include: package:lints/recommended.yaml

analyzer:
  exclude:
    - build/**
    - example/**
    - lib/generated_plugin_registrant.dart
#   language:
#     strict-raw-types: true
#   strong-mode:
#     implicit-casts: false

linter:
  rules:
    # - cancel_subscriptions
    - comment_references
    - slash_for_doc_comments
    - use_key_in_widget_constructors
    - unnecessary_statements
    - throw_in_finally
    - always_use_package_imports
    - avoid_dynamic_calls
    - avoid_empty_else
    # - avoid_print
    - avoid_relative_lib_imports
    - avoid_slow_async_io
    - avoid_type_to_string
    - avoid_types_as_parameter_names
    - avoid_web_libraries_in_flutter
    - cancel_subscriptions
    - close_sinks
    - control_flow_in_finally
    - diagnostic_describe_all_properties
    - empty_statements
    - hash_and_equals
    - collection_methods_unrelated_type
    - literal_only_boolean_expressions
    - no_adjacent_strings_in_list
    - no_duplicate_case_values
    - no_logic_in_create_state
    - prefer_void_to_null
    - test_types_in_equals
