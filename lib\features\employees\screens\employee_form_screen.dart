import 'package:flutter/material.dart';
import 'package:provider/provider.dart';


import 'package:tajer_plus/core/utils/index.dart';
import 'package:tajer_plus/core/widgets/custom_button.dart';
import 'package:tajer_plus/core/widgets/date_picker_field_compat.dart'
    as date_picker;
import 'package:tajer_plus/core/widgets/form_fields.dart';
import 'package:tajer_plus/core/models/employee.dart';
import '../../../core/constants/app_constants.dart';
import '../../accounts/presenters/account_presenter.dart'; // إضافة مقدم الحسابات
import '../../../core/theme/index.dart';

/// شاشة إضافة أو تعديل موظف
class EmployeeFormScreen extends StatefulWidget {
  final Employee? employee;

  const EmployeeFormScreen({Key? key, this.employee}) : super(key: key);

  @override
  State<EmployeeFormScreen> createState() => _EmployeeFormScreenState();
}

class _EmployeeFormScreenState extends State<EmployeeFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _employeeNumberController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _positionController = TextEditingController();
  final _departmentController = TextEditingController();
  final _salaryController = TextEditingController();
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();

  DateTime _hireDate = DateTime.now();
  String _status = 'active';
  bool _isLoading = false;

  // متغيرات الحساب
  late AccountPresenter _accountPresenter;
  String? _selectedAccountId; // معرف الحساب المحدد
  List<Map<String, dynamic>> _accounts = []; // قائمة الحسابات
  bool _isLoadingAccounts = false; // حالة تحميل الحسابات

  @override
  void initState() {
    super.initState();
    _accountPresenter = Provider.of<AccountPresenter>(context, listen: false);

    if (widget.employee != null) {
      _initializeFormWithEmployeeData();
    } else {
      // توليد رقم موظف جديد
      _generateNewEmployeeNumber();
    }

    // تحميل الحسابات
    _loadAccounts();
  }

  // دالة لتحميل الحسابات من شجرة الحسابات
  Future<void> _loadAccounts() async {
    setState(() {
      _isLoadingAccounts = true;
    });

    try {
      // تحميل الحسابات من مقدم الحسابات
      await _accountPresenter.loadAccounts();

      setState(() {
        // الحصول على الحسابات من نوع "expense" (مصروفات) أو "salary" (رواتب)
        _accounts = _accountPresenter.accounts.where((account) {
          final type = account['type']?.toString().toLowerCase() ?? '';
          final subcategory =
              account['subcategory']?.toString().toLowerCase() ?? '';
          return type == 'expense' || subcategory == 'salary';
        }).toList();

        _isLoadingAccounts = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingAccounts = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحميل الحسابات: ${e.toString()}'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _employeeNumberController.dispose();
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _positionController.dispose();
    _departmentController.dispose();
    _salaryController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  /// تعبئة النموذج ببيانات الموظف الحالي للتعديل
  void _initializeFormWithEmployeeData() {
    final employee = widget.employee!;
    _employeeNumberController.text = employee.employeeId;
    _fullNameController.text = employee.fullName;
    _emailController.text = employee.email ?? '';
    _phoneController.text = employee.phone ?? '';
    _positionController.text = employee.position ?? '';
    _departmentController.text = employee.department ?? '';
    _salaryController.text = employee.basicSalary?.toString() ?? '0';
    _addressController.text = employee.address ?? '';
    _notesController.text = employee.metadata?['notes'] ?? '';
    _hireDate = employee.hireDate ?? DateTime.now();
    _status = employee.status;
    _selectedAccountId = employee.metadata?['accountId']; // تعيين معرف الحساب
  }

  /// توليد رقم موظف جديد
  void _generateNewEmployeeNumber() {
    // هنا يمكن استدعاء خدمة لتوليد رقم موظف جديد
    // مثال بسيط:
    final now = DateTime.now();
    final year = now.year.toString().substring(2);
    final month = now.month.toString().padLeft(2, '0');
    final random = (1000 + now.millisecond).toString().substring(1);
    _employeeNumberController.text = 'EMP$year$month$random';
  }

  /// حفظ بيانات الموظف
  Future<void> _saveEmployee() async {
    if (!_formKey.currentState!.validate()) return;

    // التحقق من اختيار حساب
    if (_selectedAccountId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('الرجاء اختيار حساب للموظف'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // تجهيز بيانات الموظف
      final nameParts = _fullNameController.text.split(' ');
      final firstName = nameParts.isNotEmpty ? nameParts.first : '';
      final lastName = nameParts.length > 1 ? nameParts.last : '';

      final employee = Employee(
        id: widget.employee?.id,
        employeeId: _employeeNumberController.text,
        fullName: _fullNameController.text,
        firstName: firstName,
        lastName: lastName,
        email: _emailController.text,
        phone: _phoneController.text,
        position: _positionController.text,
        department: _departmentController.text,
        basicSalary: double.tryParse(_salaryController.text) ?? 0,
        hireDate: _hireDate,
        address: _addressController.text,
        metadata: {
          'notes': _notesController.text,
          'accountId': _selectedAccountId,
        },
        status: _status,
        createdAt: widget.employee?.createdAt,
        updatedAt: DateTime.now(),
      );

      // هنا سيتم استدعاء خدمة حفظ بيانات الموظف
      // مثال:
      // final employeeService = EmployeeService();
      // if (widget.employee == null) {
      //   await employeeService.addEmployee(employee);
      // } else {
      //   await employeeService.updateEmployee(employee);
      // }

      // محاكاة تأخير الحفظ
      await Future.delayed(const Duration(seconds: 1));

      // استخدام المتغير employee لتجنب تحذير "المتغير غير مستخدم"
      debugPrint('تم حفظ بيانات الموظف: ${employee.fullName}');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.employee == null
                  ? 'تم إضافة الموظف بنجاح'
                  : 'تم تحديث بيانات الموظف بنجاح',
            ),
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.employee == null
            ? 'إضافة موظف جديد'
            : 'تعديل بيانات الموظف'),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Layout.isDesktop() || Layout.isTablet()
            ? _buildDesktopLayout()
            : _buildMobileLayout(),
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'المعلومات الأساسية',
                        style: AppTypography(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),
                      _buildBasicInfoFields(),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معلومات العمل',
                        style: AppTypography(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),
                      _buildWorkInfoFields(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'معلومات إضافية',
                  style:
                      AppTypography(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                _buildAdditionalInfoFields(),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'المعلومات الأساسية',
                  style:
                      AppTypography(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                _buildBasicInfoFields(),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'معلومات العمل',
                  style:
                      AppTypography(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                _buildWorkInfoFields(),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'معلومات إضافية',
                  style:
                      AppTypography(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                _buildAdditionalInfoFields(),
              ],
            ),
          ),
        ),
        const SizedBox(height: 24),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildBasicInfoFields() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _employeeNumberController,
                decoration: const InputDecoration(
                  labelText: 'رقم الموظف *',
                  prefixIcon: Icon(Icons.badge),
                ),
                validator: Validators.required('رقم الموظف'),
                readOnly: widget.employee !=
                    null, // لا يمكن تعديل رقم الموظف بعد إنشائه
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _fullNameController,
          decoration: const InputDecoration(
            labelText: 'الاسم الكامل *',
            prefixIcon: Icon(Icons.person),
          ),
          validator: Validators.required('اسم الموظف'),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  prefixIcon: Icon(Icons.email),
                ),
                validator: Validators.email(),
                keyboardType: TextInputType.emailAddress,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف',
                  prefixIcon: Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildWorkInfoFields() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _positionController,
                decoration: const InputDecoration(
                  labelText: 'المنصب الوظيفي *',
                  prefixIcon: Icon(Icons.work),
                ),
                validator: Validators.required('المنصب الوظيفي'),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _departmentController,
                decoration: const InputDecoration(
                  labelText: 'القسم *',
                  prefixIcon: Icon(Icons.business),
                ),
                validator: Validators.required('القسم'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _salaryController,
                decoration: const InputDecoration(
                  labelText: 'الراتب الأساسي *',
                  prefixIcon: Icon(Icons.attach_money),
                  suffixText: AppConstants.appCurrency,
                ),
                validator: Validators.required('الراتب الأساسي'),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: date_picker.DatePickerField(
                label: 'تاريخ التعيين *',
                initialDate: _hireDate,
                onDateSelected: (date) {
                  setState(() {
                    _hireDate = date;
                  });
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // حقل اختيار الحساب من شجرة الحسابات
        _buildAccountSelector(),
        const SizedBox(height: 16),

        DropdownField<String>(
          label: 'حالة الموظف',
          value: _status,
          items: const [
            DropdownMenuItem(value: 'active', child: Text('نشط')),
            DropdownMenuItem(value: 'inactive', child: Text('غير نشط')),
            DropdownMenuItem(value: 'on_leave', child: Text('في إجازة')),
            DropdownMenuItem(value: 'terminated', child: Text('منتهي')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _status = value;
              });
            }
          },
        ),
      ],
    );
  }

  // دالة لبناء حقل اختيار الحساب
  Widget _buildAccountSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'الحساب المرتبط *',
              style: AppTypography(fontWeight: FontWeight.bold),
            ),
            const SizedBox(width: 8),
            if (_isLoadingAccounts)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String?>(
          value: _selectedAccountId,
          decoration: const InputDecoration(
            hintText: 'اختر حساب',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          ),
          items: _accounts.map((account) {
            final accountName = account['name'] as String;
            final accountCode = account['code'] as String?;
            final accountType = account['type'] as String?;

            return DropdownMenuItem<String?>(
              value: account['id'].toString(),
              child: Row(
                children: [
                  Icon(
                    _getAccountIcon(accountType),
                    size: 16,
                    color: _getAccountColor(accountType),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      accountName,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (accountCode != null) ...[
                    const SizedBox(width: 8),
                    Text(
                      '#$accountCode',
                      style: const AppTypography(
                        fontSize: 12,
                        color: AppColors.lightTextSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedAccountId = value;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'الرجاء اختيار حساب';
            }
            return null;
          },
          isExpanded: true,
        ),
        const SizedBox(height: 4),
        TextButton.icon(
          onPressed: () {
            // هنا يمكن إضافة الانتقال إلى شاشة إضافة حساب جديد
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('الانتقال إلى شاشة إضافة حساب جديد'),
              ),
            );
          },
          icon: const Icon(Icons.add, size: 16),
          label: const Text('إضافة حساب جديد'),
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: const Size(0, 32),
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            alignment: Alignment.centerLeft,
          ),
        ),
      ],
    );
  }

  // دالة للحصول على أيقونة الحساب حسب نوعه
  IconData _getAccountIcon(String? accountType) {
    switch (accountType?.toLowerCase()) {
      case 'expense':
        return Icons.money_off;
      case 'salary':
        return Icons.payments;
      default:
        return Icons.account_circle;
    }
  }

  // دالة للحصول على لون الحساب حسب نوعه
  Color _getAccountColor(String? accountType) {
    switch (accountType?.toLowerCase()) {
      case 'expense':
        return AppColors.error;
      case 'salary':
        return AppColors.info;
      default:
        return AppColors.lightTextSecondary;
    }
  }

  Widget _buildAdditionalInfoFields() {
    return Column(
      children: [
        TextFormField(
          controller: _addressController,
          decoration: const InputDecoration(
            labelText: 'العنوان',
            prefixIcon: Icon(Icons.location_on),
          ),
          maxLines: 2,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'ملاحظات',
            prefixIcon: Icon(Icons.note),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        CustomButton(
          text: 'إلغاء',
          icon: Icons.cancel,
          // color: AppColors.lightTextSecondary,
          backgroundColor: AppColors.lightTextSecondary,
          onPressed: () => Navigator.pop(context),
        ),
        const SizedBox(width: 16),
        CustomButton(
          text: widget.employee == null ? 'إضافة' : 'حفظ التغييرات',
          icon: Icons.save,
          isLoading: _isLoading,
          onPressed: _saveEmployee,
        ),
      ],
    );
  }
}
