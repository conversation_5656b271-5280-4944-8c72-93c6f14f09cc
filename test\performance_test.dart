import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/providers/app_providers.dart';
import 'package:tajer_plus/core/database/database_optimizer.dart';
import 'package:tajer_plus/core/utils/app_logger.dart';

void main() {
  group('🚀 اختبارات الأداء والتحسين', () {
    test('اختبار أداء تحميل الـ Providers', () async {
      final stopwatch = Stopwatch()..start();

      // تحميل الـ providers الأساسية فقط
      final coreProviders = AppProviders.getCoreProviders();

      stopwatch.stop();

      // يجب أن يكون التحميل سريع (أقل من 100ms)
      expect(stopwatch.elapsedMilliseconds, lessThan(100));

      // يجب أن يكون عدد الـ providers الأساسية قليل
      expect(coreProviders.length, lessThan(5));

      AppLogger.info(
          '⏱️ وقت تحميل الـ Providers الأساسية: ${stopwatch.elapsedMilliseconds}ms');
      AppLogger.info('📊 عدد الـ Providers الأساسية: ${coreProviders.length}');
    });

    test('اختبار التحميل الكسول للـ Presenters', () {
      // تنظيف أي instances سابقة
      LazyPresenterManager.clearAll();

      // التحقق من أن لا توجد presenters محملة
      expect(LazyPresenterManager.loadedCount, equals(0));

      // محاكاة تحميل presenter
      final presenter1 =
          LazyPresenterManager.getOrCreate<String>(() => 'test_presenter_1');
      expect(LazyPresenterManager.loadedCount, equals(1));
      expect(presenter1, equals('test_presenter_1'));

      // التحقق من أن نفس الـ instance يتم إرجاعه
      final presenter2 =
          LazyPresenterManager.getOrCreate<String>(() => 'test_presenter_2');
      expect(presenter2, equals('test_presenter_1')); // نفس الـ instance
      expect(LazyPresenterManager.loadedCount, equals(1)); // لم يزد العدد

      // تحميل presenter مختلف
      final presenter3 = LazyPresenterManager.getOrCreate<int>(() => 42);
      expect(LazyPresenterManager.loadedCount, equals(2));
      expect(presenter3, equals(42));

      // التحقق من الإحصائيات
      final stats = LazyPresenterManager.getMemoryStats();
      expect(stats['loaded_presenters'], equals(2));
      expect(stats['types'], hasLength(2));

      AppLogger.info('📊 إحصائيات الذاكرة: $stats');

      // تنظيف
      LazyPresenterManager.clearAll();
      expect(LazyPresenterManager.loadedCount, equals(0));
    });

    test('اختبار أداء استهلاك الذاكرة', () {
      LazyPresenterManager.clearAll();

      // محاكاة تحميل عدة presenters
      final presenters = <String>[];
      final stopwatch = Stopwatch()..start();

      for (int i = 0; i < 10; i++) {
        final presenter = LazyPresenterManager.getOrCreate<String>(
          () => 'presenter_$i',
        );
        presenters.add(presenter);
      }

      stopwatch.stop();

      // التحقق من الأداء
      expect(stopwatch.elapsedMilliseconds, lessThan(50)); // يجب أن يكون سريع
      expect(LazyPresenterManager.loadedCount, equals(1)); // نفس النوع

      // التحقق من استهلاك الذاكرة
      final stats = LazyPresenterManager.getMemoryStats();
      final memoryUsage = stats['memory_usage_estimate'] as String;
      expect(memoryUsage, contains('KB'));

      AppLogger.info(
          '⏱️ وقت تحميل 10 presenters: ${stopwatch.elapsedMilliseconds}ms');
      AppLogger.info('💾 استهلاك الذاكرة المقدر: $memoryUsage');

      LazyPresenterManager.clearAll();
    });

    test('اختبار مقارنة الأداء: عادي vs كسول', () {
      // اختبار التحميل العادي
      final stopwatch1 = Stopwatch()..start();
      final normalProviders = AppProviders.getAllProviders();
      stopwatch1.stop();

      // اختبار التحميل الأساسي فقط
      final stopwatch2 = Stopwatch()..start();
      final minimalProviders = AppProviders.getMinimalProviders();
      stopwatch2.stop();

      // التحميل الأساسي يجب أن يكون أسرع
      expect(stopwatch2.elapsedMilliseconds,
          lessThanOrEqualTo(stopwatch1.elapsedMilliseconds));

      // التحميل الأساسي يجب أن يحتوي على providers أقل
      expect(minimalProviders.length, lessThan(normalProviders.length));

      AppLogger.info(
          '⏱️ التحميل الكامل: ${stopwatch1.elapsedMilliseconds}ms (${normalProviders.length} providers)');
      AppLogger.info(
          '⏱️ التحميل الأساسي: ${stopwatch2.elapsedMilliseconds}ms (${minimalProviders.length} providers)');

      final improvement =
          ((stopwatch1.elapsedMilliseconds - stopwatch2.elapsedMilliseconds) /
                  stopwatch1.elapsedMilliseconds *
                  100)
              .round();
      AppLogger.info('📈 تحسن الأداء: $improvement%');
    });

    test('اختبار تنظيف الموارد', () {
      LazyPresenterManager.clearAll();

      // إنشاء عدة presenters
      for (int i = 0; i < 5; i++) {
        LazyPresenterManager.getOrCreate<String>(() => 'test_$i');
      }

      expect(LazyPresenterManager.loadedCount, equals(1)); // نفس النوع

      // تنظيف presenter محدد
      LazyPresenterManager.clear<String>();
      expect(LazyPresenterManager.loadedCount, equals(0));

      // إنشاء presenters من أنواع مختلفة
      LazyPresenterManager.getOrCreate<String>(() => 'string_presenter');
      LazyPresenterManager.getOrCreate<int>(() => 42);
      LazyPresenterManager.getOrCreate<bool>(() => true);

      expect(LazyPresenterManager.loadedCount, equals(3));

      // تنظيف الكل
      LazyPresenterManager.clearAll();
      expect(LazyPresenterManager.loadedCount, equals(0));
    });

    test('اختبار معلومات قاعدة البيانات', () async {
      try {
        final dbInfo = await DatabaseOptimizer.getDatabaseInfo();

        // التحقق من وجود المعلومات الأساسية
        expect(dbInfo, containsPair('version', isA<int>()));
        expect(dbInfo, containsPair('tables', isA<Map>()));
        expect(dbInfo, containsPair('total_rows', isA<int>()));

        AppLogger.info('📊 معلومات قاعدة البيانات:');
        AppLogger.info('   - الإصدار: ${dbInfo['version']}');
        AppLogger.info('   - إجمالي الصفوف: ${dbInfo['total_rows']}');
        AppLogger.info('   - الحجم المقدر: ${dbInfo['total_size_estimate']}');

        final tables = dbInfo['tables'] as Map<String, dynamic>;
        AppLogger.info('   - الجداول:');
        tables.forEach((table, count) {
          AppLogger.info('     * $table: $count صف');
        });
      } catch (e) {
        // في بيئة الاختبار، قد لا تكون قاعدة البيانات متاحة
        AppLogger.warning(
            '⚠️ لا يمكن الوصول لقاعدة البيانات في بيئة الاختبار: $e');
      }
    });

    test('اختبار حدود الأداء', () {
      LazyPresenterManager.clearAll();

      final stopwatch = Stopwatch()..start();

      // محاكاة تحميل عدد كبير من الـ presenters
      for (int i = 0; i < 1000; i++) {
        LazyPresenterManager.getOrCreate<String>(() => 'presenter_$i');
      }

      stopwatch.stop();

      // يجب أن يكون الأداء مقبول حتى مع عدد كبير من الطلبات
      expect(stopwatch.elapsedMilliseconds, lessThan(1000)); // أقل من ثانية
      expect(LazyPresenterManager.loadedCount, equals(1)); // نفس النوع

      AppLogger.info(
          '⏱️ وقت تحميل 1000 طلب: ${stopwatch.elapsedMilliseconds}ms');
      AppLogger.info(
          '💾 عدد الـ instances الفعلية: ${LazyPresenterManager.loadedCount}');

      LazyPresenterManager.clearAll();
    });

    test('اختبار الأمان من التحميل المتزامن', () async {
      LazyPresenterManager.clearAll();

      // محاكاة طلبات متزامنة
      final futures = <Future<String>>[];

      for (int i = 0; i < 10; i++) {
        futures.add(Future.microtask(() =>
            LazyPresenterManager.getOrCreate<String>(
                () => 'concurrent_presenter')));
      }

      final results = await Future.wait(futures);

      // جميع النتائج يجب أن تكون نفس الـ instance
      for (final result in results) {
        expect(result, equals('concurrent_presenter'));
      }

      // يجب أن يكون هناك instance واحد فقط
      expect(LazyPresenterManager.loadedCount, equals(1));

      AppLogger.info(
          '✅ اختبار التحميل المتزامن نجح - instance واحد فقط تم إنشاؤه');

      LazyPresenterManager.clearAll();
    });
  });
}
