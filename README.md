# تاجر بلس (Tajer Plus)

نظام محاسبي متكامل مبني بتقنية Flutter لإدارة الأعمال التجارية الصغيرة والمتوسطة.

## نظرة عامة

تاجر بلس هو نظام محاسبي شامل يوفر حلاً متكاملاً لإدارة الأعمال التجارية، بما في ذلك:

- إدارة المحاسبة والحسابات
- إدارة المخزون والمنتجات
- إدارة المبيعات والمشتريات
- إدارة العملاء والموردين
- إدارة الموارد البشرية
- إدارة الأصول الثابتة
- التقارير المالية والإدارية

## المميزات الرئيسية

- واجهة مستخدم سهلة الاستخدام ومتوافقة مع الأجهزة المختلفة
- دعم اللغة العربية والإنجليزية
- إمكانية العمل بدون اتصال بالإنترنت
- نظام صلاحيات متقدم
- تقارير متنوعة وقابلة للتخصيص
- دعم الطباعة والتصدير بتنسيقات مختلفة
- نظام نسخ احتياطي واستعادة البيانات

## التحديثات الأخيرة

### تحسين إدارة المخزون (الإصدار 2.1.0)

تم إجراء تحسينات كبيرة على نظام إدارة المخزون لتحسين الأداء وتبسيط الاستخدام:

1. **فصل مقدمي المخزون والمستودعات**: تم فصل وظائف المخزون عن وظائف المستودعات من خلال نقل وظائف المخزون من `WarehousePresenter` إلى `InventoryPresenter`.
2. **تحسين أداء استعلامات المخزون**: تم إضافة فهارس للجداول المتعلقة بالمخزون لتحسين أداء الاستعلامات.
3. **تحسين واجهة المستخدم**: تم تحديث شاشات المخزون لاستخدام المقدم المناسب لكل وظيفة.
4. **إضافة اختبارات وحدة**: تم إضافة اختبارات وحدة للتأكد من صحة وظائف المخزون الجديدة.

### تحسين هيكل قاعدة البيانات (الإصدار 2.0.0)

تم إجراء تحسينات كبيرة على هيكل قاعدة البيانات لتحسين الأداء وتبسيط الاستخدام:

1. **توحيد جداول الحسابات**: تم دمج جدول `account_chart` في جدول `accounts` الموحد.
2. **توحيد جداول الفئات**: تم دمج جدول `product_categories` في جدول `categories` الموحد.
3. **توحيد جداول الوحدات**: تم دمج جدول `product_units` في جدول `units` الموحد.
4. **إزالة جداول الربط الزائدة**: تم إزالة جداول مثل `customer_account_mappings` و `supplier_account_mappings` و `product_account_mappings` وإضافة حقول مباشرة في الجداول الرئيسية.
5. **إضافة فهارس للأداء**: تم إضافة فهارس للحقول التي يتم البحث عنها بشكل متكرر لتحسين أداء الاستعلامات.

للمزيد من المعلومات حول هذه التحديثات، يرجى الاطلاع على:

- [توثيق هيكل قاعدة البيانات](docs/database_structure.md)
- [دليل المستخدم للجداول الموحدة](docs/user_guide_unified_tables.md)
- [توثيق نظام إدارة المخزون](docs/inventory_management.md)

## البدء بالاستخدام

### متطلبات النظام

- Flutter SDK (الإصدار 3.0.0 أو أحدث)
- Dart SDK (الإصدار 2.17.0 أو أحدث)
- Android Studio / VS Code
- للتطوير: جهاز Android أو iOS أو محاكي

### التثبيت

1. استنساخ المستودع:

   ```bash
   git clone https://github.com/yourusername/tajer_plus.git
   ```

2. تثبيت التبعيات:

   ```bash
   cd tajer_plus
   flutter pub get
   ```

3. تشغيل التطبيق:

   ```bash
   flutter run
   ```

## الوثائق

- [دليل المستخدم](docs/user_guide.md)
- [دليل المطور](docs/developer_guide.md)
- [توثيق واجهة برمجة التطبيقات](docs/api_documentation.md)

## المساهمة

نرحب بمساهماتكم في تطوير هذا المشروع. يرجى الاطلاع على [دليل المساهمة](CONTRIBUTING.md) للحصول على مزيد من المعلومات.

## الترخيص

هذا المشروع مرخص بموجب [رخصة MIT](LICENSE).
