import 'package:flutter/material.dart';

/// نظام الألوان الموحد لتطبيق تاجر بلس
/// يحتوي على جميع الألوان المستخدمة في الوضعين الفاتح والداكن
///
/// 🎨 الميزات:
/// - 8 ثيمات لونية مختلفة للاختيار
/// - دعم كامل للوضع الداكن والفاتح
/// - ألوان متخصصة لكل وحدة في النظام
/// - دوال مساعدة ذكية للحصول على الألوان المناسبة
/// - نظام تدرجات وظلال جميل
/// - ألوان متكيفة حسب السياق
class AppColors {
  // منع إنشاء كائن من هذا الكلاس
  AppColors._();

  // ========== الألوان الأساسية المحدثة ==========

  /// اللون الأساسي للتطبيق - أحمر تاجر بلس محدث وعصري
  /// 🔴 استخدم في: الأزرار الرئيسية، شريط التطبيق، العناصر المهمة
  /// مثال: ElevatedButton, AppBar, FloatingActionButton
  static const Color primary = Color(0xFFE53E3E); // أحمر أكثر حيوية وعصرية

  /// نسخة داكنة من اللون الأساسي - محسنة للتباين
  /// 🔴 استخدم في: حالة الضغط على الأزرار، الظلال الملونة
  static const Color primaryDark = Color(0xFFC53030); // أحمر داكن متوازن

  /// نسخة فاتحة من اللون الأساسي - ناعمة وجذابة
  /// 🔴 استخدم في: خلفيات فاتحة، حدود ملونة، تأثيرات بصرية
  static const Color primaryLight = Color(0xFFFEB2B2); // أحمر فاتح أنيق

  /// اللون الثانوي - رمادي أنيق ومتطور
  /// ⚫ استخدم في: النصوص الرئيسية، العناوين، الحدود الداكنة
  static const Color secondary = Color(0xFF2D3748); // رمادي مزرق عصري
  static const Color secondaryDark = Color(0xFF1A202C); // رمادي داكن عميق
  static const Color secondaryLight = Color(0xFF4A5568); // رمادي فاتح متوازن

  /// اللون المميز - بنفسجي محدث وجذاب
  /// 🟣 استخدم في: العناصر المميزة، الإشعارات الخاصة، الأيقونات المهمة
  static const Color accent = Color(0xFF805AD5); // بنفسجي عصري وأنيق
  static const Color accentDark = Color(0xFF553C9A); // بنفسجي داكن راقي
  static const Color accentLight = Color(0xFFB794F6); // بنفسجي فاتح جميل

  // ========== ألوان الحالة المحدثة والعصرية ==========

  /// لون النجاح - أخضر طبيعي وحيوي
  /// ✅ استخدم في: رسائل النجاح، حالة مكتملة، أرباح، مبالغ موجبة
  /// مثال: SnackBar للنجاح، أيقونة التأكيد، حالة "مدفوع"
  static const Color success = Color(0xFF38A169); // أخضر طبيعي وجذاب
  static const Color successLight = Color(0xFFC6F6D5); // خلفية فاتحة منعشة
  static const Color successDark = Color(0xFF2F855A); // أخضر داكن قوي

  /// لون التحذير - برتقالي دافئ ومتوازن
  /// ⚠️ استخدم في: رسائل التحذير، حالة انتظار، مبالغ مستحقة
  /// مثال: تنبيهات المخزون، فواتير مستحقة، حالة "جزئي"
  static const Color warning = Color(0xFFED8936); // برتقالي دافئ وودود
  static const Color warningLight = Color(0xFFFEEBC8); // خلفية فاتحة دافئة
  static const Color warningDark = Color(0xFFDD6B20); // برتقالي داكن قوي

  /// لون الخطأ - أحمر واضح ومؤثر
  /// ❌ استخدم في: رسائل الخطأ، حالة ملغاة، خسائر، مبالغ سالبة
  /// مثال: رسائل الخطأ، حالة "ملغي"، أزرار الحذف
  static const Color error = Color(0xFFE53E3E); // أحمر واضح ومؤثر
  static const Color errorLight = Color(0xFFFED7D7); // خلفية فاتحة ناعمة
  static const Color errorDark = Color(0xFFC53030); // أحمر داكن قوي

  /// لون المعلومات - أزرق هادئ ومهني
  /// ℹ️ استخدم في: رسائل المعلومات، روابط، عناصر تفاعلية
  /// مثال: أزرار المعلومات، روابط، حالة "قيد المراجعة"
  static const Color info = Color(0xFF3182CE); // أزرق مهني وهادئ
  static const Color infoLight = Color(0xFFBEE3F8); // خلفية فاتحة هادئة
  static const Color infoDark = Color(0xFF2C5282); // أزرق داكن عميق

  // ========== الوضع الفاتح المحدث والعصري ==========

  /// خلفية التطبيق الرئيسية - رمادي فاتح دافئ وعصري
  /// 🏠 استخدم في: خلفية الشاشات الرئيسية، Scaffold.backgroundColor
  static const Color lightBackground = Color(0xFFF7FAFC); // رمادي فاتح دافئ

  /// خلفية البطاقات والعناصر - أبيض نقي مع لمسة دافئة
  /// 📄 استخدم في: Card, Container, Dialog, BottomSheet
  static const Color lightSurface = Color(0xFFFFFFFF); // أبيض نقي

  /// خلفية متغيرة للعناصر الثانوية - رمادي فاتح أنيق
  /// 📋 استخدم في: ListTile غير نشط، خلفيات الأقسام
  static const Color lightSurfaceVariant = Color(0xFFEDF2F7); // رمادي فاتح أنيق

  /// ألوان النص على الخلفيات المختلفة - محدثة للتباين الأمثل
  static const Color lightOnBackground = Color(0xFF1A202C); // رمادي داكن عميق
  static const Color lightOnSurface = Color(0xFF2D3748); // رمادي داكن متوازن
  static const Color lightOnSurfaceVariant = Color(0xFF4A5568); // رمادي متوسط

  // ========== ألوان النصوص في الوضع الفاتح المحدثة ==========

  /// النص الأساسي - رمادي داكن عصري وواضح
  /// 📝 استخدم في: العناوين الرئيسية، النصوص المهمة، أسماء المنتجات
  static const Color lightTextPrimary = Color(0xFF1A202C); // رمادي داكن عميق

  /// النص الثانوي - رمادي متوسط متوازن
  /// 📝 استخدم في: الوصف، التواريخ، المعلومات الإضافية، التسميات
  static const Color lightTextSecondary = Color(0xFF4A5568); // رمادي متوسط أنيق

  /// النص التوضيحي - رمادي فاتح ناعم
  /// 💭 استخدم في: placeholder في TextField، نصوص المساعدة
  static const Color lightTextHint = Color(0xFFA0AEC0); // رمادي فاتح ناعم

  /// النص المعطل - رمادي فاتح جداً
  /// 🚫 استخدم في: العناصر المعطلة، النصوص غير المتاحة
  static const Color lightTextDisabled = Color(0xFFE2E8F0); // رمادي فاتح جداً

  // ========== ألوان الحدود والفواصل في الوضع الفاتح المحدثة ==========

  /// خطوط الفصل - رمادي فاتح أنيق
  /// ➖ استخدم في: Divider، خطوط الفصل بين الأقسام
  static const Color lightDivider = Color(0xFFE2E8F0); // رمادي فاتح أنيق

  /// حدود العناصر - رمادي فاتح متوازن
  /// 🔲 استخدم في: حدود TextField، Container، Card
  static const Color lightBorder = Color(0xFFE2E8F0); // رمادي فاتح متوازن

  /// لون الظل - رمادي شفاف ناعم
  /// 🌫️ استخدم في: BoxShadow، ظلال البطاقات والعناصر المرفوعة
  static const Color lightShadow = Color(0x1A2D3748); // ظل رمادي ناعم

  // ========== الوضع الداكن المحدث والعصري ==========

  /// خلفية التطبيق الداكنة - رمادي داكن عميق وأنيق
  static const Color darkBackground = Color(0xFF1A202C); // رمادي داكن عميق

  /// خلفية البطاقات والعناصر الداكنة - رمادي داكن متوازن
  static const Color darkSurface = Color(0xFF2D3748); // رمادي داكن متوازن

  /// خلفية متغيرة للعناصر الثانوية الداكنة - رمادي داكن فاتح
  static const Color darkSurfaceVariant = Color(0xFF4A5568); // رمادي داكن فاتح

  /// ألوان النص على الخلفيات الداكنة - محدثة للتباين الأمثل
  static const Color darkOnBackground = Color(0xFFF7FAFC); // رمادي فاتح جداً
  static const Color darkOnSurface = Color(0xFFEDF2F7); // رمادي فاتح
  static const Color darkOnSurfaceVariant =
      Color(0xFFE2E8F0); // رمادي فاتح متوسط

  // ========== ألوان النصوص في الوضع الداكن المحدثة ==========

  /// النص الأساسي الداكن - رمادي فاتح واضح ومريح للعين
  static const Color darkTextPrimary = Color(0xFFF7FAFC); // رمادي فاتح جداً

  /// النص الثانوي الداكن - رمادي فاتح متوسط
  static const Color darkTextSecondary = Color(0xFFE2E8F0); // رمادي فاتح متوسط

  /// النص التوضيحي الداكن - رمادي متوسط ناعم
  static const Color darkTextHint = Color(0xFFA0AEC0); // رمادي متوسط ناعم

  /// النص المعطل الداكن - رمادي داكن
  static const Color darkTextDisabled = Color(0xFF718096); // رمادي داكن

  // ========== ألوان الحدود والفواصل في الوضع الداكن المحدثة ==========

  /// خطوط الفصل الداكنة - رمادي داكن ناعم
  static const Color darkDivider = Color(0xFF718096); // رمادي داكن ناعم

  /// حدود العناصر الداكنة - رمادي داكن متوازن
  static const Color darkBorder = Color(0xFF718096); // رمادي داكن متوازن

  /// لون الظل الداكن - أسود شفاف ناعم
  static const Color darkShadow = Color(0x40000000); // ظل أسود ناعم

  // ========== ألوان خاصة بالتطبيق ==========

  /// ألوان البطاقات المالية
  static const Color cardProfit = success;
  static const Color cardLoss = error;
  static const Color cardNeutral = lightTextSecondary;

  /// ألوان الحالات
  static const Color statusActive = success;
  static const Color statusInactive = lightTextSecondary;
  static const Color statusPending = warning;
  static const Color statusCancelled = error;

  // ألوان إضافية للتوافق مع الكود الموجود
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF1C1B1F);
  static const Color onError = Color(0xFFFFFFFF);

  // ========== ألوان إضافية محدثة وعصرية ==========

  /// ألوان العنبر - أصفر ذهبي دافئ
  static const Color amber = Color(0xFFF6E05E); // أصفر ذهبي عصري
  static const Color amberLight = Color(0xFFFEF5E7); // خلفية فاتحة دافئة
  static const Color amberDark = Color(0xFFD69E2E); // أصفر ذهبي داكن

  /// ألوان التركوازي - أخضر مزرق هادئ
  static const Color teal = Color(0xFF319795); // تركوازي عصري
  static const Color tealLight = Color(0xFFB2F5EA); // خلفية فاتحة منعشة
  static const Color tealDark = Color(0xFF2C7A7B); // تركوازي داكن

  /// ألوان النيلي - أزرق عميق أنيق
  static const Color indigo = Color(0xFF4C51BF); // نيلي عميق
  static const Color indigoLight = Color(0xFFC3DAFE); // خلفية فاتحة هادئة
  static const Color indigoDark = Color(0xFF434190); // نيلي داكن

  /// ألوان البني - بني دافئ وطبيعي
  static const Color brown = Color(0xFF8B4513); // بني دافئ
  static const Color brownLight = Color(0xFFF7FAFC); // خلفية فاتحة ناعمة
  static const Color brownDark = Color(0xFF744210); // بني داكن

  /// ألوان البرتقالي العميق - برتقالي نابض بالحياة
  static const Color deepOrange = Color(0xFFFF6B35); // برتقالي نابض
  static const Color deepOrangeLight = Color(0xFFFED7CC); // خلفية فاتحة دافئة
  static const Color deepOrangeDark = Color(0xFFE55A2B); // برتقالي داكن

  // ألوان حالات القيود المحاسبية
  static const Color draft = lightTextSecondary;
  static const Color posted = success;
  static const Color voided = error;
  static const Color disabled = lightTextSecondary;

  // ========== ألوان الوحدات والأقسام ==========

  /// ألوان وحدات النظام المحدثة - لتمييز كل قسم بلون عصري ومتناسق

  /// 🔐 وحدة المصادقة وتسجيل الدخول - أزرق مهني هادئ
  /// استخدم في: شاشات تسجيل الدخول، إدارة المستخدمين، الصلاحيات
  static const Color moduleAuth = Color(0xFF3182CE); // أزرق مهني

  /// 👥 وحدة المستخدمين - أخضر طبيعي منعش
  /// استخدم في: قائمة المستخدمين، إضافة مستخدم، الأدوار
  static const Color moduleUsers = Color(0xFF38A169); // أخضر طبيعي

  /// 📦 وحدة المنتجات - بنفسجي أنيق وراقي
  /// استخدم في: كتالوج المنتجات، إضافة منتج، فئات المنتجات
  static const Color moduleProducts = Color(0xFF805AD5); // بنفسجي أنيق

  /// 💰 وحدة المبيعات - تركوازي عصري وهادئ
  /// استخدم في: فواتير المبيعات، نقاط البيع، العملاء
  static const Color moduleSales = Color(0xFF319795); // تركوازي عصري

  /// 🛒 وحدة المشتريات - برتقالي دافئ وودود
  /// استخدم في: فواتير المشتريات، الموردين، طلبات الشراء
  static const Color modulePurchases = Color(0xFFED8936); // برتقالي دافئ

  /// 📋 وحدة المخزون - بني أنيق ودافئ
  /// استخدم في: إدارة المخزون، المستودعات، جرد المخزون
  static const Color moduleInventory = Color(0xFF8B4513); // بني أنيق

  /// 💼 وحدة الحسابات - نيلي عميق وأنيق
  /// استخدم في: دليل الحسابات، القيود المحاسبية، التقارير المالية
  static const Color moduleAccounts = Color(0xFF4C51BF); // نيلي عميق

  /// 📊 وحدة التقارير - وردي جذاب ونابض
  /// استخدم في: جميع أنواع التقارير، الإحصائيات، الرسوم البيانية
  static const Color moduleReports = Color(0xFFED64A6); // وردي جذاب

  /// ⚙️ وحدة الإعدادات - رمادي مزرق عصري
  /// استخدم في: إعدادات التطبيق، التفضيلات، الإعدادات العامة
  static const Color moduleSettings = Color(0xFF718096); // رمادي مزرق

  // ========== ألوان أنواع الحسابات المحاسبية المحدثة ==========

  /// 🏢 الأصول - أزرق مهني هادئ (ما تملكه الشركة)
  /// استخدم في: النقدية، المخزون، الأثاث، السيارات، العقارات
  static const Color accountAsset = Color(0xFF3182CE); // أزرق مهني

  /// 📋 الخصوم - أحمر واضح ومؤثر (ما على الشركة من التزامات)
  /// استخدم في: القروض، الديون، المستحقات، الرواتب المستحقة
  static const Color accountLiability = Color(0xFFE53E3E); // أحمر واضح

  /// 👑 حقوق الملكية - بنفسجي أنيق وراقي (حقوق أصحاب الشركة)
  /// استخدم في: رأس المال، الأرباح المحتجزة، حسابات الشركاء
  static const Color accountEquity = Color(0xFF805AD5); // بنفسجي أنيق

  /// 💰 الإيرادات - أخضر طبيعي منعش (دخل الشركة)
  /// استخدم في: مبيعات، إيرادات خدمات، إيرادات أخرى
  static const Color accountRevenue = Color(0xFF38A169); // أخضر طبيعي

  /// 💸 المصروفات - برتقالي دافئ وودود (نفقات الشركة)
  /// استخدم في: الرواتب، الإيجار، الكهرباء، مصاريف التسويق
  static const Color accountExpense = Color(0xFFED8936); // برتقالي دافئ

  /// 👤 العملاء - تركوازي عصري وهادئ (حسابات العملاء)
  /// استخدم في: ذمم العملاء، حسابات العملاء الجارية
  static const Color accountCustomer = Color(0xFF319795); // تركوازي عصري

  /// 🏭 الموردين - بني أنيق ودافئ (حسابات الموردين)
  /// استخدم في: ذمم الموردين، حسابات الموردين الجارية
  static const Color accountSupplier = Color(0xFF8B4513); // بني أنيق

  /// 💵 النقدية - نيلي عميق وأنيق (الحسابات النقدية)
  /// استخدم في: الصندوق، البنك، الحسابات الجارية
  static const Color accountCash = Color(0xFF4C51BF); // نيلي عميق

  // ========== ألوان أنواع المعاملات المحدثة ==========

  /// 💰 الإيرادات - أخضر طبيعي منعش (دخل للشركة)
  /// استخدم في: قيود الإيرادات، المبالغ الواردة، الأرباح
  static const Color transactionIncome = Color(0xFF38A169); // أخضر طبيعي

  /// 💸 المصروفات - أحمر واضح ومؤثر (خروج من الشركة)
  /// استخدم في: قيود المصروفات، المبالغ الصادرة، التكاليف
  static const Color transactionExpense = Color(0xFFE53E3E); // أحمر واضح

  /// 🔄 التحويلات - أزرق مهني هادئ (نقل بين الحسابات)
  /// استخدم في: تحويل بين البنوك، نقل من الصندوق للبنك
  static const Color transactionTransfer = Color(0xFF3182CE); // أزرق مهني

  /// 🛍️ المبيعات - بنفسجي أنيق وراقي (عمليات البيع)
  /// استخدم في: فواتير المبيعات، إيصالات البيع
  static const Color transactionSale = Color(0xFF805AD5); // بنفسجي أنيق

  /// 🛒 المشتريات - برتقالي دافئ وودود (عمليات الشراء)
  /// استخدم في: فواتير المشتريات، إيصالات الشراء
  static const Color transactionPurchase = Color(0xFFED8936); // برتقالي دافئ

  // ========== ألوان مستويات الوصول والصلاحيات ==========

  /// 🚫 عدم الوصول - رمادي (لا يوجد صلاحية)
  /// استخدم في: المستخدمين بدون صلاحيات، الميزات المقفلة
  static const Color accessNone = lightTextSecondary;

  /// 👁️ العرض فقط - أزرق (قراءة بدون تعديل)
  /// استخدم في: صلاحية المشاهدة، التقارير للمستخدمين العاديين
  static const Color accessView = info;

  /// ✏️ التعديل - برتقالي (قراءة وتعديل)
  /// استخدم في: صلاحية التعديل، المستخدمين المتوسطين
  static const Color accessEdit = warning;

  /// 🔓 الوصول الكامل - أخضر (جميع الصلاحيات)
  /// استخدم في: صلاحية المدير، الوصول الكامل للنظام
  static const Color accessFull = success;

  // ========== ألوان حالات المزامنة ==========

  /// ✅ مزامن - أخضر (تم التحديث بنجاح)
  /// استخدم في: البيانات المحدثة، المزامنة الناجحة
  static const Color syncSynced = success;

  /// ⏳ في الانتظار - برتقالي (قيد المزامنة)
  /// استخدم في: البيانات قيد التحديث، انتظار الاتصال
  static const Color syncPending = warning;

  /// ❌ فشل - أحمر (خطأ في المزامنة)
  /// استخدم في: فشل التحديث، أخطاء الشبكة
  static const Color syncFailed = error;

  /// 📴 غير متصل - رمادي (لا يوجد اتصال)
  /// استخدم في: وضع عدم الاتصال، البيانات المحلية فقط
  static const Color syncOffline = lightTextSecondary;

  // ========== ألوان مستويات الأولوية ==========

  /// 🟢 أولوية منخفضة - أخضر (غير عاجل)
  /// استخدم في: المهام العادية، التذكيرات البسيطة
  static const Color priorityLow = success;

  /// 🟡 أولوية متوسطة - برتقالي (مهم نسبياً)
  /// استخدم في: المهام المهمة، التنبيهات المتوسطة
  static const Color priorityMedium = warning;

  /// 🔴 أولوية عالية - أحمر (مهم جداً)
  /// استخدم في: المهام الحرجة، التنبيهات المهمة
  static const Color priorityHigh = error;

  /// 🟣 أولوية عاجلة - بنفسجي (فوري)
  /// استخدم في: الطوارئ، المهام الفورية، التنبيهات الحرجة
  static const Color priorityUrgent = accent;

  /// ألوان التدرج المحدثة للخلفيات الجذابة والعصرية
  static const List<Color> primaryGradient = [
    Color(0xFFE53E3E), // أحمر عصري
    Color(0xFFC53030), // أحمر داكن
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF2D3748), // رمادي عصري
    Color(0xFF1A202C), // رمادي داكن
  ];

  static const List<Color> successGradient = [
    Color(0xFF38A169), // أخضر طبيعي
    Color(0xFF2F855A), // أخضر داكن
  ];

  static const List<Color> darkGradient = [
    Color(0xFF2D3748), // رمادي داكن عصري
    Color(0xFF1A202C), // رمادي داكن عميق
  ];

  /// تدرجات جديدة وعصرية
  static const List<Color> infoGradient = [
    Color(0xFF3182CE), // أزرق مهني
    Color(0xFF2C5282), // أزرق داكن
  ];

  static const List<Color> warningGradient = [
    Color(0xFFED8936), // برتقالي دافئ
    Color(0xFFDD6B20), // برتقالي داكن
  ];

  static const List<Color> accentGradient = [
    Color(0xFF805AD5), // بنفسجي أنيق
    Color(0xFF553C9A), // بنفسجي داكن
  ];

  // ========== ألوان قابلة للتخصيص ==========

  /// مجموعة الألوان المتاحة للمستخدم - محدثة وعصرية
  static const Map<String, Map<String, dynamic>> availableThemes = {
    'red': {
      'name': 'أحمر تاجر بلس العصري',
      'primary': Color(0xFFE53E3E), // أحمر عصري وحيوي
      'primaryDark': Color(0xFFC53030),
      'primaryLight': Color(0xFFFEB2B2),
      'gradient': [Color(0xFFE53E3E), Color(0xFFC53030)],
    },
    'blue': {
      'name': 'أزرق مهني هادئ',
      'primary': Color(0xFF3182CE), // أزرق مهني وهادئ
      'primaryDark': Color(0xFF2C5282),
      'primaryLight': Color(0xFFBEE3F8),
      'gradient': [Color(0xFF3182CE), Color(0xFF2C5282)],
    },
    'green': {
      'name': 'أخضر طبيعي منعش',
      'primary': Color(0xFF38A169), // أخضر طبيعي وجذاب
      'primaryDark': Color(0xFF2F855A),
      'primaryLight': Color(0xFFC6F6D5),
      'gradient': [Color(0xFF38A169), Color(0xFF2F855A)],
    },
    'purple': {
      'name': 'بنفسجي أنيق وراقي',
      'primary': Color(0xFF805AD5), // بنفسجي عصري وأنيق
      'primaryDark': Color(0xFF553C9A),
      'primaryLight': Color(0xFFB794F6),
      'gradient': [Color(0xFF805AD5), Color(0xFF553C9A)],
    },
    'orange': {
      'name': 'برتقالي دافئ وودود',
      'primary': Color(0xFFED8936), // برتقالي دافئ وودود
      'primaryDark': Color(0xFFDD6B20),
      'primaryLight': Color(0xFFFEEBC8),
      'gradient': [Color(0xFFED8936), Color(0xFFDD6B20)],
    },
    'teal': {
      'name': 'تركوازي عصري وهادئ',
      'primary': Color(0xFF319795), // تركوازي عصري
      'primaryDark': Color(0xFF2C7A7B),
      'primaryLight': Color(0xFFB2F5EA),
      'gradient': [Color(0xFF319795), Color(0xFF2C7A7B)],
    },
    'indigo': {
      'name': 'نيلي عميق وأنيق',
      'primary': Color(0xFF4C51BF), // نيلي عميق وأنيق
      'primaryDark': Color(0xFF434190),
      'primaryLight': Color(0xFFC3DAFE),
      'gradient': [Color(0xFF4C51BF), Color(0xFF434190)],
    },
    'pink': {
      'name': 'وردي جذاب ونابض',
      'primary': Color(0xFFED64A6), // وردي جذاب ونابض
      'primaryDark': Color(0xFFD53F8C),
      'primaryLight': Color(0xFFFED7E2),
      'gradient': [Color(0xFFED64A6), Color(0xFFD53F8C)],
    },
    'emerald': {
      'name': 'زمردي فاخر ومميز',
      'primary': Color(0xFF10B981), // زمردي فاخر
      'primaryDark': Color(0xFF059669),
      'primaryLight': Color(0xFFD1FAE5),
      'gradient': [Color(0xFF10B981), Color(0xFF059669)],
    },
    'cyan': {
      'name': 'سماوي منعش وحيوي',
      'primary': Color(0xFF06B6D4), // سماوي منعش
      'primaryDark': Color(0xFF0891B2),
      'primaryLight': Color(0xFFCFFAFE),
      'gradient': [Color(0xFF06B6D4), Color(0xFF0891B2)],
    },
    'violet': {
      'name': 'بنفسجي عميق وأنيق',
      'primary': Color(0xFF7C3AED), // بنفسجي عميق
      'primaryDark': Color(0xFF5B21B6),
      'primaryLight': Color(0xFFDDD6FE),
      'gradient': [Color(0xFF7C3AED), Color(0xFF5B21B6)],
    },
    'rose': {
      'name': 'وردي راقي ومميز',
      'primary': Color(0xFFF43F5E), // وردي راقي
      'primaryDark': Color(0xFFE11D48),
      'primaryLight': Color(0xFFFECDD3),
      'gradient': [Color(0xFFF43F5E), Color(0xFFE11D48)],
    },
    'amber': {
      'name': 'كهرماني دافئ وجذاب',
      'primary': Color(0xFFF59E0B), // كهرماني دافئ
      'primaryDark': Color(0xFFD97706),
      'primaryLight': Color(0xFFFEF3C7),
      'gradient': [Color(0xFFF59E0B), Color(0xFFD97706)],
    },
    'lime': {
      'name': 'أخضر ليموني منعش',
      'primary': Color(0xFF84CC16), // أخضر ليموني
      'primaryDark': Color(0xFF65A30D),
      'primaryLight': Color(0xFFECFCCB),
      'gradient': [Color(0xFF84CC16), Color(0xFF65A30D)],
    },
    'sky': {
      'name': 'أزرق سماوي هادئ',
      'primary': Color(0xFF0EA5E9), // أزرق سماوي
      'primaryDark': Color(0xFF0284C7),
      'primaryLight': Color(0xFFE0F2FE),
      'gradient': [Color(0xFF0EA5E9), Color(0xFF0284C7)],
    },
  };

  // ========== دوال مساعدة ذكية ==========

  /// الحصول على لون النص المناسب حسب لون الخلفية (ذكي)
  static Color getTextColorForBackground(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    // تحسين التباين للقراءة الأفضل
    if (luminance > 0.7) {
      return lightTextPrimary; // أسود للخلفيات الفاتحة جداً
    } else if (luminance > 0.5) {
      return lightTextPrimary; // رمادي داكن للخلفيات الفاتحة
    } else if (luminance > 0.3) {
      return const Color(0xFFE0E0E0); // رمادي فاتح للخلفيات المتوسطة
    } else {
      return onPrimary; // أبيض للخلفيات الداكنة
    }
  }

  /// الحصول على لون الأيقونة المناسب حسب لون الخلفية (ذكي)
  static Color getIconColorForBackground(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    if (luminance > 0.6) {
      return lightTextSecondary;
    } else {
      return darkTextSecondary;
    }
  }

  /// الحصول على لون الخلفية المتكيف حسب الثيم
  static Color getAdaptiveCardBackground(bool isDark) {
    return isDark ? darkSurface : lightSurface;
  }

  /// الحصول على لون النص المتكيف حسب الثيم
  static Color getAdaptiveTextColor(bool isDark) {
    return isDark ? darkTextPrimary : lightTextPrimary;
  }

  /// الحصول على لون النص الثانوي المتكيف
  static Color getAdaptiveSecondaryTextColor(bool isDark) {
    return isDark ? darkTextSecondary : lightTextSecondary;
  }

  /// الحصول على لون الحدود المتكيف
  static Color getAdaptiveBorderColor(bool isDark) {
    return isDark ? darkBorder : lightBorder;
  }

  /// الحصول على لون الظل المتكيف
  static Color getAdaptiveShadowColor(bool isDark) {
    return isDark ? darkShadow : lightShadow;
  }

  /// الحصول على لون نوع الحساب
  static Color getAccountTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'asset':
      case 'أصول':
        return accountAsset;
      case 'liability':
      case 'خصوم':
        return accountLiability;
      case 'equity':
      case 'حقوق الملكية':
        return accountEquity;
      case 'revenue':
      case 'إيرادات':
        return accountRevenue;
      case 'expense':
      case 'مصروفات':
        return accountExpense;
      case 'customer':
      case 'عملاء':
        return accountCustomer;
      case 'supplier':
      case 'موردين':
        return accountSupplier;
      case 'cash':
      case 'نقدية':
        return accountCash;
      default:
        return lightTextSecondary;
    }
  }

  /// فحص التباين بين لونين
  static double getContrastRatio(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// التأكد من التباين الكافي للنص
  static Color ensureTextContrast(Color textColor, Color backgroundColor) {
    final contrast = getContrastRatio(textColor, backgroundColor);
    if (contrast < 4.5) {
      // إذا كان التباين ضعيف، نغير لون النص
      return getTextColorForBackground(backgroundColor);
    }
    return textColor;
  }

  /// إنشاء تدرج لوني
  static LinearGradient createGradient(
    List<Color> colors, {
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
    );
  }

  /// إنشاء ظل ملون
  static List<BoxShadow> createSoftShadow({
    Color? color,
    double blurRadius = 8.0,
    double spreadRadius = 0.0,
    Offset offset = const Offset(0, 2),
    double opacity = 0.15,
  }) {
    return [
      BoxShadow(
        color: (color ?? lightTextPrimary).withValues(alpha: opacity),
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
        offset: offset,
      ),
    ];
  }

  /// الحصول على لون الوحدة حسب الاسم
  static Color getModuleColor(String module) {
    switch (module.toLowerCase()) {
      case 'auth':
      case 'authentication':
      case 'المصادقة':
        return moduleAuth;
      case 'users':
      case 'المستخدمين':
        return moduleUsers;
      case 'products':
      case 'المنتجات':
        return moduleProducts;
      case 'sales':
      case 'المبيعات':
        return moduleSales;
      case 'purchases':
      case 'المشتريات':
        return modulePurchases;
      case 'inventory':
      case 'المخزون':
        return moduleInventory;
      case 'accounts':
      case 'الحسابات':
        return moduleAccounts;
      case 'reports':
      case 'التقارير':
        return moduleReports;
      case 'settings':
      case 'الإعدادات':
        return moduleSettings;
      default:
        return lightTextSecondary;
    }
  }

  /// الحصول على لون نوع المعاملة
  static Color getTransactionTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'income':
      case 'إيراد':
        return transactionIncome;
      case 'expense':
      case 'مصروف':
        return transactionExpense;
      case 'transfer':
      case 'تحويل':
        return transactionTransfer;
      case 'sale':
      case 'مبيعات':
        return transactionSale;
      case 'purchase':
      case 'مشتريات':
        return transactionPurchase;
      default:
        return lightTextSecondary;
    }
  }

  /// الحصول على لون مستوى الوصول
  static Color getAccessLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'none':
      case 'لا يوجد':
        return accessNone;
      case 'view':
      case 'عرض':
        return accessView;
      case 'edit':
      case 'تعديل':
        return accessEdit;
      case 'full':
      case 'كامل':
        return accessFull;
      default:
        return accessNone;
    }
  }

  /// الحصول على لون حالة المزامنة
  static Color getSyncStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'synced':
      case 'مزامن':
        return syncSynced;
      case 'pending':
      case 'انتظار':
        return syncPending;
      case 'failed':
      case 'فشل':
        return syncFailed;
      case 'offline':
      case 'غير متصل':
        return syncOffline;
      default:
        return syncOffline;
    }
  }

  /// الحصول على لون الأولوية
  static Color getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'low':
      case 'منخفضة':
        return priorityLow;
      case 'medium':
      case 'متوسطة':
        return priorityMedium;
      case 'high':
      case 'عالية':
        return priorityHigh;
      case 'urgent':
      case 'عاجلة':
        return priorityUrgent;
      default:
        return priorityMedium;
    }
  }

  // ========== دوال مساعدة جديدة للحالات المتقدمة ==========

  /// الحصول على لون حالة الشبكة
  static Color getNetworkStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'connected':
      case 'متصل':
        return networkConnected;
      case 'disconnected':
      case 'منقطع':
        return networkDisconnected;
      case 'connecting':
      case 'يتصل':
        return networkConnecting;
      default:
        return networkUnknown;
    }
  }

  /// الحصول على لون حالة البطارية
  static Color getBatteryStatusColor(double percentage) {
    if (percentage >= 80) {
      return batteryFull;
    } else if (percentage >= 40) {
      return batteryMedium;
    } else {
      return batteryLow;
    }
  }

  /// الحصول على لون مستوى الأمان
  static Color getSecurityLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'high':
      case 'عالي':
        return securityHigh;
      case 'medium':
      case 'متوسط':
        return securityMedium;
      case 'low':
      case 'منخفض':
        return securityLow;
      default:
        return securityUnknown;
    }
  }

  /// الحصول على لون مستوى الأداء
  static Color getPerformanceLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'excellent':
      case 'ممتاز':
        return performanceExcellent;
      case 'good':
      case 'جيد':
        return performanceGood;
      case 'average':
      case 'متوسط':
        return performanceAverage;
      case 'poor':
      case 'ضعيف':
        return performancePoor;
      default:
        return performanceAverage;
    }
  }

  /// إنشاء تدرج لوني ديناميكي
  static LinearGradient createDynamicGradient(
    Color startColor,
    Color endColor, {
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    List<double>? stops,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: [startColor, endColor],
      stops: stops,
    );
  }

  /// إنشاء تدرج لوني متعدد الألوان
  static LinearGradient createMultiColorGradient(
    List<Color> colors, {
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    List<double>? stops,
  }) {
    return LinearGradient(
      begin: begin,
      end: end,
      colors: colors,
      stops: stops,
    );
  }

  // ========== نظام الشفافية العصري والذكي ==========

  /// إنشاء لون بشفافية مخصصة - طريقة عصرية وآمنة
  static Color withCustomOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity.clamp(0.0, 1.0));
  }

  /// مستويات التأكيد النصي العصرية (Material Design 3)
  static Color get textEmphasisHigh => lightTextPrimary.withValues(alpha: 0.87);
  static Color get textEmphasisMedium =>
      lightTextPrimary.withValues(alpha: 0.60);
  static Color get textEmphasisDisabled =>
      lightTextPrimary.withValues(alpha: 0.38);

  /// طبقات التفاعل العصرية
  static Color get surfaceOverlay => lightTextPrimary.withValues(alpha: 0.05);
  static Color get hoverOverlay => lightTextPrimary.withValues(alpha: 0.08);
  static Color get focusOverlay => lightTextPrimary.withValues(alpha: 0.12);
  static Color get pressedOverlay => lightTextPrimary.withValues(alpha: 0.16);
  static Color get selectedOverlay => lightTextPrimary.withValues(alpha: 0.12);
  static Color get draggedOverlay => lightTextPrimary.withValues(alpha: 0.16);

  /// مستويات التأكيد للوضع الداكن
  static Color get darkTextEmphasisHigh =>
      darkTextPrimary.withValues(alpha: 0.87);
  static Color get darkTextEmphasisMedium =>
      darkTextPrimary.withValues(alpha: 0.60);
  static Color get darkTextEmphasisDisabled =>
      darkTextPrimary.withValues(alpha: 0.38);

  /// طبقات التفاعل للوضع الداكن
  static Color get darkSurfaceOverlay =>
      darkTextPrimary.withValues(alpha: 0.05);
  static Color get darkHoverOverlay => darkTextPrimary.withValues(alpha: 0.08);
  static Color get darkFocusOverlay => darkTextPrimary.withValues(alpha: 0.12);
  static Color get darkPressedOverlay =>
      darkTextPrimary.withValues(alpha: 0.16);
  static Color get darkSelectedOverlay =>
      darkTextPrimary.withValues(alpha: 0.12);
  static Color get darkDraggedOverlay =>
      darkTextPrimary.withValues(alpha: 0.16);

  // ========== ألوان إضافية مطلوبة للنظام ==========

  static Color get primaryContainer => primaryLight.withValues(alpha: 0.12);
  static Color get onPrimaryContainer => primaryDark;
  static Color get secondaryContainer => secondaryLight.withValues(alpha: 0.12);
  static Color get onSecondaryContainer => secondaryDark;
  static Color get errorContainer => errorLight.withValues(alpha: 0.12);
  static Color get onErrorContainer => errorDark;
  static Color get surfaceContainer => lightSurfaceVariant;
  static Color get surfaceContainerHighest => lightSurfaceVariant;
  static Color get outline => lightBorder;
  static Color get outlineVariant => lightBorder.withValues(alpha: 0.5);
  static Color get scrim => lightTextPrimary.withValues(alpha: 0.32);
  static Color get inverseSurface => darkSurface;
  static Color get onInverseSurface => darkTextPrimary;
  static Color get inversePrimary => primaryLight;

  // ========== ألوان الطباعة والتقارير PDF ==========

  /// ألوان خاصة بالطباعة والتقارير لتحل محل PdfColors
  /// هذه الألوان متوافقة مع الطباعة وتعطي نتائج ممتازة

  /// خلفية رؤوس الجداول - رمادي فاتح للطباعة
  static const Color pdfHeaderBackground = Color(0xFFF5F5F5);

  /// لون النص في رؤوس الجداول - أسود قوي
  static const Color pdfHeaderText = Color(0xFF000000);

  /// لون النص العادي في التقارير - أسود متوسط
  static const Color pdfBodyText = Color(0xFF212121);

  /// لون النص الثانوي في التقارير - رمادي متوسط
  static const Color pdfSecondaryText = Color(0xFF757575);

  /// خلفية الصفوف المتناوبة في الجداول - رمادي فاتح جداً
  static const Color pdfAlternateRow = Color(0xFFFAFAFA);

  /// حدود الجداول والعناصر - رمادي متوسط
  static const Color pdfBorder = Color(0xFFE0E0E0);

  /// لون الخط الفاصل - رمادي فاتح
  static const Color pdfDivider = Color(0xFFEEEEEE);

  /// ألوان الحالات للطباعة
  static const Color pdfSuccess = Color(0xFF4CAF50); // أخضر للطباعة
  static const Color pdfWarning = Color(0xFFFF9800); // برتقالي للطباعة
  static const Color pdfError = Color(0xFFF44336); // أحمر للطباعة
  static const Color pdfInfo = Color(0xFF2196F3); // أزرق للطباعة

  // ========== ألوان حالات متقدمة جديدة ==========

  /// ألوان حالات الشبكة والاتصال
  static const Color networkConnected = Color(0xFF38A169); // متصل - أخضر
  static const Color networkDisconnected = Color(0xFFE53E3E); // منقطع - أحمر
  static const Color networkConnecting = Color(0xFFED8936); // يتصل - برتقالي
  static const Color networkUnknown = Color(0xFF718096); // غير معروف - رمادي

  /// ألوان حالات البطارية والطاقة
  static const Color batteryFull = Color(0xFF38A169); // ممتلئة - أخضر
  static const Color batteryMedium = Color(0xFFED8936); // متوسطة - برتقالي
  static const Color batteryLow = Color(0xFFE53E3E); // منخفضة - أحمر
  static const Color batteryCharging = Color(0xFF3182CE); // تشحن - أزرق

  /// ألوان حالات الأمان والحماية
  static const Color securityHigh = Color(0xFF38A169); // أمان عالي - أخضر
  static const Color securityMedium = Color(0xFFED8936); // أمان متوسط - برتقالي
  static const Color securityLow = Color(0xFFE53E3E); // أمان منخفض - أحمر
  static const Color securityUnknown = Color(0xFF718096); // غير معروف - رمادي

  /// ألوان حالات الأداء والسرعة
  static const Color performanceExcellent = Color(0xFF38A169); // ممتاز - أخضر
  static const Color performanceGood = Color(0xFF319795); // جيد - تركوازي
  static const Color performanceAverage = Color(0xFFED8936); // متوسط - برتقالي
  static const Color performancePoor = Color(0xFFE53E3E); // ضعيف - أحمر
}

/// Extension لإضافة دوال مساعدة للألوان
extension ColorExtensions on Color {
  /// تغميق اللون بنسبة معينة
  Color darken(double amount) {
    assert(amount >= 0 && amount <= 1, 'Amount must be between 0 and 1');
    final hsl = HSLColor.fromColor(this);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }

  /// تفتيح اللون بنسبة معينة
  Color lighten(double amount) {
    assert(amount >= 0 && amount <= 1, 'Amount must be between 0 and 1');
    final hsl = HSLColor.fromColor(this);
    final hslLight =
        hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }
}
