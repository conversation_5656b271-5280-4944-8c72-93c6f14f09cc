import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'advanced_color_system.dart';
import 'app_colors.dart';

/// نظام التوافق اللوني المتقدم
/// 🎨 ضمان التوافق المثالي بين جميع العناصر والطبقات
/// 🔍 فحص وتصحيح التباين تلقائياً
/// 🌈 تنسيق الألوان بذكاء عبر جميع المكونات
/// ♿ ضمان إمكانية الوصول في كل طبقة
class ColorHarmonySystem {
  ColorHarmonySystem._();

  // ========== نظام الطبقات اللونية ==========

  /// إنشاء نظام طبقات متوافق لوناً
  static ColorLayerSystem createHarmoniousLayers({
    required Color primaryColor,
    required bool isDarkMode,
    double contrastRatio = 4.5,
  }) {
    final baseHsl = HSLColor.fromColor(primaryColor);

    return ColorLayerSystem(
      // طبقة الخلفية الأساسية
      backgroundLayer: _createBackgroundLayer(baseHsl, isDarkMode),

      // طبقة السطح (البطاقات والحاويات)
      surfaceLayer: _createSurfaceLayer(baseHsl, isDarkMode),

      // طبقة المحتوى (النصوص والأيقونات)
      contentLayer: _createContentLayer(baseHsl, isDarkMode, contrastRatio),

      // طبقة التفاعل (الأزرار والروابط)
      interactionLayer: _createInteractionLayer(baseHsl, isDarkMode),

      // طبقة التأكيد (العناصر المهمة)
      emphasisLayer: _createEmphasisLayer(baseHsl, isDarkMode),

      // طبقة الحالة (نجاح، خطأ، تحذير)
      statusLayer: _createStatusLayer(baseHsl, isDarkMode),
    );
  }

  /// فحص وتصحيح التوافق بين طبقتين
  static ColorCompatibilityResult checkLayerCompatibility(
    Color foregroundColor,
    Color backgroundColor, {
    double minimumContrast = 4.5,
    bool autoFix = true,
  }) {
    final contrast = AdvancedColorSystem.ensureAccessibleContrast(
      foregroundColor,
      backgroundColor,
      minimumRatio: minimumContrast,
    );

    final currentContrast =
        _calculateContrastRatio(foregroundColor, backgroundColor);
    final isCompatible = currentContrast >= minimumContrast;

    return ColorCompatibilityResult(
      originalForeground: foregroundColor,
      originalBackground: backgroundColor,
      adjustedForeground: autoFix ? contrast : foregroundColor,
      adjustedBackground: backgroundColor,
      contrastRatio: currentContrast,
      isCompatible: isCompatible,
      wasAdjusted: autoFix && !isCompatible,
      recommendedContrast: minimumContrast,
    );
  }

  /// إنشاء مجموعة ألوان متوافقة للنصوص
  static TextColorPalette createTextColorPalette({
    required Color backgroundColor,
    required Color primaryColor,
    required bool isDarkMode,
  }) {
    final bgLuminance = backgroundColor.computeLuminance();
    final isLightBackground = bgLuminance > 0.5;

    return TextColorPalette(
      // النص الأساسي - أعلى تباين
      primary:
          _getOptimalTextColor(backgroundColor, isDarkMode, TextEmphasis.high),

      // النص الثانوي - تباين متوسط
      secondary: _getOptimalTextColor(
          backgroundColor, isDarkMode, TextEmphasis.medium),

      // النص المساعد - تباين منخفض
      tertiary:
          _getOptimalTextColor(backgroundColor, isDarkMode, TextEmphasis.low),

      // النص المعطل
      disabled: _getOptimalTextColor(
          backgroundColor, isDarkMode, TextEmphasis.disabled),

      // نص الروابط
      link: _getOptimalLinkColor(backgroundColor, primaryColor, isDarkMode),

      // نص الأخطاء
      error: _getOptimalErrorColor(backgroundColor, isDarkMode),

      // نص النجاح
      success: _getOptimalSuccessColor(backgroundColor, isDarkMode),

      // نص التحذير
      warning: _getOptimalWarningColor(backgroundColor, isDarkMode),
    );
  }

  /// إنشاء مجموعة ألوان متوافقة للأزرار
  static ButtonColorPalette createButtonColorPalette({
    required Color primaryColor,
    required Color backgroundColor,
    required bool isDarkMode,
  }) {
    final primaryHsl = HSLColor.fromColor(primaryColor);

    return ButtonColorPalette(
      // الزر الأساسي
      primary: ButtonColorSet(
        background: primaryColor,
        foreground:
            _getOptimalTextColor(primaryColor, isDarkMode, TextEmphasis.high),
        border: primaryColor,
        hover: _adjustColorBrightness(primaryColor, isDarkMode ? 0.1 : -0.1),
        pressed: _adjustColorBrightness(primaryColor, isDarkMode ? 0.2 : -0.2),
        disabled: primaryColor.withValues(alpha: 0.3),
      ),

      // الزر الثانوي
      secondary: ButtonColorSet(
        background: Colors.transparent,
        foreground: primaryColor,
        border: primaryColor,
        hover: primaryColor.withValues(alpha: 0.1),
        pressed: primaryColor.withValues(alpha: 0.2),
        disabled: primaryColor.withValues(alpha: 0.3),
      ),

      // الزر النصي
      text: ButtonColorSet(
        background: Colors.transparent,
        foreground: primaryColor,
        border: Colors.transparent,
        hover: primaryColor.withValues(alpha: 0.1),
        pressed: primaryColor.withValues(alpha: 0.2),
        disabled: primaryColor.withValues(alpha: 0.3),
      ),

      // زر الخطر
      danger: ButtonColorSet(
        background: AppColors.error,
        foreground: Colors.white,
        border: AppColors.error,
        hover: _adjustColorBrightness(AppColors.error, isDarkMode ? 0.1 : -0.1),
        pressed:
            _adjustColorBrightness(AppColors.error, isDarkMode ? 0.2 : -0.2),
        disabled: AppColors.error.withValues(alpha: 0.3),
      ),

      // زر النجاح
      success: ButtonColorSet(
        background: AppColors.success,
        foreground: Colors.white,
        border: AppColors.success,
        hover:
            _adjustColorBrightness(AppColors.success, isDarkMode ? 0.1 : -0.1),
        pressed:
            _adjustColorBrightness(AppColors.success, isDarkMode ? 0.2 : -0.2),
        disabled: AppColors.success.withValues(alpha: 0.3),
      ),
    );
  }

  /// إنشاء مجموعة ألوان متوافقة للحدود والفواصل
  static BorderColorPalette createBorderColorPalette({
    required Color backgroundColor,
    required Color primaryColor,
    required bool isDarkMode,
  }) {
    final bgLuminance = backgroundColor.computeLuminance();
    final baseOpacity = isDarkMode ? 0.2 : 0.15;

    return BorderColorPalette(
      // الحدود الخفيفة
      subtle:
          _getOptimalTextColor(backgroundColor, isDarkMode, TextEmphasis.low)
              .withValues(alpha: baseOpacity),

      // الحدود العادية
      normal:
          _getOptimalTextColor(backgroundColor, isDarkMode, TextEmphasis.medium)
              .withValues(alpha: baseOpacity * 1.5),

      // الحدود القوية
      strong:
          _getOptimalTextColor(backgroundColor, isDarkMode, TextEmphasis.high)
              .withValues(alpha: baseOpacity * 2),

      // حدود التركيز
      focus: primaryColor,

      // حدود الخطأ
      error: AppColors.error,

      // حدود النجاح
      success: AppColors.success,

      // حدود التحذير
      warning: AppColors.warning,
    );
  }

  /// إنشاء مجموعة ألوان متوافقة للظلال
  static ShadowColorPalette createShadowColorPalette({
    required Color backgroundColor,
    required Color primaryColor,
    required bool isDarkMode,
  }) {
    final shadowBase = isDarkMode ? Colors.black : primaryColor;

    return ShadowColorPalette(
      // ظل خفيف
      subtle: shadowBase.withValues(alpha: isDarkMode ? 0.1 : 0.05),

      // ظل عادي
      normal: shadowBase.withValues(alpha: isDarkMode ? 0.2 : 0.1),

      // ظل قوي
      strong: shadowBase.withValues(alpha: isDarkMode ? 0.3 : 0.15),

      // ظل مرتفع
      elevated: shadowBase.withValues(alpha: isDarkMode ? 0.4 : 0.2),

      // ظل ملون (للعناصر المهمة)
      colored: primaryColor.withValues(alpha: isDarkMode ? 0.2 : 0.1),
    );
  }

  // ========== دوال مساعدة خاصة ==========

  static ColorLayer _createBackgroundLayer(HSLColor baseHsl, bool isDarkMode) {
    return ColorLayer(
      // ألوان مريحة للعين مثل VS Code
      primary: isDarkMode
          ? const Color(0xFF1E1E1E) // كحلي داكن مريح بدلاً من الأسود
          : const Color(0xFFFAFAFA),
      secondary: isDarkMode
          ? const Color(0xFF252526) // رمادي داكن دافئ
          : const Color(0xFFF5F5F5),
      tertiary: isDarkMode
          ? const Color(0xFF2D2D30) // رمادي داكن متوسط
          : const Color(0xFFEEEEEE),
    );
  }

  static ColorLayer _createSurfaceLayer(HSLColor baseHsl, bool isDarkMode) {
    final surfaceBase = isDarkMode
        ? HSLColor.fromAHSL(1.0, baseHsl.hue, 0.05, 0.08)
        : HSLColor.fromAHSL(1.0, baseHsl.hue, 0.02, 0.98);

    return ColorLayer(
      primary: surfaceBase.toColor(),
      secondary: isDarkMode
          ? surfaceBase.withLightness(0.12).toColor()
          : surfaceBase.withLightness(0.95).toColor(),
      tertiary: isDarkMode
          ? surfaceBase.withLightness(0.16).toColor()
          : surfaceBase.withLightness(0.92).toColor(),
    );
  }

  static ColorLayer _createContentLayer(
      HSLColor baseHsl, bool isDarkMode, double contrastRatio) {
    return ColorLayer(
      primary: isDarkMode ? const Color(0xFFFFFFFF) : const Color(0xFF000000),
      secondary: isDarkMode ? const Color(0xFFE0E0E0) : const Color(0xFF424242),
      tertiary: isDarkMode ? const Color(0xFFBDBDBD) : const Color(0xFF757575),
    );
  }

  static ColorLayer _createInteractionLayer(HSLColor baseHsl, bool isDarkMode) {
    return ColorLayer(
      primary: baseHsl.toColor(),
      secondary: baseHsl.withLightness(isDarkMode ? 0.7 : 0.4).toColor(),
      tertiary: baseHsl.withLightness(isDarkMode ? 0.8 : 0.3).toColor(),
    );
  }

  static ColorLayer _createEmphasisLayer(HSLColor baseHsl, bool isDarkMode) {
    final accentHue = (baseHsl.hue + 30) % 360;
    final accentHsl = HSLColor.fromAHSL(
        1.0, accentHue, baseHsl.saturation, baseHsl.lightness);

    return ColorLayer(
      primary: accentHsl.toColor(),
      secondary: accentHsl.withSaturation(0.7).toColor(),
      tertiary: accentHsl.withSaturation(0.5).toColor(),
    );
  }

  static ColorLayer _createStatusLayer(HSLColor baseHsl, bool isDarkMode) {
    return ColorLayer(
      primary: AppColors.success,
      secondary: AppColors.warning,
      tertiary: AppColors.error,
    );
  }

  static Color _getOptimalTextColor(
      Color backgroundColor, bool isDarkMode, TextEmphasis emphasis) {
    final bgLuminance = backgroundColor.computeLuminance();
    final isLightBg = bgLuminance > 0.5;

    Color baseColor = isLightBg ? Colors.black : Colors.white;

    switch (emphasis) {
      case TextEmphasis.high:
        return baseColor.withValues(alpha: 0.87);
      case TextEmphasis.medium:
        return baseColor.withValues(alpha: 0.60);
      case TextEmphasis.low:
        return baseColor.withValues(alpha: 0.38);
      case TextEmphasis.disabled:
        return baseColor.withValues(alpha: 0.26);
    }
  }

  static Color _getOptimalLinkColor(
      Color backgroundColor, Color primaryColor, bool isDarkMode) {
    final linkColor = isDarkMode
        ? HSLColor.fromColor(primaryColor).withLightness(0.7).toColor()
        : HSLColor.fromColor(primaryColor).withLightness(0.4).toColor();

    return AdvancedColorSystem.ensureAccessibleContrast(
        linkColor, backgroundColor);
  }

  static Color _getOptimalErrorColor(Color backgroundColor, bool isDarkMode) {
    final errorColor =
        isDarkMode ? const Color(0xFFFF6B6B) : const Color(0xFFD32F2F);
    return AdvancedColorSystem.ensureAccessibleContrast(
        errorColor, backgroundColor);
  }

  static Color _getOptimalSuccessColor(Color backgroundColor, bool isDarkMode) {
    final successColor =
        isDarkMode ? const Color(0xFF4CAF50) : const Color(0xFF2E7D32);
    return AdvancedColorSystem.ensureAccessibleContrast(
        successColor, backgroundColor);
  }

  static Color _getOptimalWarningColor(Color backgroundColor, bool isDarkMode) {
    final warningColor =
        isDarkMode ? const Color(0xFFFFB74D) : const Color(0xFFE65100);
    return AdvancedColorSystem.ensureAccessibleContrast(
        warningColor, backgroundColor);
  }

  static Color _adjustColorBrightness(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final newLightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(newLightness).toColor();
  }

  static double _calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();
    final lighter = math.max(luminance1, luminance2);
    final darker = math.min(luminance1, luminance2);
    return (lighter + 0.05) / (darker + 0.05);
  }
}

// ========== التعدادات والكلاسات المساعدة ==========

enum TextEmphasis { high, medium, low, disabled }

class ColorLayerSystem {
  final ColorLayer backgroundLayer;
  final ColorLayer surfaceLayer;
  final ColorLayer contentLayer;
  final ColorLayer interactionLayer;
  final ColorLayer emphasisLayer;
  final ColorLayer statusLayer;

  const ColorLayerSystem({
    required this.backgroundLayer,
    required this.surfaceLayer,
    required this.contentLayer,
    required this.interactionLayer,
    required this.emphasisLayer,
    required this.statusLayer,
  });
}

class ColorLayer {
  final Color primary;
  final Color secondary;
  final Color tertiary;

  const ColorLayer({
    required this.primary,
    required this.secondary,
    required this.tertiary,
  });
}

class ColorCompatibilityResult {
  final Color originalForeground;
  final Color originalBackground;
  final Color adjustedForeground;
  final Color adjustedBackground;
  final double contrastRatio;
  final bool isCompatible;
  final bool wasAdjusted;
  final double recommendedContrast;

  const ColorCompatibilityResult({
    required this.originalForeground,
    required this.originalBackground,
    required this.adjustedForeground,
    required this.adjustedBackground,
    required this.contrastRatio,
    required this.isCompatible,
    required this.wasAdjusted,
    required this.recommendedContrast,
  });
}

class TextColorPalette {
  final Color primary;
  final Color secondary;
  final Color tertiary;
  final Color disabled;
  final Color link;
  final Color error;
  final Color success;
  final Color warning;

  const TextColorPalette({
    required this.primary,
    required this.secondary,
    required this.tertiary,
    required this.disabled,
    required this.link,
    required this.error,
    required this.success,
    required this.warning,
  });
}

class ButtonColorPalette {
  final ButtonColorSet primary;
  final ButtonColorSet secondary;
  final ButtonColorSet text;
  final ButtonColorSet danger;
  final ButtonColorSet success;

  const ButtonColorPalette({
    required this.primary,
    required this.secondary,
    required this.text,
    required this.danger,
    required this.success,
  });
}

class ButtonColorSet {
  final Color background;
  final Color foreground;
  final Color border;
  final Color hover;
  final Color pressed;
  final Color disabled;

  const ButtonColorSet({
    required this.background,
    required this.foreground,
    required this.border,
    required this.hover,
    required this.pressed,
    required this.disabled,
  });
}

class BorderColorPalette {
  final Color subtle;
  final Color normal;
  final Color strong;
  final Color focus;
  final Color error;
  final Color success;
  final Color warning;

  const BorderColorPalette({
    required this.subtle,
    required this.normal,
    required this.strong,
    required this.focus,
    required this.error,
    required this.success,
    required this.warning,
  });
}

class ShadowColorPalette {
  final Color subtle;
  final Color normal;
  final Color strong;
  final Color elevated;
  final Color colored;

  const ShadowColorPalette({
    required this.subtle,
    required this.normal,
    required this.strong,
    required this.elevated,
    required this.colored,
  });
}
