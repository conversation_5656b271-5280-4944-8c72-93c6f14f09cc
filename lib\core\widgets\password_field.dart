import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/index.dart';
import '../../../core/theme/index.dart';

/// حقل إدخال كلمة المرور
class PasswordField extends StatefulWidget {
  /// وحدة التحكم في النص
  final TextEditingController controller;

  /// عنوان الحقل
  final String label;

  /// تلميح الحقل
  final String? hint;

  /// هل الحقل مطلوب
  final bool isRequired;

  /// دالة التحقق من صحة المدخلات
  final String? Function(String?)? validator;

  /// أيقونة الحقل
  final IconData? prefixIcon;

  /// دالة تنفذ عند تغيير القيمة
  final ValueChanged<String>? onChanged;

  /// دالة تنفذ عند الضغط على الحقل
  final VoidCallback? onTap;

  const PasswordField({
    Key? key,
    required this.controller,
    required this.label,
    this.hint,
    this.isRequired = false,
    this.validator,
    this.prefixIcon,
    this.onChanged,
    this.onTap,
  }) : super(key: key);

  @override
  State<PasswordField> createState() => _PasswordFieldState();
}

class _PasswordFieldState extends State<PasswordField> {
  bool _obscureText = true;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return TextFormField(
      controller: widget.controller,
      decoration: InputDecoration(
        labelText: widget.isRequired ? '${widget.label} *' : widget.label,
        hintText: widget.hint,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
        suffixIcon: IconButton(
          icon: Icon(
            _obscureText ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () {
            setState(() {
              _obscureText = !_obscureText;
            });
          },
        ),
        filled: true,
        fillColor: isDark
            ? AppColors.lightSurfaceVariant
            : AppColors.lightSurfaceVariant,
      ),
      keyboardType: TextInputType.visiblePassword,
      obscureText: _obscureText,
      inputFormatters: [
        FilteringTextInputFormatter.deny(RegExp(r'\s')),
      ],
      validator: widget.validator ??
          (widget.isRequired
              ? (value) => Validators.required('كلمة المرور')(value)
              : null),
      onChanged: widget.onChanged,
      onTap: widget.onTap,
      style: AppTypography(
        fontSize: Layout.getResponsiveFontSize(14),
      ),
    );
  }
}
