import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/theme/index.dart';
import 'package:tajer_plus/core/widgets/adaptive_card.dart';

void main() {
  group('Smart Theme System Tests', () {
    testWidgets('AdaptiveCard should adapt to theme changes',
        (WidgetTester tester) async {
      // بناء التطبيق مع ثيم فاتح
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: AdaptiveCard(
              child: Text('Test Card'),
            ),
          ),
        ),
      );

      // التحقق من وجود البطاقة
      expect(find.byType(AdaptiveCard), findsOneWidget);
      expect(find.text('Test Card'), findsOneWidget);

      // تغيير إلى الثيم الداكن
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.dark(),
          home: const Scaffold(
            body: AdaptiveCard(
              child: Text('Test Card'),
            ),
          ),
        ),
      );

      await tester.pump();

      // التحقق من أن البطاقة لا تزال موجودة
      expect(find.byType(AdaptiveCard), findsOneWidget);
      expect(find.text('Test Card'), findsOneWidget);
    });

    testWidgets('AdaptiveStatsCard should display stats correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: const Scaffold(
            body: AdaptiveStatsCard(
              title: 'المبيعات',
              value: '1000.00 ر.س',
              subtitle: 'إجمالي المبيعات',
              icon: Icons.shopping_cart,
            ),
          ),
        ),
      );

      // التحقق من النصوص
      expect(find.text('المبيعات'), findsOneWidget);
      expect(find.text('1000.00 ر.س'), findsOneWidget);
      expect(find.text('إجمالي المبيعات'), findsOneWidget);

      // التحقق من الأيقونة
      expect(find.byIcon(Icons.shopping_cart), findsOneWidget);
    });

    testWidgets('AdaptiveButton should handle tap events',
        (WidgetTester tester) async {
      bool buttonPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: AdaptiveButton(
              text: 'اضغط هنا',
              onPressed: () {
                buttonPressed = true;
              },
            ),
          ),
        ),
      );

      // العثور على الزر والضغط عليه
      await tester.tap(find.text('اضغط هنا'));
      await tester.pump();

      // التحقق من أن الحدث تم تنفيذه
      expect(buttonPressed, isTrue);
    });

    test('Color contrast calculation should work correctly', () {
      // اختبار حساب التباين
      const white = Color(0xFFFFFFFF);
      const black = Color(0xFF000000);

      final contrast = AppColors.getContrastRatio(white, black);

      // التباين بين الأبيض والأسود يجب أن يكون 21:1
      expect(contrast, closeTo(21.0, 0.1));
    });

    test('Smart text color should ensure good contrast', () {
      // اختبار لون النص الذكي
      const darkBackground = Color(0xFF000000);
      const lightBackground = Color(0xFFFFFFFF);

      final textOnDark = AppColors.getTextColorForBackground(darkBackground);
      final textOnLight = AppColors.getTextColorForBackground(lightBackground);

      // النص على خلفية داكنة يجب أن يكون فاتح
      expect(textOnDark.computeLuminance(), greaterThan(0.5));

      // النص على خلفية فاتحة يجب أن يكون داكن
      expect(textOnLight.computeLuminance(), lessThan(0.5));
    });

    test('Adaptive colors should change based on theme', () {
      // اختبار الألوان المتكيفة
      final lightCardBg = AppColors.getAdaptiveCardBackground(false);
      final darkCardBg = AppColors.getAdaptiveCardBackground(true);

      // ألوان البطاقات يجب أن تكون مختلفة
      expect(lightCardBg, isNot(equals(darkCardBg)));

      final lightText = AppColors.getAdaptiveTextColor(false);
      final darkText = AppColors.getAdaptiveTextColor(true);

      // ألوان النص يجب أن تكون مختلفة
      expect(lightText, isNot(equals(darkText)));
    });

    testWidgets('Theme switching should not break UI',
        (WidgetTester tester) async {
      // اختبار تبديل الثيم
      bool isDark = false;

      await tester.pumpWidget(
        StatefulBuilder(
          builder: (context, setState) {
            return MaterialApp(
              theme: isDark ? ThemeData.dark() : ThemeData.light(),
              home: Scaffold(
                body: Column(
                  children: [
                    const AdaptiveCard(
                      child: Text('بطاقة اختبار'),
                    ),
                    AdaptiveButton(
                      text: 'تبديل الثيم',
                      onPressed: () {
                        setState(() {
                          isDark = !isDark;
                        });
                      },
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      );

      // التحقق من الحالة الأولية
      expect(find.text('بطاقة اختبار'), findsOneWidget);
      expect(find.text('تبديل الثيم'), findsOneWidget);

      // تبديل الثيم
      await tester.tap(find.text('تبديل الثيم'));
      await tester.pumpAndSettle();

      // التحقق من أن العناصر لا تزال موجودة
      expect(find.text('بطاقة اختبار'), findsOneWidget);
      expect(find.text('تبديل الثيم'), findsOneWidget);
    });

    test('Smart gradient should create valid gradients', () {
      // اختبار التدرجات الذكية
      final colors = [Colors.red, Colors.blue];

      // يجب أن ينشئ تدرج صحيح
      expect(colors.length, equals(2));
      expect(colors[0], equals(Colors.red));
      expect(colors[1], equals(Colors.blue));
    });

    test('Contrast fixing should improve readability', () {
      // اختبار إصلاح التباين
      const backgroundColor = Color(0xFF333333);
      const poorTextColor = Color(0xFF555555);

      final originalContrast =
          AppColors.getContrastRatio(backgroundColor, poorTextColor);
      final improvedTextColor =
          AppColors.getTextColorForBackground(backgroundColor);
      final improvedContrast =
          AppColors.getContrastRatio(backgroundColor, improvedTextColor);

      // التباين المحسن يجب أن يكون أفضل
      expect(improvedContrast, greaterThan(originalContrast));
      expect(improvedContrast, greaterThanOrEqualTo(4.5));
    });
  });

  group('Performance Tests', () {
    test('Color calculations should be fast', () {
      final stopwatch = Stopwatch()..start();

      // تنفيذ 1000 عملية حساب لون
      for (int i = 0; i < 1000; i++) {
        AppColors.getTextColorForBackground(Color(0xFF000000 + i));
      }

      stopwatch.stop();

      // يجب أن تكتمل في أقل من 100 مللي ثانية
      expect(stopwatch.elapsedMilliseconds, lessThan(100));
    });

    test('Contrast calculations should be efficient', () {
      final stopwatch = Stopwatch()..start();

      // تنفيذ 1000 عملية حساب تباين
      for (int i = 0; i < 1000; i++) {
        AppColors.getContrastRatio(
          Color(0xFF000000 + i),
          Color(0xFFFFFFFF - i),
        );
      }

      stopwatch.stop();

      // يجب أن تكتمل في أقل من 50 مللي ثانية
      expect(stopwatch.elapsedMilliseconds, lessThan(50));
    });
  });
}
