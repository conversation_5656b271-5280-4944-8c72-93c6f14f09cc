import '../database/database_service.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';
import '../models/customer.dart';

/// Service for customer-related operations
class CustomerService {
  // Singleton pattern
  static final CustomerService _instance = CustomerService._internal();
  factory CustomerService() => _instance;
  CustomerService._internal();

  final DatabaseService _db = DatabaseService.instance;

  /// Get all customers
  Future<List<Customer>> getAllCustomers({
    bool includeInactive = false,
    String? searchQuery,
    String? type,
    int? limit,
    int? offset,
  }) async {
    try {
      AppLogger.info('Getting all customers');

      // Build where clause
      String whereClause = 'is_deleted = 0';
      List<dynamic> whereArgs = [];

      if (!includeInactive) {
        whereClause += ' AND is_active = 1';
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        whereClause += ' AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)';
        whereArgs
            .addAll(['%$searchQuery%', '%$searchQuery%', '%$searchQuery%']);
      }

      if (type != null && type.isNotEmpty) {
        whereClause += ' AND customer_type = ?';
        whereArgs.add(type);
      }

      // Query the database
      final List<Map<String, dynamic>> maps = await _db.query(
        DatabaseService.tableCustomers,
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'name ASC',
        limit: limit,
        offset: offset,
      );

      // Convert to Customer objects
      return List.generate(maps.length, (i) {
        return Customer.fromMap(maps[i]);
      });
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to get all customers',
        error: e,
        stackTrace: stackTrace,
      );
      return [];
    }
  }

  /// Get a customer by ID
  Future<Customer?> getCustomerById(String id) async {
    try {
      AppLogger.info('Getting customer by ID: $id');

      final List<Map<String, dynamic>> maps = await _db.query(
        DatabaseService.tableCustomers,
        where: 'id = ? AND is_deleted = 0',
        whereArgs: [id],
      );

      if (maps.isEmpty) {
        return null;
      }

      return Customer.fromMap(maps.first);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to get customer by ID',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return null;
    }
  }

  /// Add a new customer
  Future<bool> addCustomer(Customer customer, {String? userId}) async {
    try {
      AppLogger.info('Adding new customer: ${customer.name}');

      final customerMap = customer.toMap();

      // Set created_by if provided
      if (userId != null) {
        customerMap['created_by'] = userId;
      }

      await _db.insert(DatabaseService.tableCustomers, customerMap);

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to add customer',
        error: e,
        stackTrace: stackTrace,
        context: {'customer': customer.toString()},
      );
      return false;
    }
  }

  /// Update an existing customer
  Future<bool> updateCustomer(Customer customer, {String? userId}) async {
    try {
      AppLogger.info('Updating customer: ${customer.name}');

      final customerMap = customer.toMap();

      // Set updated_at and updated_by
      customerMap['updated_at'] = DateTime.now().toIso8601String();
      if (userId != null) {
        customerMap['updated_by'] = userId;
      }

      await _db.update(
        DatabaseService.tableCustomers,
        customerMap,
        where: 'id = ?',
        whereArgs: [customer.id],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to update customer',
        error: e,
        stackTrace: stackTrace,
        context: {'customer': customer.toString()},
      );
      return false;
    }
  }

  /// Delete a customer (soft delete)
  Future<bool> deleteCustomer(String id, {String? userId}) async {
    try {
      AppLogger.info('Deleting customer: $id');

      final now = DateTime.now().toIso8601String();

      await _db.update(
        DatabaseService.tableCustomers,
        {
          'is_deleted': 1,
          'updated_at': now,
          'updated_by': userId,
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to delete customer',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id},
      );
      return false;
    }
  }

  /// Get customer count
  Future<int> getCustomerCount({bool includeInactive = false}) async {
    try {
      AppLogger.info('Getting customer count');

      String whereClause = 'is_deleted = 0';
      if (!includeInactive) {
        whereClause += ' AND is_active = 1';
      }

      final result = await _db.rawQuery(
        'SELECT COUNT(*) as count FROM ${DatabaseService.tableCustomers} WHERE $whereClause',
      );

      return result.first['count'] as int;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to get customer count',
        error: e,
        stackTrace: stackTrace,
      );
      return 0;
    }
  }

  /// Update customer balance
  Future<bool> updateCustomerBalance(String id, double amount) async {
    try {
      AppLogger.info('Updating customer balance: $id, amount: $amount');

      // Get current balance
      final customer = await getCustomerById(id);
      if (customer == null) {
        return false;
      }

      // Calculate new balance
      final newBalance = customer.balance + amount;

      // Update balance
      await _db.update(
        DatabaseService.tableCustomers,
        {
          'balance': newBalance,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [id],
      );

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'Failed to update customer balance',
        error: e,
        stackTrace: stackTrace,
        context: {'id': id, 'amount': amount},
      );
      return false;
    }
  }
}
