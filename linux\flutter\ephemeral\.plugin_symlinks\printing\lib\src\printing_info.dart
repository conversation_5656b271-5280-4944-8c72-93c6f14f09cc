/*
 * Copyright (C) 2017, <PERSON> <<EMAIL>>
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/// Capabilities supported for the current platform
class PrintingInfo {
  /// Create an information object
  const PrintingInfo({
    this.directPrint = false,
    this.dynamicLayout = false,
    this.canPrint = false,
    this.canConvertHtml = false,
    this.canListPrinters = false,
    this.canShare = false,
    this.canRaster = false,
  });

  /// Create an information object from a dictionary
  factory PrintingInfo.fromMap(Map<dynamic, dynamic> map) => PrintingInfo(
        directPrint: map['directPrint'] ?? false,
        dynamicLayout: map['dynamicLayout'] ?? false,
        canPrint: map['canPrint'] ?? false,
        canConvertHtml: map['canConvertHtml'] ?? false,
        canListPrinters: map['canListPrinters'] ?? false,
        canShare: map['canShare'] ?? false,
        canRaster: map['canRaster'] ?? false,
      );

  /// Default information with no feature available
  static const PrintingInfo unavailable = PrintingInfo();

  /// The platform can print directly to a printer
  final bool directPrint;

  /// The platform can request a dynamic layout when the user change
  /// the printer or printer settings
  final bool dynamicLayout;

  /// The platform implementation is able to print a Pdf document
  final bool canPrint;

  /// The platform implementation is able to convert an html document to Pdf
  final bool canConvertHtml;

  /// The platform implementation is able list the available printers on the system
  final bool canListPrinters;

  /// The platform implementation is able to share a Pdf document
  /// to other applications
  final bool canShare;

  /// The platform implementation is able to convert pages from a Pdf document
  /// to a stream of images
  final bool canRaster;

  @override
  String toString() => '''
$runtimeType:
  canPrint: $canPrint
  directPrint: $directPrint
  dynamicLayout: $dynamicLayout
  canConvertHtml: $canConvertHtml
  canListPrinters: $canListPrinters
  canShare: $canShare
  canRaster: $canRaster''';
}
