import 'package:flutter/material.dart';
import 'index.dart';

/// ودجة اختبار لعرض نظام الثيم
class ThemeTestWidget extends StatelessWidget {
  const ThemeTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار نظام الثيم'),
        backgroundColor: DynamicColors.primary,
        foregroundColor: AppColors.onPrimary,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('الألوان الأساسية'),
            _buildColorRow('Primary', DynamicColors.primary),
            _buildColorRow('Primary Dark', DynamicColors.primaryDark),
            _buildColorRow('Primary Light', DynamicColors.primaryLight),
            _buildColorRow('Secondary', AppColors.secondary),
            _buildColorRow('Accent', AppColors.accent),
            _buildColorRow('Error', AppColors.error),
            _buildColorRow('Success', AppColors.success),
            _buildColorRow('Warning', AppColors.warning),
            _buildColorRow('Info', AppColors.info),
            const SizedBox(height: 24),
            _buildSectionTitle('ألوان الوحدات'),
            _buildModuleColors(),
            const SizedBox(height: 24),
            _buildSectionTitle('ألوان أنواع الحسابات'),
            _buildAccountTypeColors(),
            const SizedBox(height: 24),
            _buildSectionTitle('ألوان أنواع المعاملات'),
            _buildTransactionTypeColors(),
            const SizedBox(height: 24),
            _buildSectionTitle('ألوان مستويات الوصول'),
            _buildAccessLevelColors(),
            const SizedBox(height: 24),
            _buildSectionTitle('النظام الذكي - التدرجات'),
            _buildGradientExamples(),
            const SizedBox(height: 24),
            _buildSectionTitle('النظام الذكي - الظلال'),
            _buildShadowExamples(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const AppTypography(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.lightTextPrimary,
        ),
      ),
    );
  }

  Widget _buildColorRow(String name, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.lightSurfaceVariant),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const AppTypography(
                    fontWeight: FontWeight.w500,
                    color: AppColors.lightTextPrimary,
                  ),
                ),
                Text(
                  color.toString(),
                  style: const AppTypography(
                    fontSize: 12,
                    color: AppColors.lightTextSecondary,
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModuleColors() {
    final modules = [
      ('المصادقة', 'auth'),
      ('المستخدمين', 'users'),
      ('المنتجات', 'products'),
      ('المبيعات', 'sales'),
      ('المشتريات', 'purchases'),
      ('المخزون', 'inventory'),
      ('الحسابات', 'accounts'),
      ('التقارير', 'reports'),
      ('الإعدادات', 'settings'),
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: modules.map((module) {
        final color = AppColors.getModuleColor(module.$2);
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color),
          ),
          child: Text(
            module.$1,
            style: AppTypography(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAccountTypeColors() {
    final accountTypes = [
      ('أصول', 'asset'),
      ('خصوم', 'liability'),
      ('حقوق الملكية', 'equity'),
      ('إيرادات', 'revenue'),
      ('مصروفات', 'expense'),
      ('عملاء', 'customer'),
      ('موردين', 'supplier'),
      ('نقدية', 'cash'),
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: accountTypes.map((type) {
        final color = AppColors.getAccountTypeColor(type.$2);
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color),
          ),
          child: Text(
            type.$1,
            style: AppTypography(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildTransactionTypeColors() {
    final transactionTypes = [
      ('إيراد', 'income'),
      ('مصروف', 'expense'),
      ('تحويل', 'transfer'),
      ('مبيعات', 'sale'),
      ('مشتريات', 'purchase'),
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: transactionTypes.map((type) {
        final color = AppColors.getTransactionTypeColor(type.$2);
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color),
          ),
          child: Text(
            type.$1,
            style: AppTypography(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildAccessLevelColors() {
    final accessLevels = [
      ('لا يوجد', 'none'),
      ('عرض', 'view'),
      ('تعديل', 'edit'),
      ('كامل', 'full'),
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: accessLevels.map((level) {
        final color = AppColors.getAccessLevelColor(level.$2);
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color),
          ),
          child: Text(
            level.$1,
            style: AppTypography(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildGradientExamples() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 80,
            decoration: BoxDecoration(
              gradient: SmartThemeSystem.createBeautifulGradient(
                  DynamicColors.primary),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Text(
                'تدرج أساسي',
                style: AppTypography(
                  color: AppColors.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Container(
            height: 80,
            decoration: BoxDecoration(
              gradient: SmartThemeSystem.createBeautifulGradient(
                AppColors.success,
                secondaryColor: AppColors.info,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: Text(
                'تدرج مخصص',
                style: AppTypography(
                  color: AppColors.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShadowExamples() {
    return Builder(
      builder: (context) => Row(
        children: [
          Expanded(
            child: Container(
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.lightSurface,
                borderRadius: BorderRadius.circular(12),
                boxShadow:
                    SmartThemeSystem.getBeautifulCardShadow(elevation: 2),
              ),
              child: const Center(
                child: Text(
                  'ظل خفيف',
                  style: AppTypography(
                    color: AppColors.lightTextPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Container(
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.lightSurface,
                borderRadius: BorderRadius.circular(12),
                boxShadow:
                    SmartThemeSystem.getBeautifulCardShadow(elevation: 8),
              ),
              child: const Center(
                child: Text(
                  'ظل قوي',
                  style: AppTypography(
                    color: AppColors.lightTextPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
