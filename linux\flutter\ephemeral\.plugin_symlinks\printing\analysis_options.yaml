include: package:flutter_lints/flutter.yaml

analyzer:
  errors:
    missing_required_param: warning
    missing_return: warning
    public_member_api_docs: ignore
    todo: ignore

linter:
  rules:
    - always_declare_return_types
    - always_put_control_body_on_new_line
    - avoid_bool_literals_in_conditional_expressions
    - avoid_classes_with_only_static_members
    - avoid_field_initializers_in_const_classes
    - avoid_redundant_argument_values
    - avoid_slow_async_io
    - avoid_unused_constructor_parameters
    - avoid_void_async
    - await_only_futures
    - cancel_subscriptions
    - cast_nullable_to_non_nullable
    - directives_ordering
    - file_names
    - flutter_style_todos
    - leading_newlines_in_multiline_strings
    - no_adjacent_strings_in_list
    - no_default_cases
    - omit_local_variable_types
    - parameter_assignments
    - prefer_asserts_in_initializer_lists
    - prefer_const_constructors_in_immutables
    - prefer_final_fields
    - prefer_final_in_for_each
    - prefer_final_locals
    - prefer_foreach
    - prefer_generic_function_type_aliases
    - prefer_if_elements_to_conditional_expressions
    - prefer_if_null_operators
    - prefer_interpolation_to_compose_strings
    - prefer_null_aware_operators
    - prefer_relative_imports
    - prefer_single_quotes
    - prefer_typing_uninitialized_variables
    - public_member_api_docs
    - require_trailing_commas
    - sort_constructors_first
    - sort_pub_dependencies
    - sort_unnamed_constructors_first
    - test_types_in_equals
    - throw_in_finally
    - unnecessary_brace_in_string_interps
    - unnecessary_getters_setters
    - unnecessary_lambdas
    - unnecessary_null_checks
    - unnecessary_nullable_for_final_variable_declarations
    - unnecessary_overrides
    - unnecessary_parenthesis
    - unnecessary_statements
    - unnecessary_this
    - use_build_context_synchronously
    - use_function_type_syntax_for_parameters
    - use_late_for_private_fields_and_variables
    - use_named_constants
    - void_checks
