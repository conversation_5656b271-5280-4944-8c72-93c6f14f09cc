# تهيئة العملات الأساسية - تطبيق تاجر بلس

## 🎯 الهدف

إضافة دعم شامل للعملات المتعددة في تطبيق تاجر بلس مع التركيز على العملات الأساسية:

- **الريال اليمني (YER)** - العملة الافتراضية
- **الريال السعودي (SAR)**
- **الدولار الأمريكي (USD)**
- **اليورو (EUR)**
- **الجنيه المصري (EGP)**

## ✅ التحسينات المطبقة

### 1. تحديث جدول العملات

**الملف:** `lib/core/database/database_schema.dart`

#### الحقول المضافة

```sql
CREATE TABLE IF NOT EXISTS currencies (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  code TEXT NOT NULL UNIQUE,        -- رمز العملة الدولي (ISO 4217)
  symbol TEXT NOT NULL,             -- رمز العملة للعرض
  is_default INTEGER DEFAULT 0,     -- العملة الافتراضية
  exchange_rate REAL DEFAULT 1.0,   -- سعر الصرف
  country TEXT,                     -- اسم البلد
  country_code TEXT,                -- رمز البلد
  decimal_places INTEGER DEFAULT 2, -- عدد الخانات العشرية
  description TEXT,                 -- وصف العملة
  is_active INTEGER NOT NULL DEFAULT 1,
  created_at TEXT NOT NULL,
  updated_at TEXT,
  created_by TEXT,
  updated_by TEXT,
  is_deleted INTEGER NOT NULL DEFAULT 0
);
```

#### الفهارس المضافة

```sql
CREATE INDEX IF NOT EXISTS idx_currencies_code ON currencies (code);
CREATE INDEX IF NOT EXISTS idx_currencies_is_default ON currencies (is_default);
CREATE INDEX IF NOT EXISTS idx_currencies_is_active ON currencies (is_active);
CREATE INDEX IF NOT EXISTS idx_currencies_is_deleted ON currencies (is_deleted);
```

### 2. تهيئة العملات الأساسية

**الملف:** `lib/core/database/basic_data_initializer.dart`

#### العملات المهيئة

##### الريال اليمني (العملة الافتراضية)

```dart
{
  'name': 'ريال يمني',
  'code': 'YER',
  'symbol': 'ر.ي',
  'is_default': 1,
  'exchange_rate': 1.0,
  'country': 'اليمن',
  'country_code': 'YE',
  'decimal_places': 0,
  'description': 'الريال اليمني - العملة الرسمية لجمهورية اليمن',
}
```

##### الريال السعودي

```dart
{
  'name': 'ريال سعودي',
  'code': 'SAR',
  'symbol': 'ر.س',
  'is_default': 0,
  'exchange_rate': 0.133, // تقريباً 1 ريال يمني = 0.133 ريال سعودي
  'country': 'المملكة العربية السعودية',
  'country_code': 'SA',
  'decimal_places': 2,
  'description': 'الريال السعودي - العملة الرسمية للمملكة العربية السعودية',
}
```

##### الدولار الأمريكي

```dart
{
  'name': 'دولار أمريكي',
  'code': 'USD',
  'symbol': '$',
  'is_default': 0,
  'exchange_rate': 0.004, // تقريباً 1 ريال يمني = 0.004 دولار أمريكي
  'country': 'الولايات المتحدة الأمريكية',
  'country_code': 'US',
  'decimal_places': 2,
  'description': 'الدولار الأمريكي - العملة الاحتياطية العالمية',
}
```

##### اليورو

```dart
{
  'name': 'يورو',
  'code': 'EUR',
  'symbol': '€',
  'is_default': 0,
  'exchange_rate': 0.0037, // تقريباً 1 ريال يمني = 0.0037 يورو
  'country': 'الاتحاد الأوروبي',
  'country_code': 'EU',
  'decimal_places': 2,
  'description': 'اليورو - العملة الموحدة للاتحاد الأوروبي',
}
```

##### الجنيه المصري

```dart
{
  'name': 'جنيه مصري',
  'code': 'EGP',
  'symbol': 'ج.م',
  'is_default': 0,
  'exchange_rate': 0.123, // تقريباً 1 ريال يمني = 0.123 جنيه مصري
  'country': 'جمهورية مصر العربية',
  'country_code': 'EG',
  'decimal_places': 2,
  'description': 'الجنيه المصري - العملة الرسمية لجمهورية مصر العربية',
}
```

### 3. تحديث الإعدادات الأساسية

تم تحديث الإعدادات لتعكس الريال اليمني كعملة افتراضية:

```dart
{
  'key': 'currency_code',
  'value': 'YER',
  'description': 'رمز العملة الافتراضية',
},
{
  'key': 'currency_name',
  'value': 'ريال يمني',
  'description': 'اسم العملة الافتراضية',
},
{
  'key': 'currency_symbol',
  'value': 'ر.ي',
  'description': 'رمز العملة للعرض',
}
```

### 4. تحديث طرق الدفع

تم تحديث جميع طرق الدفع لتستخدم الريال اليمني كعملة افتراضية:

```dart
'currency': 'YER', // بدلاً من 'SAR'
```

### 5. تحديث بيانات الفرع الافتراضي

تم تحديث metadata للفرع الافتراضي:

```dart
'metadata': '{"timezone": "Asia/Aden", "currency": "YER", "language": "ar", "country": "Yemen"}'
```

## 🔄 تسلسل التهيئة المحدث

1. **تهيئة بيانات المصادقة** (الأدوار، الصلاحيات، مجموعات المستخدمين، المستخدم الافتراضي)
2. **تهيئة وحدات القياس الأساسية**
3. **تهيئة المخزن الافتراضي**
4. **تهيئة طرق الدفع الأساسية** (بالريال اليمني)
5. **تهيئة الإعدادات الأساسية** (بالريال اليمني كعملة افتراضية)
6. **تهيئة الفرع الافتراضي** (مع بيانات يمنية)
7. **ربط العلاقات** (المستخدم ↔ الفرع ↔ المخزن)
8. **تهيئة العملات الأساسية** ⭐ **جديد**

## 📊 أسعار الصرف المستخدمة

| العملة | الرمز | سعر الصرف مقابل الريال اليمني | ملاحظات |
|--------|------|---------------------------|---------|
| ريال يمني | YER | 1.0 | العملة الأساسية |
| ريال سعودي | SAR | 0.133 | تقريبي |
| دولار أمريكي | USD | 0.004 | تقريبي |
| يورو | EUR | 0.0037 | تقريبي |
| جنيه مصري | EGP | 0.123 | تقريبي |

> **ملاحظة:** أسعار الصرف المستخدمة هي أسعار تقريبية لأغراض التهيئة الأولية. يجب تحديثها بانتظام من مصادر موثوقة.

## 🎯 الفوائد المحققة

### دعم متعدد العملات

- إمكانية التعامل مع 5 عملات أساسية
- نظام تحويل عملات مرن
- دعم الخانات العشرية المختلفة

### سهولة الاستخدام

- الريال اليمني كعملة افتراضية
- رموز عملات واضحة ومفهومة
- أوصاف شاملة لكل عملة

### قابلية التوسع

- هيكل مرن لإضافة عملات جديدة
- نظام فهرسة محسن للأداء
- دعم البيانات الوصفية للعملات

## 🚀 الخطوات التالية

1. **اختبار تهيئة العملات:** التأكد من إنشاء جميع العملات بنجاح
2. **تحديث واجهات المستخدم:** إضافة قوائم اختيار العملات
3. **تطبيق تحويل العملات:** في الفواتير والتقارير
4. **إضافة خدمة تحديث أسعار الصرف:** من مصادر خارجية
5. **اختبار العمليات المالية:** بعملات مختلفة
