import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_typography.dart';

/// مجموعة من المكونات المخصصة الجذابة لتطبيق تاجر بلس
class CustomWidgets {
  CustomWidgets._();

  /// بطاقة جذابة مع تدرج لوني وظل
  static Widget gradientCard({
    required Widget child,
    List<Color>? gradientColors,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double elevation = 4,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: AppColors.createGradient(
          gradientColors ?? AppColors.primaryGradient,
        ),
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        boxShadow: AppColors.createSoftShadow(
          blurRadius: elevation * 2,
          opacity: 0.15,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? BorderRadius.circular(16),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(16),
            child: child,
          ),
        ),
      ),
    );
  }

  /// بطاقة إحصائية جذابة
  static Widget statisticsCard({
    required String title,
    required String value,
    required IconData icon,
    Color? color,
    Color? backgroundColor,
    VoidCallback? onTap,
  }) {
    final cardColor = color ?? AppColors.primary;
    final bgColor = backgroundColor ?? cardColor.withValues(alpha: 0.1);

    return gradientCard(
      gradientColors: [bgColor, bgColor.darken(0.05)],
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: cardColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: cardColor,
                  size: 24,
                ),
              ),
              if (onTap != null)
                Icon(
                  Icons.arrow_forward_ios,
                  color: cardColor.withValues(alpha: 0.7),
                  size: 16,
                ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            value,
            style: AppTypography.statisticsStyle.copyWith(color: cardColor),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
              color: cardColor.withValues(alpha: 0.8),
              fontWeight: AppTypography.weightMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// زر جذاب مع تدرج لوني
  static Widget gradientButton({
    required String text,
    required VoidCallback onPressed,
    List<Color>? gradientColors,
    IconData? icon,
    double? width,
    double height = 48,
    BorderRadius? borderRadius,
    AppTypography? textStyle,
    bool isLoading = false,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: AppColors.createGradient(
          gradientColors ?? AppColors.primaryGradient,
        ),
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        boxShadow: AppColors.createSoftShadow(
          color: gradientColors?.first ?? AppColors.primary,
          blurRadius: 8,
          opacity: 0.3,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        child: InkWell(
          onTap: isLoading ? null : onPressed,
          borderRadius: borderRadius ?? BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isLoading)
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.onPrimary),
                    ),
                  )
                else ...[
                  if (icon != null) ...[
                    Icon(icon, color: AppColors.onPrimary, size: 20),
                    const SizedBox(width: 8),
                  ],
                  Text(
                    text,
                    style: textStyle ??
                        AppTypography.lightTextTheme.labelLarge?.copyWith(
                          color: AppColors.onPrimary,
                          fontWeight: AppTypography.weightMedium,
                        ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// حقل إدخال جذاب مع تأثيرات
  static Widget customTextField({
    required String label,
    String? hint,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixTap,
    TextEditingController? controller,
    TextInputType? keyboardType,
    bool obscureText = false,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    void Function(String)? onSubmitted,
    bool enabled = true,
    int maxLines = 1,
    Color? focusColor,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTypography.lightTextTheme.labelMedium?.copyWith(
            fontWeight: AppTypography.weightMedium,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          obscureText: obscureText,
          validator: validator,
          onChanged: onChanged,
          onFieldSubmitted: onSubmitted,
          enabled: enabled,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: prefixIcon != null
                ? Icon(prefixIcon, color: focusColor ?? AppColors.primary)
                : null,
            suffixIcon: suffixIcon != null
                ? IconButton(
                    icon: Icon(suffixIcon),
                    onPressed: onSuffixTap,
                    color: focusColor ?? AppColors.primary,
                  )
                : null,
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: focusColor ?? AppColors.primary,
                width: 2,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// بطاقة قائمة جذابة
  static Widget customListTile({
    required String title,
    String? subtitle,
    IconData? leadingIcon,
    Widget? trailing,
    VoidCallback? onTap,
    Color? iconColor,
    Color? backgroundColor,
    bool showDivider = true,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.lightSurface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppColors.createSoftShadow(opacity: 0.05),
      ),
      child: Column(
        children: [
          ListTile(
            leading: leadingIcon != null
                ? Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: (iconColor ?? AppColors.primary)
                          .withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      leadingIcon,
                      color: iconColor ?? AppColors.primary,
                      size: 20,
                    ),
                  )
                : null,
            title: Text(
              title,
              style: AppTypography.lightTextTheme.bodyLarge?.copyWith(
                fontWeight: AppTypography.weightMedium,
              ),
            ),
            subtitle: subtitle != null
                ? Text(
                    subtitle,
                    style: AppTypography.lightTextTheme.bodySmall,
                  )
                : null,
            trailing: trailing ??
                (onTap != null
                    ? const Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color: AppColors.lightTextHint,
                      )
                    : null),
            onTap: onTap,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          if (showDivider)
            Divider(
              height: 1,
              indent: leadingIcon != null ? 72 : 16,
              endIndent: 16,
              color: AppColors.lightDivider,
            ),
        ],
      ),
    );
  }

  /// شريط تطبيق مخصص مع تدرج
  static PreferredSizeWidget customAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    List<Color>? gradientColors,
    double elevation = 0,
    VoidCallback? onBackPressed,
  }) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Container(
        decoration: BoxDecoration(
          gradient: AppColors.createGradient(
            gradientColors ?? AppColors.primaryGradient,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: elevation > 0
              ? AppColors.createSoftShadow(
                  color: gradientColors?.first ?? AppColors.primary,
                  blurRadius: elevation * 2,
                  opacity: 0.2,
                )
              : null,
        ),
        child: AppBar(
          title: Text(
            title,
            style: AppTypography.lightTextTheme.headlineMedium?.copyWith(
              color: AppColors.onPrimary,
              fontWeight: AppTypography.weightSemiBold,
            ),
          ),
          centerTitle: centerTitle,
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: leading ??
              (onBackPressed != null
                  ? IconButton(
                      icon: const Icon(Icons.arrow_back,
                          color: AppColors.onPrimary),
                      onPressed: onBackPressed,
                    )
                  : null),
          actions: actions,
          iconTheme: const IconThemeData(color: AppColors.onPrimary),
          actionsIconTheme: const IconThemeData(color: AppColors.onPrimary),
        ),
      ),
    );
  }

  /// مؤشر تحميل جذاب
  static Widget customLoadingIndicator({
    String? message,
    Color? color,
    double size = 40,
  }) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? AppColors.primary,
              ),
            ),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message,
              style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
                color: color ?? AppColors.primary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// رسالة فارغة جذابة
  static Widget emptyStateWidget({
    required String title,
    String? subtitle,
    IconData? icon,
    String? buttonText,
    VoidCallback? onButtonPressed,
    Color? color,
  }) {
    final stateColor = color ?? AppColors.lightTextSecondary;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: stateColor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 64,
                  color: stateColor,
                ),
              ),
              const SizedBox(height: 24),
            ],
            Text(
              title,
              style: AppTypography.lightTextTheme.headlineSmall?.copyWith(
                color: stateColor,
                fontWeight: AppTypography.weightMedium,
              ),
              textAlign: TextAlign.center,
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle,
                style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
                  color: stateColor.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (buttonText != null && onButtonPressed != null) ...[
              const SizedBox(height: 24),
              gradientButton(
                text: buttonText,
                onPressed: onButtonPressed,
                gradientColors: [stateColor, stateColor.darken(0.1)],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
