import 'package:flutter/material.dart';
import '../../../core/theme/index.dart';
import '../../../core/utils/index.dart';
import '../../../core/constants/app_constants.dart';

import 'package:tajer_plus/core/widgets/custom_button.dart';
import 'package:tajer_plus/core/widgets/data_table_widget.dart';
import 'package:tajer_plus/core/widgets/search_field.dart' as search;
import 'package:tajer_plus/core/models/employee.dart';
import 'package:tajer_plus/features/employees/screens/employee_form_screen.dart';

/// شاشة عرض قائمة الموظفين
class EmployeesScreen extends StatefulWidget {
  const EmployeesScreen({Key? key}) : super(key: key);

  @override
  State<EmployeesScreen> createState() => _EmployeesScreenState();
}

class _EmployeesScreenState extends State<EmployeesScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Employee> _employees = [];
  List<Employee> _filteredEmployees = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadEmployees();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// تحميل بيانات الموظفين
  Future<void> _loadEmployees() async {
    // هنا سيتم استدعاء خدمة جلب بيانات الموظفين
    // مثال:
    // final employeeService = EmployeeService();
    // final employees = await employeeService.getEmployees();

    // بيانات تجريبية للعرض
    await Future.delayed(const Duration(seconds: 1));
    final dummyEmployees = [
      Employee(
        id: '1',
        employeeId: 'EMP001',
        fullName: 'أحمد محمد',
        firstName: 'أحمد',
        lastName: 'محمد',
        email: '<EMAIL>',
        phone: '0123456789',
        position: 'مدير مبيعات',
        department: 'المبيعات',
        basicSalary: 5000,
        hireDate: DateTime.now().subtract(const Duration(days: 365)),
        status: 'active',
      ),
      Employee(
        id: '2',
        employeeId: 'EMP002',
        fullName: 'سارة أحمد',
        firstName: 'سارة',
        lastName: 'أحمد',
        email: '<EMAIL>',
        phone: '0123456788',
        position: 'محاسب',
        department: 'المالية',
        basicSalary: 4500,
        hireDate: DateTime.now().subtract(const Duration(days: 180)),
        status: 'active',
      ),
      Employee(
        id: '3',
        employeeId: 'EMP003',
        fullName: 'محمد علي',
        firstName: 'محمد',
        lastName: 'علي',
        email: '<EMAIL>',
        phone: '0123456787',
        position: 'مندوب مبيعات',
        department: 'المبيعات',
        basicSalary: 3500,
        hireDate: DateTime.now().subtract(const Duration(days: 90)),
        status: 'on_leave',
      ),
    ];

    setState(() {
      _employees = dummyEmployees;
      _filteredEmployees = dummyEmployees;
      _isLoading = false;
    });
  }

  /// البحث في قائمة الموظفين
  void _searchEmployees(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredEmployees = _employees;
      });
      return;
    }

    final filtered = _employees.where((employee) {
      final name = employee.fullName.toLowerCase();
      final number = employee.employeeId.toLowerCase();
      final position = employee.position?.toLowerCase() ?? '';
      final department = employee.department?.toLowerCase() ?? '';
      final searchLower = query.toLowerCase();

      return name.contains(searchLower) ||
          number.contains(searchLower) ||
          position.contains(searchLower) ||
          department.contains(searchLower);
    }).toList();

    setState(() {
      _filteredEmployees = filtered;
    });
  }

  /// حذف موظف
  Future<void> _deleteEmployee(String id) async {
    // هنا سيتم استدعاء خدمة حذف الموظف
    // مثال:
    // final employeeService = EmployeeService();
    // await employeeService.deleteEmployee(id);

    setState(() {
      _employees.removeWhere((employee) => employee.id == id);
      _filteredEmployees.removeWhere((employee) => employee.id == id);
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم حذف الموظف بنجاح')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الموظفين'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadEmployees();
            },
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const EmployeeFormScreen(),
            ),
          ).then((_) => _loadEmployees());
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          _buildSearchAndFilter(),
          const SizedBox(height: 16),
          Expanded(
            child: _buildEmployeesTable(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Row(
      children: [
        Expanded(
          child: search.SearchField(
            controller: _searchController,
            hintText: 'بحث عن موظف...',
            onChanged: _searchEmployees,
          ),
        ),
        const SizedBox(width: 16),
        CustomButton(
          text: 'إضافة موظف',
          icon: Icons.add,
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const EmployeeFormScreen(),
              ),
            ).then((_) => _loadEmployees());
          },
        ),
      ],
    );
  }

  Widget _buildEmployeesTable() {
    if (_filteredEmployees.isEmpty) {
      return const Center(
        child: Text(
          'لا يوجد موظفين',
          style: AppTypography(fontSize: 18),
        ),
      );
    }

    return Layout.isDesktop() || Layout.isTablet()
        ? _buildDesktopTable()
        : _buildMobileList();
  }

  Widget _buildDesktopTable() {
    return DataTableWidget<Employee>(
      columns: const [
        DataColumn(label: Text('رقم الموظف')),
        DataColumn(label: Text('الاسم')),
        DataColumn(label: Text('القسم')),
        DataColumn(label: Text('المنصب')),
        DataColumn(label: Text('الراتب')),
        DataColumn(label: Text('الحالة')),
        DataColumn(label: Text('إجراءات')),
      ],
      items: _filteredEmployees,
      rowBuilder: (employee) {
        return DataRow(
          cells: [
            DataCell(Text(employee.employeeId)),
            DataCell(Text(employee.fullName)),
            DataCell(Text(employee.department ?? '')),
            DataCell(Text(employee.position ?? '')),
            DataCell(
                Text('${employee.basicSalary} ${AppConstants.appCurrency}')),
            DataCell(_buildStatusChip(employee.status)),
            DataCell(_buildActionButtons(employee)),
          ],
        );
      },
    );
  }

  Widget _buildMobileList() {
    return ListView.builder(
      itemCount: _filteredEmployees.length,
      itemBuilder: (context, index) {
        final employee = _filteredEmployees[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            title: Text(
              employee.fullName,
              style: const AppTypography(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('رقم الموظف: ${employee.employeeId}'),
                Text('القسم: ${employee.department ?? ''}'),
                Text('المنصب: ${employee.position ?? ''}'),
                Text(
                    'الراتب: ${employee.basicSalary} ${AppConstants.appCurrency}'),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatusChip(employee.status),
                IconButton(
                  icon: const Icon(Icons.edit, color: AppColors.info),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) =>
                            EmployeeFormScreen(employee: employee),
                      ),
                    ).then((_) => _loadEmployees());
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: AppColors.error),
                  onPressed: () => _showDeleteConfirmation(employee),
                ),
              ],
            ),
            isThreeLine: true,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => EmployeeFormScreen(employee: employee),
                ),
              ).then((_) => _loadEmployees());
            },
          ),
        );
      },
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String text;

    switch (status) {
      case 'active':
        color = AppColors.success;
        text = 'نشط';
        break;
      case 'inactive':
        color = AppColors.error;
        text = 'غير نشط';
        break;
      case 'on_leave':
        color = AppColors.warning;
        text = 'في إجازة';
        break;
      case 'terminated':
        color = AppColors.error;
        text = 'منتهي';
        break;
      default:
        color = AppColors.secondary;
        text = 'غير معروف';
        break;
    }

    return Chip(
      label: Text(
        text,
        style: const AppTypography(color: AppColors.onPrimary, fontSize: 12),
      ),
      backgroundColor: color,
      padding: const EdgeInsets.all(4),
    );
  }

  Widget _buildActionButtons(Employee employee) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: const Icon(Icons.edit, color: AppColors.info),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => EmployeeFormScreen(employee: employee),
              ),
            ).then((_) => _loadEmployees());
          },
        ),
        IconButton(
          icon: const Icon(Icons.delete, color: AppColors.error),
          onPressed: () => _showDeleteConfirmation(employee),
        ),
      ],
    );
  }

  void _showDeleteConfirmation(Employee employee) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الموظف ${employee.fullName}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteEmployee(employee.id);
            },
            child:
                const Text('حذف', style: AppTypography(color: AppColors.error)),
          ),
        ],
      ),
    );
  }
}
