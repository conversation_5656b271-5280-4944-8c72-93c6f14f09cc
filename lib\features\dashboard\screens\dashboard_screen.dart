import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import '../presenters/dashboard_presenter.dart';
import '../../shared/widgets/custom_card.dart';
import '../../shared/widgets/loading_indicator.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/widgets/app_drawer.dart';
import '../../../core/theme/index.dart'; // استخدام الثيم الموحد
import '../../../core/widgets/adaptive_card.dart'; // البطاقات الذكية الجديدة
import '../../../core/widgets/enhanced_ui_components.dart'; // المكونات المحسنة
// import '../../../core/utils/app_logger.dart';
// import '../../../core/utils/error_tracker.dart';
import '../widgets/dashboard_header.dart';
import '../widgets/animated_chart.dart';

/// شاشة لوحة المعلومات التفاعلية
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  late DashboardPresenter _dashboardPresenter;
  String _selectedPeriod = 'month';
  String _selectedChartType = 'sales';

  @override
  void initState() {
    super.initState();
    _dashboardPresenter =
        Provider.of<DashboardPresenter>(context, listen: false);
    // استخدام ميكانيكية آمنة لتحميل البيانات بعد بناء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadData();
      }
    });
  }

  Future<void> _loadData() async {
    await _dashboardPresenter.loadDashboardData(period: _selectedPeriod);
  }

  // تم تعليق هذه الدالة لأنها غير مستخدمة
  /*
  Future<void> _reloadDataOnError() async {
    // تجنب إعادة المحاولة المتكررة باستخدام متغير للتحكم
    if (_isReloading) return;
    _isReloading = true;

    try {
      // تسجيل الخطأ في سجل الأخطاء للمطورين
      AppLogger.error(
          'خطأ في تحميل بيانات لوحة المعلومات: ${_dashboardPresenter.error}');
      ErrorTracker.captureError(
        'فشل في تحميل بيانات لوحة المعلومات',
        error: _dashboardPresenter.error ?? 'خطأ غير معروف',
        stackTrace: StackTrace.current,
        context: {'screen': 'dashboard', 'period': _selectedPeriod},
      );

      // إعادة تحميل البيانات مرة واحدة فقط
      await _loadData();
    } catch (e) {
      // التحقق من أن الشاشة لا تزال مرتبطة بشجرة العناصر
      if (!mounted) return;

      // تسجيل الخطأ الجديد
      AppLogger.error('فشل في إعادة تحميل بيانات لوحة المعلومات: $e');

      // عرض رسالة خطأ صغيرة بدلاً من مربع حوار كامل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              const Text('تعذر تحميل بعض البيانات. سيتم عرض البيانات المتاحة.'),
          duration: const Duration(seconds: 3),
          action: SnackBarAction(
            label: 'إعادة المحاولة',
            onPressed: () {
              _isReloading = false; // السماح بإعادة المحاولة عند الضغط على الزر
              _loadData();
            },
          ),
        ),
      );
    } finally {
      // إعادة تعيين متغير التحكم بعد الانتهاء
      _isReloading = false;
    }
  }
  */

  // المؤشر الحالي للشريط السفلي
  int _currentIndex = 0;
  // متغير للتحكم في إعادة تحميل البيانات لمنع التكرار
  bool _isReloading = false;

  @override
  Widget build(BuildContext context) {
    // تحديد العنوان بناءً على المؤشر الحالي (لا نستخدمه حاليًا لأننا نستخدم رأس مخصص)
    switch (_currentIndex) {
      case 0:
        // الرئيسية
        break;
      case 1:
        // المبيعات
        break;
      case 2:
        // المخزون
        break;
      case 3:
        // التقارير
        break;
      default:
      // الرئيسية
    }

    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      // إضافة القائمة الجانبية
      drawer: const AppDrawer(),
      body: Consumer<DashboardPresenter>(
        builder: (context, presenter, child) {
          if (presenter.isLoading) {
            return Stack(
              children: [
                // خلفية متدرجة عصرية باستخدام الألوان الجديدة
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: isDarkMode
                          ? AppColors.darkGradient
                          : [
                              AppColors.lightBackground,
                              AppColors.lightSurfaceVariant,
                            ],
                    ),
                  ),
                ),
                const Center(child: LoadingIndicator()),
              ],
            );
          }

          // عرض الشاشة العادية حتى مع وجود خطأ
          // نعرض رسالة خطأ فقط دون محاولة إعادة التحميل التلقائي لتجنب الحلقة المتكررة
          if (presenter.error != null && !_isReloading) {
            // عرض رسالة خطأ صغيرة بدلاً من إعادة التحميل التلقائي
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text(
                        'تعذر تحميل بعض البيانات. اسحب للأسفل لإعادة المحاولة.'),
                    duration: const Duration(seconds: 3),
                    action: SnackBarAction(
                      label: 'إعادة المحاولة',
                      onPressed: () {
                        _isReloading = false;
                        _loadData();
                      },
                    ),
                  ),
                );
              }
            });
          }

          return RefreshIndicator(
            onRefresh: _loadData,
            child: Stack(
              children: [
                // خلفية متدرجة عصرية باستخدام الألوان الجديدة
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: isDarkMode
                          ? AppColors.darkGradient
                          : [
                              AppColors.lightBackground,
                              AppColors.lightSurfaceVariant,
                            ],
                    ),
                  ),
                ),

                // محتوى الشاشة
                CustomScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  slivers: [
                    // رأس الشاشة
                    SliverAppBar(
                      expandedHeight: 120,
                      floating: false,
                      pinned: true,
                      elevation: 0,
                      backgroundColor: Colors.transparent,
                      // إضافة زر القائمة الجانبية بألوان عصرية
                      leading: IconButton(
                        icon: const Icon(
                          Icons.menu,
                          // color: AppColors.primary,
                          size: 28,
                        ),
                        onPressed: () {
                          // فتح القائمة الجانبية
                          Scaffold.of(context).openDrawer();
                        },
                        tooltip: 'القائمة الرئيسية',
                      ),
                      flexibleSpace: FlexibleSpaceBar(
                        background: DashboardHeader(
                          userName: 'مدير النظام',
                          welcomeTitle: 'مرحباً بك في',
                          welcomeMessage: 'نظام تاجر بلس المحاسبي',
                          onUserTap: () {
                            Navigator.pushNamed(context, '/profile');
                          },
                          onNotificationTap: () {
                            Navigator.pushNamed(context, '/notifications');
                          },
                          unreadNotifications: 3,
                          backgroundColor:
                              AppColors.primaryLight.withValues(alpha: 0.1),
                          textColor: DynamicColors.textPrimary(context),
                        ),
                      ),
                    ),

                    // محتوى اللوحة - استخدام النظام الموحد
                    SliverPadding(
                      padding: AppDimensions.screenPaddingMedium,
                      sliver: SliverList(
                        delegate: SliverChildListDelegate([
                          // محدد الفترة الزمنية
                          _buildPeriodSelector(),
                          const SizedBox(height: AppDimensions.spacing24),

                          // بطاقات الملخص
                          _buildEnhancedSummaryCards(presenter),
                          const SizedBox(height: AppDimensions.spacing24),

                          // الوصول السريع
                          _buildEnhancedQuickAccess(),
                          const SizedBox(height: AppDimensions.spacing24),

                          // محدد نوع الرسم البياني
                          _buildEnhancedChartSelector(),
                          const SizedBox(height: AppDimensions.spacing16),

                          // الرسم البياني
                          _buildEnhancedChart(presenter),
                          const SizedBox(height: AppDimensions.spacing24),

                          // أكثر المنتجات مبيعاً
                          _buildEnhancedTopProducts(presenter),
                          const SizedBox(height: AppDimensions.spacing24),

                          // المنتجات منخفضة المخزون
                          _buildEnhancedLowStockProducts(presenter),
                          const SizedBox(height: AppDimensions.spacing24),

                          // آخر العمليات
                          _buildEnhancedRecentTransactions(presenter),
                          const SizedBox(
                              height: AppDimensions.spacing64 +
                                  AppDimensions
                                      .spacing16), // مساحة إضافية في الأسفل
                        ]),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: _buildEnhancedBottomNavigationBar(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder: (context) => _buildQuickActionsSheet(context),
          );
        },
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        elevation: 8,
        child: const Icon(Icons.add),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildPeriodSelector() {
    final theme = Theme.of(context);

    return CustomCard(
      elevation: 4,
      borderRadius: 20,
      shadowColor: AppColors.primary.withValues(alpha: 0.2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.date_range, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                'الفترة الزمنية',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: DynamicColors.textPrimary(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: DynamicColors.surfaceVariant(context),
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.all(8),
            child: Row(
              children: [
                _buildEnhancedPeriodButton('اليوم', 'day', Icons.today),
                _buildEnhancedPeriodButton('الأسبوع', 'week', Icons.view_week),
                _buildEnhancedPeriodButton(
                    'الشهر', 'month', Icons.calendar_month),
                _buildEnhancedPeriodButton(
                    'السنة', 'year', Icons.calendar_today),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedPeriodButton(String label, String value, IconData icon) {
    final isSelected = _selectedPeriod == value;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedPeriod = value;
          });
          _loadData();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.primary : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: isSelected
                    ? AppColors.onPrimary
                    : DynamicColors.textSecondary(context),
                size: 20,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: AppTypography(
                  color: isSelected
                      ? AppColors.onPrimary
                      : DynamicColors.textPrimary(context),
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقات الملخص المحسنة مع الثيمات الديناميكية العصرية
  Widget _buildEnhancedSummaryCards(DashboardPresenter presenter) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع أيقونة ديناميكية
        Padding(
          padding: const EdgeInsets.only(bottom: 20.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: DynamicColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.analytics_outlined,
                  color: DynamicColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'ملخص الأداء',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: DynamicColors.textPrimary(context),
                ),
              ),
            ],
          ),
        ),

        // الصف الأول - المبيعات والمشتريات
        Row(
          children: [
            Expanded(
              child: EnhancedUIComponents.statsCard(
                title: 'المبيعات',
                value: '${presenter.totalSales.toStringAsFixed(2)} ر.س',
                subtitle: 'إجمالي المبيعات',
                icon: Icons.shopping_cart_outlined,
                context: context,
                customColor: AppColors.moduleSales,
                onTap: () => Navigator.pushNamed(context, '/sales'),
                showTrend: true,
                trendValue: 12.5,
                isPositiveTrend: true,
              ),
            ),
            const SizedBox(width: AppDimensions.spacing16),
            Expanded(
              child: _buildModernStatsCard(
                title: 'المشتريات',
                value: '${presenter.totalPurchases.toStringAsFixed(2)} ر.س',
                subtitle: 'إجمالي المشتريات',
                icon: Icons.shopping_bag_outlined,
                gradientColors: [
                  AppColors.modulePurchases,
                  AppColors.modulePurchases.withValues(alpha: 0.7),
                ],
                onTap: () => Navigator.pushNamed(context, '/purchases'),
                isDarkMode: isDarkMode,
              ),
            ),
          ],
        ),

        const SizedBox(height: AppDimensions.spacing16),

        // الصف الثاني - الأرباح والفواتير
        Row(
          children: [
            Expanded(
              child: _buildModernStatsCard(
                title: 'الأرباح',
                value: '${presenter.totalProfit.toStringAsFixed(2)} ر.س',
                subtitle: 'صافي الأرباح',
                icon: Icons.trending_up_outlined,
                gradientColors: presenter.totalProfit >= 0
                    ? [
                        AppColors.success,
                        AppColors.success.withValues(alpha: 0.7)
                      ]
                    : [AppColors.error, AppColors.error.withValues(alpha: 0.7)],
                onTap: () => Navigator.pushNamed(context, '/financial-reports'),
                isDarkMode: isDarkMode,
                showTrend: true,
                trendValue: 12.5,
                isProfit: true,
                profitValue: presenter.totalProfit,
              ),
            ),
            const SizedBox(width: AppDimensions.spacing16),
            Expanded(
              child: _buildModernStatsCard(
                title: 'الفواتير',
                value: presenter.invoiceCount.toString(),
                subtitle: 'عدد الفواتير',
                icon: Icons.receipt_long_outlined,
                gradientColors: [
                  AppColors.moduleAccounts,
                  AppColors.moduleAccounts.withValues(alpha: 0.7),
                ],
                onTap: () => Navigator.pushNamed(context, '/invoices'),
                isDarkMode: isDarkMode,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائيات عصرية مع تدرجات لونية وتأثيرات بصرية
  Widget _buildModernStatsCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required List<Color> gradientColors,
    required VoidCallback onTap,
    required bool isDarkMode,
    bool showTrend = false,
    double? trendValue,
    bool isProfit = false,
    double? profitValue,
  }) {
    return SizedBox(
      height: 130,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: isDarkMode
                    ? [
                        gradientColors[0].withValues(alpha: 0.3),
                        gradientColors[1].withValues(alpha: 0.1),
                      ]
                    : gradientColors,
              ),
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              boxShadow: [
                BoxShadow(
                  color: gradientColors[0]
                      .withValues(alpha: isDarkMode ? 0.1 : 0.2),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            padding: AppDimensions.cardPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // الصف العلوي - الأيقونة والسهم
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.white
                            .withValues(alpha: isDarkMode ? 0.1 : 0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Icon(
                        icon,
                        color: Colors.white,
                        size: 26,
                      ),
                    ),
                    if (showTrend && trendValue != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              trendValue >= 0
                                  ? Icons.trending_up
                                  : Icons.trending_down,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${trendValue.abs().toStringAsFixed(1)}%',
                              style: AppTypography.lightTextTheme.bodySmall
                                  ?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      )
                    else
                      Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white.withValues(alpha: 0.7),
                        size: 18,
                      ),
                  ],
                ),

                // الصف السفلي - القيم والنصوص
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      value,
                      style:
                          AppTypography.lightTextTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: AppTypography.weightBold,
                        fontSize: 20,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            subtitle,
                            style: AppTypography.lightTextTheme.bodySmall
                                ?.copyWith(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (isProfit && profitValue != null)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              profitValue >= 0 ? 'ربح' : 'خسارة',
                              style: AppTypography.lightTextTheme.bodySmall
                                  ?.copyWith(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء قسم الوصول السريع المحسن مع تصميم عصري
  Widget _buildEnhancedQuickAccess() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع أيقونة ديناميكية
        Padding(
          padding: const EdgeInsets.only(bottom: 20.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: DynamicColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.flash_on_outlined,
                  color: DynamicColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'الوصول السريع',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: DynamicColors.textPrimary(context),
                ),
              ),
            ],
          ),
        ),

        // بطاقة الوصول السريع مع تصميم محسن
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isDarkMode ? AppColors.darkSurface : AppColors.lightSurface,
            borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
            boxShadow: [
              BoxShadow(
                color: (isDarkMode ? Colors.black : Colors.grey)
                    .withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: _buildQuickAccessButton(
                      title: 'بيع جديد',
                      icon: Icons.shopping_cart_outlined,
                      color: AppColors.moduleSales,
                      onTap: () => Navigator.pushNamed(context, '/sale-form'),
                      isDarkMode: isDarkMode,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildQuickAccessButton(
                      title: 'إضافة منتج',
                      icon: Icons.inventory_2_outlined,
                      color: AppColors.moduleProducts,
                      onTap: () =>
                          Navigator.pushNamed(context, '/product-form'),
                      isDarkMode: isDarkMode,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildQuickAccessButton(
                      title: 'إضافة عميل',
                      icon: Icons.person_add_outlined,
                      color: AppColors.moduleUsers,
                      onTap: () =>
                          Navigator.pushNamed(context, '/customer-form'),
                      isDarkMode: isDarkMode,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildQuickAccessButton(
                      title: 'المخزون',
                      icon: Icons.warehouse_outlined,
                      color: AppColors.moduleInventory,
                      onTap: () => Navigator.pushNamed(context, '/products'),
                      isDarkMode: isDarkMode,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء زر الوصول السريع مع تصميم عصري
  Widget _buildQuickAccessButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required bool isDarkMode,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withValues(alpha: isDarkMode ? 0.1 : 0.05),
            borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
            border: Border.all(
              color: color.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: isDarkMode ? 0.2 : 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 28,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
                  color: isDarkMode
                      ? AppColors.darkTextPrimary
                      : AppColors.lightTextPrimary,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء شاشة الإجراءات السريعة
  Widget _buildQuickActionsSheet(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.lightTextSecondary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'إجراءات سريعة',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAction(
                'فاتورة بيع',
                Icons.receipt,
                AppColors.info,
                () => Navigator.pushNamed(context, '/sale-form'),
              ),
              _buildQuickAction(
                'فاتورة شراء',
                Icons.shopping_bag,
                AppColors.success,
                () => Navigator.pushNamed(context, '/purchase-form'),
              ),
              _buildQuickAction(
                'إضافة منتج',
                Icons.inventory,
                AppColors.accent,
                () => Navigator.pushNamed(context, '/product-form'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAction(
                'إضافة عميل',
                Icons.person_add,
                AppColors.warning,
                () => Navigator.pushNamed(context, '/customer-form'),
              ),
              _buildQuickAction(
                'إضافة مورد',
                Icons.business,
                AppColors.teal,
                () => Navigator.pushNamed(context, '/supplier-form'),
              ),
              _buildQuickAction(
                'مصروفات',
                Icons.money_off,
                AppColors.error,
                () => Navigator.pushNamed(context, AppRoutes.expenses),
              ),
            ],
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  /// بناء إجراء سريع
  Widget _buildQuickAction(
      String title, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: AppTypography(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء شريط التنقل السفلي المحسن
  Widget _buildEnhancedBottomNavigationBar() {
    return BottomAppBar(
      shape: const CircularNotchedRectangle(),
      notchMargin: 8,
      elevation: 8,
      child: Container(
        height: 60,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildBottomNavItem(
              'الرئيسية',
              Icons.dashboard,
              0,
            ),
            _buildBottomNavItem(
              'المبيعات',
              Icons.shopping_cart,
              1,
            ),
            const SizedBox(width: 40), // مساحة للزر العائم
            _buildBottomNavItem(
              'المخزون',
              Icons.inventory,
              2,
            ),
            _buildBottomNavItem(
              'التقارير',
              Icons.bar_chart,
              3,
            ),
          ],
        ),
      ),
    );
  }

///////////////////////////////////////////////////////////
  /// بناء قسم أكثر المنتجات مبيعاً المحسن
  Widget _buildEnhancedTopProducts(DashboardPresenter presenter) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final topProducts = presenter.topProducts;

    return AdaptiveCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.trending_up, color: AppColors.accent),
              const SizedBox(width: 8),
              Text(
                'أكثر المنتجات مبيعاً',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.getAdaptiveTextColor(isDark),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          topProducts.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(24.0),
                    child: Column(
                      children: [
                        Icon(
                          Icons.inventory_2,
                          size: 48,
                          color: AppColors.lightTextSecondary,
                          //AppColors.getAdaptiveSecondaryTextColor(isDark),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'لا توجد بيانات للمنتجات الأكثر مبيعاً',
                          style: AppTypography(
                            color: AppColors.lightTextSecondary,
                            // AppColors.getAdaptiveSecondaryTextColor(isDark),

                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: math.min(5, topProducts.length),
                  separatorBuilder: (context, index) => const Divider(),
                  itemBuilder: (context, index) {
                    final product = topProducts[index];
                    return ListTile(
                      leading: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: AppColors.accent.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Text(
                            '${index + 1}',
                            style: const AppTypography(
                              color: AppColors.lightTextSecondary,
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                          ),
                        ),
                      ),
                      title: Text(
                        product['name'] ?? '',
                        style: AppTypography(
                          fontWeight: FontWeight.bold,
                          color: AppColors.getAdaptiveTextColor(isDark),
                        ),
                      ),
                      subtitle: Text(
                        'الكمية المباعة: ${product['quantity']}',
                        style: AppTypography(
                          color:
                              AppColors.getAdaptiveSecondaryTextColor(isDark),
                          fontSize: 12,
                        ),
                      ),
                      trailing: Text(
                        '${product['total']} ر.س',
                        style: const AppTypography(
                          fontWeight: FontWeight.bold,
                          color: AppColors.lightTextSecondary,
                        ),
                      ),
                      onTap: () {
                        // الانتقال إلى صفحة تفاصيل المنتج
                        if (product['id'] != null) {
                          Navigator.pushNamed(
                            context,
                            '/product-details',
                            arguments: product['id'],
                          );
                        }
                      },
                    );
                  },
                ),
          if (topProducts.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Center(
                child: TextButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(context, '/products');
                  },
                  icon: const Icon(Icons.arrow_forward),
                  label: const Text('عرض جميع المنتجات'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.accent,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

//////////////////////////////////////////////////
  /// بناء قسم المنتجات منخفضة المخزون المحسن
  Widget _buildEnhancedLowStockProducts(DashboardPresenter presenter) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final lowStockProducts = presenter.lowStockProducts;

    return AdaptiveCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.warning_amber, color: AppColors.warning),
              const SizedBox(width: 8),
              Text(
                'منتجات منخفضة المخزون',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.getAdaptiveTextColor(isDark),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          lowStockProducts.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(24.0),
                    child: Column(
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: 48,
                          color: AppColors.lightTextSecondary,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'لا توجد منتجات منخفضة المخزون',
                          style: AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: math.min(5, lowStockProducts.length),
                  separatorBuilder: (context, index) => const Divider(),
                  itemBuilder: (context, index) {
                    final product = lowStockProducts[index];
                    final stockLevel = (product['quantity'] ?? 0) /
                        (product['reorder_level'] ?? 1);

                    return ListTile(
                      leading: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: AppColors.warning.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.inventory,
                            color: AppColors.lightTextSecondary,
                          ),
                        ),
                      ),
                      title: Text(
                        product['name'] ?? '',
                        style: const AppTypography(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'المخزون الحالي: ${product['quantity']}',
                            style: const AppTypography(
                              color: AppColors.lightTextSecondary,
                              fontSize: 12,
                            ),
                          ),
                          const SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: stockLevel.clamp(0.0, 1.0),
                            backgroundColor: AppColors.lightSurfaceVariant,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              stockLevel < 0.3
                                  ? AppColors.error
                                  : stockLevel < 0.7
                                      ? AppColors.warning
                                      : AppColors.success,
                            ),
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ],
                      ),
                      trailing: Text(
                        'الحد الأدنى: ${product['reorder_level']}',
                        style: const AppTypography(
                          fontWeight: FontWeight.bold,
                          color: AppColors.lightTextSecondary,
                        ),
                      ),
                      onTap: () {
                        // الانتقال إلى صفحة تفاصيل المنتج
                        if (product['id'] != null) {
                          Navigator.pushNamed(
                            context,
                            '/product-details',
                            arguments: product['id'],
                          );
                        }
                      },
                    );
                  },
                ),
          if (lowStockProducts.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Center(
                child: TextButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(context, '/purchase-form');
                  },
                  icon: const Icon(Icons.add_shopping_cart),
                  label: const Text('إنشاء طلب شراء'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.warning,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء قسم آخر العمليات المحسن
  Widget _buildEnhancedRecentTransactions(DashboardPresenter presenter) {
    final theme = Theme.of(context);
    final recentTransactions = presenter.recentTransactions;

    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.receipt_long, color: theme.primaryColor),
              const SizedBox(width: 8),
              Text(
                'آخر العمليات',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          recentTransactions.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(24.0),
                    child: Column(
                      children: [
                        Icon(
                          Icons.receipt,
                          size: 48,
                          color: AppColors.lightTextSecondary,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'لا توجد عمليات حديثة',
                          style: AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: math.min(5, recentTransactions.length),
                  separatorBuilder: (context, index) => const Divider(),
                  itemBuilder: (context, index) {
                    final transaction = recentTransactions[index];
                    final isInvoice = transaction['type'] == 'invoice';
                    final isPurchase = transaction['type'] == 'purchase';
                    final isExpense = transaction['type'] == 'expense';

                    IconData icon;
                    Color color;
                    String title;

                    if (isInvoice) {
                      icon = Icons.receipt;
                      color = AppColors.info;
                      title = 'فاتورة بيع';
                    } else if (isPurchase) {
                      icon = Icons.shopping_bag;
                      color = AppColors.success;
                      title = 'فاتورة شراء';
                    } else if (isExpense) {
                      icon = Icons.money_off;
                      color = AppColors.error;
                      title = 'مصروفات';
                    } else {
                      icon = Icons.swap_horiz;
                      color = AppColors.accent;
                      title = 'عملية أخرى';
                    }

                    return ListTile(
                      leading: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Center(
                          child: Icon(icon, color: color),
                        ),
                      ),
                      title: Flexible(
                        child: Row(
                          children: [
                            Flexible(
                              child: Text(
                                title,
                                style: const AppTypography(
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: color.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '#${transaction['number'] ?? ''}',
                                style: AppTypography(
                                  color: color,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            transaction['date'] ?? '',
                            style: const AppTypography(
                              color: AppColors.lightTextSecondary,
                              fontSize: 12,
                            ),
                          ),
                          if (transaction['customer_name'] != null)
                            Text(
                              'العميل: ${transaction['customer_name']}',
                              style: const AppTypography(
                                color: AppColors.lightTextSecondary,
                                fontSize: 12,
                              ),
                            ),
                          if (transaction['supplier_name'] != null)
                            Text(
                              'المورد: ${transaction['supplier_name']}',
                              style: const AppTypography(
                                color: AppColors.lightTextSecondary,
                                fontSize: 12,
                              ),
                            ),
                        ],
                      ),
                      trailing: Text(
                        '${transaction['total']} ر.س',
                        style: AppTypography(
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      onTap: () {
                        // الانتقال إلى صفحة تفاصيل العملية
                        if (transaction['id'] != null) {
                          if (isInvoice) {
                            Navigator.pushNamed(
                              context,
                              '/invoice-details',
                              arguments: transaction['id'],
                            );
                          } else if (isPurchase) {
                            Navigator.pushNamed(
                              context,
                              '/purchase-details',
                              arguments: transaction['id'],
                            );
                          } else if (isExpense) {
                            Navigator.pushNamed(
                              context,
                              '/expense-details',
                              arguments: transaction['id'],
                            );
                          }
                        }
                      },
                    );
                  },
                ),
          if (recentTransactions.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Center(
                child: TextButton.icon(
                  onPressed: () {
                    Navigator.pushNamed(context, '/transactions');
                  },
                  icon: const Icon(Icons.arrow_forward),
                  label: const Text('عرض جميع العمليات'),
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.info,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء عنصر في شريط التنقل السفلي
  Widget _buildBottomNavItem(String label, IconData icon, int index) {
    final isSelected = _currentIndex == index;
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    return InkWell(
      onTap: () {
        setState(() {
          _currentIndex = index;
        });

        // التنقل إلى الشاشات المختلفة بناءً على المؤشر
        switch (index) {
          case 1: // المبيعات
            Navigator.pushNamed(context, AppRoutes.sales);
            break;
          case 2: // المخزون
            Navigator.pushNamed(context, AppRoutes.products);
            break;
          case 3: // التقارير
            Navigator.pushNamed(context, AppRoutes.reports);
            break;
        }
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(
            horizontal: 8, vertical: 4), // تقليل الحشو
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              flex: 0,
              child: Icon(
                icon,
                color: isSelected ? primaryColor : AppColors.lightTextSecondary,
                size: 18, // تقليل حجم الأيقونة أكثر
              ),
            ),
            const SizedBox(height: 1), // تقليل المسافة أكثر
            Flexible(
              flex: 1,
              child: Text(
                label,
                style: AppTypography(
                  color:
                      isSelected ? primaryColor : AppColors.lightTextSecondary,
                  fontSize: 9, // تقليل حجم الخط أكثر
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  height: 0.9, // تقليل ارتفاع السطر أكثر
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء محدد نوع الرسم البياني المحسن
  Widget _buildEnhancedChartSelector() {
    final theme = Theme.of(context);
    // final primaryColor = theme.primaryColor;
    // final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withValues(alpha: 0.1),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              //Icon(Icons.bar_chart, color: primaryColor),
              // Icon(Icons.speed, color: DynamicColors.primary),
              Icon(
                Icons.bar_chart,
                color: DynamicColors.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'نوع الرسم البياني',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            decoration: BoxDecoration(
              color: AppColors.lightTextSecondary.withValues(alpha: 0.4),
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.all(4),
            child: Row(
              children: [
                _buildEnhancedChartTypeButton(
                    'المبيعات', 'sales', Icons.shopping_cart),
                _buildEnhancedChartTypeButton(
                    'المشتريات', 'purchases', Icons.shopping_bag),
                _buildEnhancedChartTypeButton(
                    'الأرباح', 'profit', Icons.trending_up),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر نوع الرسم البياني المحسن
  Widget _buildEnhancedChartTypeButton(
      String label, String value, IconData icon) {
    final isSelected = _selectedChartType == value;
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedChartType = value;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 4),
          decoration: BoxDecoration(
            color: isSelected ? primaryColor : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected
                    ? AppColors.primary
                    : AppColors.lightTextSecondary,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: AppTypography(
                  color: isSelected
                      ? AppColors.onPrimary
                      : AppColors.lightSurfaceVariant,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء الرسم البياني المحسن
  Widget _buildEnhancedChart(DashboardPresenter presenter) {
    final chartData = _getChartData(presenter);
    final xLabels = _getChartLabels(presenter);
    final theme = Theme.of(context);
    //final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        //color: AppColors.lightTextSecondary,

        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.lightTextSecondary.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(_getChartIcon(), color: _getChartColor()),
                  const SizedBox(width: 8),
                  Text(
                    _getChartTitle(),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getChartColor().withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 16,
                      color: _getChartColor(),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _getPeriodText(),
                      style: AppTypography(
                        color: _getChartColor(),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          chartData.isEmpty
              ? const SizedBox(
                  height: 300,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.bar_chart,
                          size: 64,
                          color: AppColors.lightTextSecondary,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'لا توجد بيانات للعرض',
                          style: AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'جرب تغيير الفترة الزمنية أو نوع الرسم البياني',
                          style: AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontSize: 15,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : AnimatedBarChart(
                  data: chartData,
                  xLabels: xLabels,
                  color: _getChartColor(),
                  height: 300,
                  barWidth: 20,
                  topRadius: 8,
                  valueFormatter: (value) => '${value.toStringAsFixed(2)} ر.س',
                ),
          if (chartData.isNotEmpty) ...[
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildChartStat(
                  'المتوسط',
                  _calculateAverage(chartData),
                  Icons.calculate,
                  AppColors.info,
                ),
                const SizedBox(width: 16),
                _buildChartStat(
                  'الإجمالي',
                  _calculateTotal(chartData),
                  Icons.summarize,
                  AppColors.success,
                ),
                const SizedBox(width: 16),
                _buildChartStat(
                  'الأعلى',
                  _calculateMax(chartData),
                  Icons.trending_up,
                  AppColors.accent,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// بناء إحصائية للرسم البياني
  Widget _buildChartStat(
      String label, double value, IconData icon, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 16),
            const SizedBox(height: 4),
            Text(
              label,
              style: AppTypography(
                color: color,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${value.toStringAsFixed(2)} ر.س',
              style: AppTypography(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// حساب متوسط البيانات
  double _calculateAverage(List<double> data) {
    if (data.isEmpty) return 0;
    final sum = data.reduce((a, b) => a + b);
    return sum / data.length;
  }

  /// حساب إجمالي البيانات
  double _calculateTotal(List<double> data) {
    if (data.isEmpty) return 0;
    return data.reduce((a, b) => a + b);
  }

  /// حساب أقصى قيمة
  double _calculateMax(List<double> data) {
    if (data.isEmpty) return 0;
    return data.reduce((a, b) => a > b ? a : b);
  }

  /// الحصول على نص الفترة الزمنية
  String _getPeriodText() {
    switch (_selectedPeriod) {
      case 'day':
        return 'اليوم';
      case 'week':
        return 'الأسبوع';
      case 'month':
        return 'الشهر';
      case 'year':
        return 'السنة';
      default:
        return 'الشهر';
    }
  }

  /// الحصول على أيقونة الرسم البياني
  IconData _getChartIcon() {
    switch (_selectedChartType) {
      case 'sales':
        return Icons.shopping_cart;
      case 'purchases':
        return Icons.shopping_bag;
      case 'profit':
        return Icons.trending_up;
      default:
        return Icons.bar_chart;
    }
  }

  /// الحصول على عناوين المحور الأفقي
  List<String> _getChartLabels(DashboardPresenter presenter) {
    final count = presenter.salesData.length;
    final labels = <String>[];

    for (int i = 0; i < count; i++) {
      labels.add(_getChartLabel(i));
    }

    return labels;
  }

  List<double> _getChartData(DashboardPresenter presenter) {
    switch (_selectedChartType) {
      case 'sales':
        return presenter.salesData;
      case 'purchases':
        return presenter.purchasesData;
      case 'profit':
        return presenter.profitData;
      default:
        return presenter.salesData;
    }
  }

  String _getChartTitle() {
    switch (_selectedChartType) {
      case 'sales':
        return 'المبيعات';
      case 'purchases':
        return 'المشتريات';
      case 'profit':
        return 'الأرباح';
      default:
        return 'المبيعات';
    }
  }

  Color _getChartColor() {
    switch (_selectedChartType) {
      case 'sales':
        return AppColors.info;
      case 'purchases':
        return AppColors.success;
      case 'profit':
        return AppColors.accent;
      default:
        return AppColors.info;
    }
  }

  String _getChartLabel(int index) {
    switch (_selectedPeriod) {
      case 'day':
        return '${index * 2}:00';
      case 'week':
        final days = [
          'الأحد',
          'الإثنين',
          'الثلاثاء',
          'الأربعاء',
          'الخميس',
          'الجمعة',
          'السبت'
        ];
        return days[index % 7];
      case 'month':
        return '${index + 1}';
      case 'year':
        final months = [
          'يناير',
          'فبراير',
          'مارس',
          'أبريل',
          'مايو',
          'يونيو',
          'يوليو',
          'أغسطس',
          'سبتمبر',
          'أكتوبر',
          'نوفمبر',
          'ديسمبر'
        ];
        return months[index % 12];
      default:
        return index.toString();
    }
  }

  // ignore: unused_element
  double _getMaxY(List<double> data) {
    if (data.isEmpty) return 0;
    return data.reduce((value, element) => value > element ? value : element);
  }

  // ignore: unused_element
  Widget _buildTopProducts(DashboardPresenter presenter) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أكثر المنتجات مبيعاً',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 16),
          presenter.topProducts.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text('لا توجد بيانات للعرض'),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: presenter.topProducts.length,
                  itemBuilder: (context, index) {
                    final product = presenter.topProducts[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor: AppColors.infoLight,
                        child: Text(
                          '${index + 1}',
                          style: const AppTypography(
                            color: AppColors.lightTextSecondary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(product['name'] ?? ''),
                      subtitle: Text('الكمية: ${product['quantity']}'),
                      trailing: Text(
                        '${product['total'].toStringAsFixed(2)} ر.س',
                        style: const AppTypography(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    );
                  },
                ),
        ],
      ),
    );
  }

  // ignore: unused_element
  Widget _buildLowStockProducts(DashboardPresenter presenter) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المنتجات منخفضة المخزون',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
            ///////////////////////
          ),
          const SizedBox(height: 16),
          presenter.lowStockProducts.isEmpty
              ? const Center(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text('لا توجد منتجات منخفضة المخزون'),
                  ),
                )
              : ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: presenter.lowStockProducts.length,
                  itemBuilder: (context, index) {
                    final product = presenter.lowStockProducts[index];
                    return ListTile(
                      leading: const CircleAvatar(
                        backgroundColor: AppColors.error,
                        child: Icon(
                          Icons.warning,
                          color: AppColors.lightTextSecondary,
                        ),
                        /////////////////////
                      ),
                      title: Text(product['name'] ?? ''),
                      subtitle: Text(
                        'المخزون الحالي: ${product['quantity']} | الحد الأدنى: ${product['minStock']}',
                      ),
                      trailing: ElevatedButton(
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            '/product-form',
                            arguments: product['id'],
                          );
                        },
                        child: const Text('تعديل'),
                      ),
                    );
                  },
                ),
        ],
      ),
    );
  }

  /// الحصول على لون العملية بناءً على نوعها
  // ignore: unused_element
  Color _getTransactionColor(String? type) {
    switch (type) {
      case 'sale':
        return AppColors.success;
      case 'purchase':
        return AppColors.info;
      case 'expense':
        return AppColors.error;
      case 'income':
        return AppColors.accent;
      default:
        return AppColors.secondary;
    }
  }

  /// الحصول على أيقونة العملية بناءً على نوعها
  // ignore: unused_element
  IconData _getTransactionIcon(String? type) {
    switch (type) {
      case 'sale':
        return Icons.shopping_cart;
      case 'purchase':
        return Icons.shopping_bag;
      case 'expense':
        return Icons.money_off;
      case 'income':
        return Icons.attach_money;
      default:
        return Icons.receipt;
    }
  }

  /// بناء شريط التنقل السفلي
  // ignore: unused_element
  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });

        // التنقل إلى الشاشات المختلفة بناءً على المؤشر
        switch (index) {
          case 1: // المبيعات
            Navigator.pushNamed(context, AppRoutes.sales);
            break;
          case 2: // المخزون
            Navigator.pushNamed(context, AppRoutes.products);
            break;
          case 3: // التقارير
            Navigator.pushNamed(context, AppRoutes.financialReports);
            break;
        }
      },
      type: BottomNavigationBarType.fixed,
      backgroundColor:
          Theme.of(context).bottomNavigationBarTheme.backgroundColor ??
              AppColors.lightSurface,
      selectedItemColor: DynamicColors.primary,
      unselectedItemColor: Theme.of(context).unselectedWidgetColor,
      selectedLabelStyle: const AppTypography(fontWeight: FontWeight.bold),
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.dashboard),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.shopping_cart),
          label: 'المبيعات',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.inventory),
          label: 'المخزون',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.bar_chart),
          label: 'التقارير',
        ),
      ],
    );
  }
}
