import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

// استيراد BasePresenter الموحد
import 'base_presenter.dart';
import '../utils/app_logger.dart';

// استيراد جميع الـ Presenters
import '../../features/products/presenters/product_presenter.dart';
import '../../features/categories/presenters/category_presenter.dart';
import '../../features/units/presenters/unit_presenter.dart';
import '../../features/customers/presenters/customer_presenter.dart';
import '../../features/sales/presenters/sale_presenter.dart';
import '../../features/suppliers/presenters/supplier_presenter.dart';
import '../../features/accounts/presenters/account_presenter.dart';
import '../../features/purchases/presenters/purchase_presenter.dart';
import '../../features/warehouses/presenters/warehouse_presenter.dart';
import '../../features/expenses/presenters/expense_presenter.dart';
import '../../features/warehouses/presenters/inventory_transfer_presenter.dart';
import '../../features/warehouses/presenters/inventory_adjustment_presenter.dart';
import '../../features/warehouses/presenters/inventory_presenter.dart';
import '../../features/simple_ties/presenters/simple_tie_presenter.dart';
import '../../features/pos/presenters/pos_presenter.dart';
import '../../features/promotions/presenter/promotion_presenter.dart';
import '../../features/vouchers/presenters/voucher_presenter.dart';
import '../../features/currencies/presenters/currency_presenter.dart';
import '../../features/settings/presenters/settings_presenter.dart';
import '../../features/users/presenters/user_presenter.dart';
import '../../features/users/presenters/permission_presenter.dart';
import '../../features/users/presenters/role_presenter.dart';
import '../../features/accounts/presenters/journal_entry_presenter.dart';
import '../../features/dashboard/presenters/dashboard_presenter.dart';
import '../../features/reports/presenters/reports_presenter.dart';
import '../../../core/theme/index.dart';

/// 🏗️ مدير موحد لجميع الـ Providers في التطبيق مع دعم التحميل الكسول
///
/// هذا الكلاس يدير جميع الـ Presenters المطلوبة لشاشات التطبيق المختلفة:
///
/// 📱 **الشاشات المدعومة:**
/// - 🏠 شاشة لوحة المعلومات (Dashboard)
/// - 🔐 شاشات إدارة المستخدمين والصلاحيات
/// - 💰 شاشات النظام المحاسبي والمالي
/// - 🛍️ شاشات المبيعات والمشتريات
/// - 📦 شاشات إدارة المخزون والمنتجات
/// - 💸 شاشات المصروفات والعمليات المالية
/// - 📊 شاشات التقارير والإحصائيات
/// - ⚙️ شاشات الإعدادات والتكوين
///
/// يساعد في تنظيم وإدارة الـ providers بشكل أفضل وتحسين الأداء
class AppProviders {
  /// 🔧 الحصول على قائمة الـ providers الأساسية فقط
  /// هذه الـ providers ضرورية لبدء تشغيل التطبيق وتشمل:
  /// - مدير الثيم (Theme Manager)
  /// - مقدم لوحة المعلومات (Dashboard Presenter)
  /// - مقدم الإعدادات (Settings Presenter)
  static List<SingleChildWidget> getCoreProviders() {
    return [
      // 🎨 مدير الثيم - ضروري لجميع شاشات التطبيق
      ChangeNotifierProvider(create: (_) => themeManager),

      // 🏠 مقدم لوحة المعلومات - الشاشة الرئيسية
      ChangeNotifierProvider(create: (_) => DashboardPresenter()),

      // ⚙️ مقدم الإعدادات - شاشة الإعدادات العامة
      ChangeNotifierProvider(create: (_) => SettingsPresenter()),
    ];
  }

  /// 🔄 الحصول على الـ providers الكسولة - يتم تحميلها عند الحاجة فقط
  ///
  /// هذه الدالة تحتوي على الـ Presenters التي يتم تحميلها بشكل كسول لتحسين الأداء.
  /// يتم استخدام LazyPresenterManager لإدارة دورة حياة هذه الـ Presenters.
  ///
  /// ⚠️ **ملاحظة:** هذه الدالة مخصصة للاستخدام المتقدم فقط.
  /// للاستخدام العادي، استخدم getMinimalProviders() بدلاً من ذلك.
  static List<SingleChildWidget> getLazyProviders() {
    return [
      // تحميل كسول للـ presenters الثانوية
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<ProductPresenter>(
          () => ProductPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<CategoryPresenter>(
          () => CategoryPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<UnitPresenter>(
          () => UnitPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<CustomerPresenter>(
          () => CustomerPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<SalePresenter>(
          () => SalePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<SupplierPresenter>(
          () => SupplierPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<AccountPresenter>(
          () => AccountPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<PurchasePresenter>(
          () => PurchasePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<WarehousePresenter>(
          () => WarehousePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<InventoryTransferPresenter>(
          () => InventoryTransferPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<InventoryAdjustmentPresenter>(
          () => InventoryAdjustmentPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<InventoryPresenter>(
          () => InventoryPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<SimpleTiePresenter>(
          () => SimpleTiePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<PromotionPresenter>(
          () => PromotionPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<VoucherPresenter>(
          () => VoucherPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<CurrencyPresenter>(
          () => CurrencyPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<UserPresenter>(
          () => UserPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<PermissionPresenter>(
          () => PermissionPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<RolePresenter>(
          () => RolePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<JournalEntryPresenter>(
          () => JournalEntryPresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<ExpensePresenter>(
          () => ExpensePresenter(),
        ),
      ),
      ChangeNotifierProvider.value(
        value: LazyPresenterManager.getOrCreate<ReportsPresenter>(
          () => ReportsPresenter(),
        ),
      ),
    ];
  }

  /// 🔗 الحصول على الـ providers المعقدة (ProxyProviders)
  ///
  /// هذه الدالة تحتوي على الـ Presenters التي تعتمد على presenters أخرى.
  /// يتم استخدام ProxyProvider لحقن التبعيات بشكل صحيح.
  ///
  /// **الـ Presenters المعقدة:**
  /// - 🛒 POSPresenter: يعتمد على ProductPresenter و CustomerPresenter
  static List<SingleChildWidget> getComplexProviders() {
    return [
      // 🛒 مقدم نقطة البيع - يحتاج إلى مقدم المنتجات والعملاء
      ProxyProvider2<ProductPresenter, CustomerPresenter, POSPresenter>(
        update: (_, productPresenter, customerPresenter, previous) =>
            previous ??
            POSPresenter(
              productPresenter: productPresenter,
              customerPresenter: customerPresenter,
            ),
      ),
    ];
  }

  /// 🌟 الحصول على جميع الـ providers (أساسية + كسولة + معقدة)
  ///
  /// هذه الدالة تجمع جميع الـ providers في التطبيق.
  ///
  /// ⚠️ **تحذير:** استخدام هذه الدالة قد يؤثر على أداء التطبيق
  /// لأنها تحمل جميع الـ Presenters مرة واحدة.
  /// يُنصح باستخدام getMinimalProviders() بدلاً من ذلك.
  static List<SingleChildWidget> getAllProviders() {
    return [
      ...getCoreProviders(),
      ...getLazyProviders(),
      ...getComplexProviders(),
    ];
  }

  /// ⭐ الحصول على الـ providers الأساسية فقط (لتحسين وقت البدء)
  ///
  /// 🎯 **هذه هي الدالة الرئيسية المُوصى باستخدامها في التطبيق**
  ///
  /// تحتوي على جميع الـ Presenters المطلوبة لجميع شاشات التطبيق:
  ///
  /// 📋 **قائمة الشاشات المدعومة:**
  ///
  /// 🔐 **شاشات إدارة المستخدمين:**
  /// - LoginScreen (شاشة تسجيل الدخول)
  /// - UsersScreen (شاشة إدارة المستخدمين)
  /// - RolesManagementScreen (شاشة إدارة الأدوار)
  /// - UserAccessScreen (شاشة صلاحيات المستخدمين)
  ///
  /// 💰 **شاشات النظام المحاسبي:**
  /// - AccountsScreen (شاشة الحسابات)
  /// - JournalEntriesScreen (شاشة القيود المحاسبية)
  /// - CurrenciesScreen (شاشة العملات)
  /// - VouchersScreen (شاشة السندات المالية)
  ///
  /// 🛍️ **شاشات المبيعات والمشتريات:**
  /// - SalesScreen (شاشة المبيعات)
  /// - PurchasesScreen (شاشة المشتريات)
  /// - CustomersScreen (شاشة العملاء)
  /// - SuppliersScreen (شاشة الموردين)
  /// - POSScreen (شاشة نقطة البيع)
  ///
  /// 📦 **شاشات إدارة المخزون:**
  /// - ProductsScreen (شاشة المنتجات)
  /// - CategoriesScreen (شاشة الفئات)
  /// - UnitsScreen (شاشة الوحدات)
  /// - WarehousesScreen (شاشة المخازن)
  /// - InventoryScreen (شاشة إدارة المخزون)
  ///
  /// 💸 **شاشات المصروفات:**
  /// - ExpenseScreen (شاشة المصروفات)
  /// - SimpleTieScreen (شاشة القيود البسيطة)
  /// - PromotionsScreen (شاشة العروض الترويجية)
  ///
  /// 📊 **شاشات التقارير:**
  /// - ReportsScreen (شاشة التقارير)
  /// - DashboardScreen (شاشة لوحة المعلومات)
  ///
  /// ⚙️ **شاشات الإعدادات:**
  /// - SettingsScreen (شاشة الإعدادات)
  static List<SingleChildWidget> getMinimalProviders() {
    return [
      ...getCoreProviders(),

      // ═══════════════════════════════════════════════════════════════
      // 🔐 مقدمات إدارة المستخدمين والصلاحيات
      // ═══════════════════════════════════════════════════════════════
      // مطلوب لشاشات: تسجيل الدخول، إدارة المستخدمين، الأدوار، الصلاحيات
      ChangeNotifierProvider(
          create: (_) => PermissionPresenter()), // شاشة الصلاحيات
      ChangeNotifierProvider(create: (_) => RolePresenter()), // شاشة الأدوار
      ChangeNotifierProvider(create: (_) => UserPresenter()), // شاشة المستخدمين

      // ═══════════════════════════════════════════════════════════════
      // 💰 مقدمات النظام المحاسبي والمالي
      // ═══════════════════════════════════════════════════════════════
      // مطلوب لشاشات: الحسابات، القيود، التقارير المالية، العملات
      ChangeNotifierProvider(
          create: (_) => AccountPresenter()), // شاشة الحسابات المحاسبية
      ChangeNotifierProvider(
          create: (_) => JournalEntryPresenter()), // شاشة القيود المحاسبية
      ChangeNotifierProvider(
          create: (_) => CurrencyPresenter()), // شاشة العملات
      ChangeNotifierProvider(
          create: (_) => VoucherPresenter()), // شاشة السندات المالية

      // ═══════════════════════════════════════════════════════════════
      // 🛍️ مقدمات إدارة المبيعات والمشتريات
      // ═══════════════════════════════════════════════════════════════
      // مطلوب لشاشات: المبيعات، المشتريات، نقطة البيع، العملاء، الموردين
      ChangeNotifierProvider(create: (_) => SalePresenter()), // شاشة المبيعات
      ChangeNotifierProvider(
          create: (_) => PurchasePresenter()), // شاشة المشتريات
      ChangeNotifierProvider(
          create: (_) => CustomerPresenter()), // شاشة العملاء
      ChangeNotifierProvider(
          create: (_) => SupplierPresenter()), // شاشة الموردين

      // ═══════════════════════════════════════════════════════════════
      // 📦 مقدمات إدارة المخزون والمنتجات
      // ═══════════════════════════════════════════════════════════════
      // مطلوب لشاشات: المنتجات، الفئات، الوحدات، المخازن، المخزون
      ChangeNotifierProvider(
          create: (_) => ProductPresenter()), // شاشة المنتجات
      ChangeNotifierProvider(create: (_) => CategoryPresenter()), // شاشة الفئات
      ChangeNotifierProvider(create: (_) => UnitPresenter()), // شاشة الوحدات
      ChangeNotifierProvider(
          create: (_) => WarehousePresenter()), // شاشة المخازن
      ChangeNotifierProvider(
          create: (_) => InventoryPresenter()), // شاشة إدارة المخزون
      ChangeNotifierProvider(
          create: (_) => InventoryTransferPresenter()), // شاشة نقل المخزون
      ChangeNotifierProvider(
          create: (_) => InventoryAdjustmentPresenter()), // شاشة تسوية المخزون

      // ═══════════════════════════════════════════════════════════════
      // 💸 مقدمات إدارة المصروفات والعمليات المالية
      // ═══════════════════════════════════════════════════════════════
      // مطلوب لشاشات: المصروفات، القيود البسيطة، العروض الترويجية
      ChangeNotifierProvider(
          create: (_) => ExpensePresenter()), // شاشة المصروفات
      ChangeNotifierProvider(
          create: (_) => SimpleTiePresenter()), // شاشة القيود البسيطة
      ChangeNotifierProvider(
          create: (_) => PromotionPresenter()), // شاشة العروض الترويجية

      // ═══════════════════════════════════════════════════════════════
      // 📊 مقدمات التقارير والإحصائيات
      // ═══════════════════════════════════════════════════════════════
      // مطلوب لشاشات: التقارير، لوحة المعلومات، الإحصائيات
      ChangeNotifierProvider(
          create: (_) => ReportsPresenter()), // شاشة التقارير

      ...getComplexProviders(),
    ];
  }

  /// تنظيف الموارد عند إغلاق التطبيق
  static void dispose() {
    // يمكن إضافة منطق تنظيف الموارد هنا إذا لزم الأمر
  }
}

/// مدير التحميل الكسول المحسن للـ Presenters
class LazyPresenterManager {
  static final Map<Type, dynamic> _instances = {};
  static final Map<Type, bool> _isLoading = {};
  static final Map<Type, DateTime> _creationTimes = {};

  /// الحصول على presenter بشكل كسول أو إنشاؤه إذا لم يكن موجوداً
  static T getOrCreate<T>(T Function() factory) {
    if (!_instances.containsKey(T)) {
      // منع إنشاء متعدد للـ presenter نفسه
      if (_isLoading[T] == true) {
        // انتظار حتى ينتهي الإنشاء مع timeout
        int attempts = 0;
        while (_isLoading[T] == true && attempts < 100) {
          attempts++;
          // انتظار قصير جداً
          Future.delayed(const Duration(milliseconds: 10));
        }

        if (_instances.containsKey(T)) {
          return _instances[T] as T;
        }
      }

      _isLoading[T] = true;
      try {
        final instance = factory();
        _instances[T] = instance;
        _creationTimes[T] = DateTime.now();

        // تهيئة الـ presenter إذا كان يحتوي على دالة init
        if (instance is BasePresenter) {
          instance.init().catchError((e) {
            // تسجيل الخطأ بدون إيقاف التطبيق
            AppLogger.error('خطأ في تهيئة ${T.toString()}: $e');
          });
        }

        return instance;
      } finally {
        _isLoading[T] = false;
      }
    }
    return _instances[T] as T;
  }

  /// الحصول على presenter بشكل كسول (للتوافق مع الكود القديم)
  static T getLazyPresenter<T>(T Function() factory) {
    return getOrCreate<T>(factory);
  }

  /// التحقق من وجود presenter
  static bool exists<T>() {
    return _instances.containsKey(T);
  }

  /// الحصول على عدد الـ presenters المحملة
  static int get loadedCount => _instances.length;

  /// الحصول على قائمة بأنواع الـ presenters المحملة
  static List<Type> get loadedTypes => _instances.keys.toList();

  /// تنظيف جميع الـ instances
  static void clearAll() {
    // تنظيف الـ presenters التي تحتاج dispose
    for (var instance in _instances.values) {
      if (instance is ChangeNotifier) {
        instance.dispose();
      }
    }
    _instances.clear();
    _isLoading.clear();
    _creationTimes.clear();
  }

  /// تنظيف presenter محدد
  static void clear<T>() {
    final instance = _instances[T];
    if (instance is ChangeNotifier) {
      instance.dispose();
    }
    _instances.remove(T);
    _isLoading.remove(T);
    _creationTimes.remove(T);
  }

  /// إحصائيات استخدام الذاكرة المحسنة
  static Map<String, dynamic> getMemoryStats() {
    final now = DateTime.now();
    final stats = <String, dynamic>{
      'loaded_presenters': loadedCount,
      'types': loadedTypes.map((t) => t.toString()).toList(),
      'memory_usage_estimate': '${loadedCount * 50}KB',
      'creation_times': {},
      'uptime_minutes': {},
    };

    // إضافة معلومات وقت الإنشاء ومدة التشغيل
    for (final entry in _creationTimes.entries) {
      final type = entry.key.toString();
      final creationTime = entry.value;
      final uptime = now.difference(creationTime);

      stats['creation_times'][type] = creationTime.toIso8601String();
      stats['uptime_minutes'][type] = uptime.inMinutes;
    }

    return stats;
  }

  /// تنظيف الـ presenters القديمة (التي لم تستخدم لفترة طويلة)
  static void cleanupOldPresenters(
      {Duration maxAge = const Duration(hours: 1)}) {
    final now = DateTime.now();
    final toRemove = <Type>[];

    for (final entry in _creationTimes.entries) {
      final type = entry.key;
      final creationTime = entry.value;

      if (now.difference(creationTime) > maxAge) {
        toRemove.add(type);
      }
    }

    for (final type in toRemove) {
      final instance = _instances[type];
      if (instance is ChangeNotifier) {
        instance.dispose();
      }
      _instances.remove(type);
      _isLoading.remove(type);
      _creationTimes.remove(type);

      AppLogger.info('تم تنظيف presenter قديم: ${type.toString()}');
    }
  }

  /// إعادة تشغيل presenter محدد
  static void restart<T>(T Function() factory) {
    clear<T>();
    getOrCreate<T>(factory);
  }
}
