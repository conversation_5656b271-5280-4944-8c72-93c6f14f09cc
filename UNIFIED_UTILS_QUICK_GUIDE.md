# 🚀 دليل سريع - نظام الأدوات الموحد

## 📱 الاستخدام السريع

### 1. **الاستيراد الموحد**
```dart
import 'package:tajer_plus/core/utils/index.dart';
```

### 2. **التحقق من صحة البيانات**
```dart
// التحقق البسيط
validator: UnifiedValidators.required('اسم المستخدم')

// التحقق المركب
validator: UnifiedValidators.combine([
  UnifiedValidators.required('البريد الإلكتروني'),
  UnifiedValidators.email(),
])

// التحقق الشرطي
validator: UnifiedValidators.conditional(
  isRequired,
  UnifiedValidators.required('الحقل'),
)
```

### 3. **المساعدات العامة**
```dart
// عرض الرسائل
UnifiedHelpers.showSuccess(context, 'تم الحفظ بنجاح');
UnifiedHelpers.showError(context, 'حدث خطأ');
UnifiedHelpers.showWarning(context, 'تحذير');

// العمليات الآمنة
await UnifiedHelpers.safeExecute(
  context,
  () async {
    // كود العملية
  },
  successMessage: 'تم بنجاح',
  showLoading: true,
);

// تنسيق البيانات
final date = UnifiedHelpers.formatDate(DateTime.now());
final currency = UnifiedHelpers.formatCurrency(1500.50);
```

### 4. **التخطيط والاستجابة**
```dart
// تهيئة النظام
UnifiedLayout.init(context);

// الأبعاد المتجاوبة
final width = UnifiedLayout.w(50); // 50% من عرض الشاشة
final height = UnifiedLayout.h(30); // 30% من ارتفاع الشاشة

// العناصر الآمنة
UnifiedLayout.safeText('النص هنا')
UnifiedLayout.safeButton(
  label: 'زر',
  onPressed: () {},
)
```

## 🔧 الوظائف المتاحة

### **التحقق من صحة البيانات (UnifiedValidators)**
```dart
// التحقق الأساسي
UnifiedValidators.required('الحقل')
UnifiedValidators.email()
UnifiedValidators.phone()

// التحقق من النصوص
UnifiedValidators.minLength(8, 'كلمة المرور')
UnifiedValidators.maxLength(50, 'الاسم')
UnifiedValidators.lengthRange(3, 20, 'اسم المستخدم')
UnifiedValidators.arabicOnly('الاسم العربي')
UnifiedValidators.englishOnly('الاسم الإنجليزي')

// التحقق من الأرقام
UnifiedValidators.number('العمر')
UnifiedValidators.integer('الكمية')
UnifiedValidators.positiveNumber('السعر')
UnifiedValidators.numberRange(1, 100, 'النسبة المئوية')

// التحقق المتقدم
UnifiedValidators.saudiNationalId()
UnifiedValidators.saudiPostalCode()
UnifiedValidators.strongPassword()
UnifiedValidators.passwordMatch(passwordController.text)
```

### **المساعدات العامة (UnifiedHelpers)**
```dart
// تنسيق البيانات
UnifiedHelpers.formatDate(date, format: 'dd/MM/yyyy')
UnifiedHelpers.formatDateTime(dateTime)
UnifiedHelpers.formatCurrency(amount, symbol: 'ر.س')
UnifiedHelpers.formatFileSize(bytes)

// الرسائل والحوارات
UnifiedHelpers.showSuccess(context, 'رسالة نجاح')
UnifiedHelpers.showError(context, 'رسالة خطأ')
UnifiedHelpers.showConfirmDialog(context, message: 'هل أنت متأكد؟')
UnifiedHelpers.showLoading(context, message: 'جاري المعالجة...')

// العمليات الآمنة
UnifiedHelpers.safeAsyncOperation(context, () async { ... })
UnifiedHelpers.safeLoadData(context, () async { ... })
UnifiedHelpers.safeNavigate(context, '/route')

// التحقق من البيانات
UnifiedHelpers.isValidEmail(email)
UnifiedHelpers.isValidPhone(phone)
UnifiedHelpers.isStrongPassword(password)
```

### **التخطيط والاستجابة (UnifiedLayout)**
```dart
// التهيئة والأبعاد
UnifiedLayout.init(context)
UnifiedLayout.w(percentage) // عرض متجاوب
UnifiedLayout.h(percentage) // ارتفاع متجاوب

// فحص نوع الجهاز
UnifiedLayout.isMobile()
UnifiedLayout.isTablet()
UnifiedLayout.isDesktop()

// الأحجام المتجاوبة
UnifiedLayout.getResponsiveFontSize(14)
UnifiedLayout.getResponsiveIconSize(24)
UnifiedLayout.getGridColumnCount()

// العناصر الآمنة
UnifiedLayout.safeText('نص', style: AppTypography())
UnifiedLayout.safeButton(label: 'زر', onPressed: () {})
UnifiedLayout.safeTextField(labelText: 'حقل إدخال')
UnifiedLayout.safeCard(child: Widget())

// التخطيط المرن
UnifiedLayout.flexibleRow(children: [...])
UnifiedLayout.safeColumn(children: [...])
UnifiedLayout.safeGrid(children: [...])
```

## 🎨 أمثلة عملية

### **نموذج تسجيل دخول**
```dart
class LoginForm extends StatelessWidget {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    UnifiedLayout.init(context);
    
    return Form(
      key: _formKey,
      child: UnifiedLayout.safeColumn(
        spacing: 16,
        children: [
          UnifiedLayout.safeTextField(
            controller: _emailController,
            labelText: 'البريد الإلكتروني',
            keyboardType: TextInputType.emailAddress,
            validator: UnifiedValidators.combine([
              UnifiedValidators.required('البريد الإلكتروني'),
              UnifiedValidators.email(),
            ]),
          ),
          
          UnifiedLayout.safeTextField(
            controller: _passwordController,
            labelText: 'كلمة المرور',
            obscureText: true,
            validator: UnifiedValidators.combine([
              UnifiedValidators.required('كلمة المرور'),
              UnifiedValidators.minLength(6, 'كلمة المرور'),
            ]),
          ),
          
          UnifiedLayout.safeButton(
            label: 'تسجيل دخول',
            onPressed: () => _handleLogin(context),
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  void _handleLogin(BuildContext context) async {
    if (!_formKey.currentState!.validate()) return;

    await UnifiedHelpers.safeExecute(
      context,
      () async {
        // كود تسجيل الدخول
        await AuthService.login(
          _emailController.text,
          _passwordController.text,
        );
      },
      successMessage: 'تم تسجيل الدخول بنجاح',
      errorMessage: 'فشل في تسجيل الدخول',
      showLoading: true,
    );
  }
}
```

### **قائمة متجاوبة**
```dart
class ResponsiveProductGrid extends StatelessWidget {
  final List<Product> products;

  const ResponsiveProductGrid({required this.products});

  @override
  Widget build(BuildContext context) {
    UnifiedLayout.init(context);
    
    return UnifiedLayout.safeGrid(
      crossAxisCount: UnifiedLayout.getGridColumnCount(),
      childAspectRatio: UnifiedLayout.getGridAspectRatio(),
      spacing: 16,
      children: products.map((product) => 
        UnifiedLayout.safeCard(
          onTap: () => _showProductDetails(context, product),
          child: Column(
            children: [
              UnifiedLayout.safeText(
                product.name,
                style: const AppTypography(fontWeight: FontWeight.bold),
                maxLines: 2,
              ),
              UnifiedLayout.safeText(
                UnifiedHelpers.formatCurrency(product.price),
                style: const AppTypography(color: AppColors.primary),
              ),
            ],
          ),
        ),
      ).toList(),
    );
  }

  void _showProductDetails(BuildContext context, Product product) {
    UnifiedHelpers.safeNavigate(
      context,
      '/product-details',
      arguments: product,
    );
  }
}
```

### **معالج النماذج المتقدم**
```dart
class AdvancedFormHandler {
  static Future<bool> handleFormSubmission({
    required BuildContext context,
    required GlobalKey<FormState> formKey,
    required Future<void> Function() submitAction,
    String successMessage = 'تم الحفظ بنجاح',
    String errorMessage = 'فشل في الحفظ',
  }) async {
    if (!formKey.currentState!.validate()) {
      UnifiedHelpers.showWarning(context, 'يرجى تصحيح الأخطاء أولاً');
      return false;
    }

    return await UnifiedHelpers.safeExecute(
      context,
      submitAction,
      successMessage: successMessage,
      errorMessage: errorMessage,
      showLoading: true,
    );
  }
}
```

## ⚠️ نصائح مهمة

### **✅ افعل**
- استخدم `UnifiedLayout.init(context)` في بداية كل شاشة
- استخدم التحقق المركب مع `combine()` للتحقق المعقد
- استخدم العمليات الآمنة مع `UnifiedHelpers.safeExecute()`
- استخدم العناصر الآمنة `safe*` للتخطيط المتجاوب

### **❌ لا تفعل**
- لا تخلط بين الأنظمة القديمة والجديدة في نفس الملف
- لا تتجاهل فحص `mounted` في العمليات غير المتزامنة
- لا تستخدم أحجام ثابتة، استخدم النسب المئوية
- لا تنس معالجة الأخطاء في العمليات الحرجة

## 🔗 روابط مفيدة

- **الكود الموحد**: `lib/core/utils/unified_*.dart`
- **نقطة الوصول**: `lib/core/utils/index.dart`
- **التوثيق الكامل**: `UNIFIED_UTILS_SYSTEM_REPORT.md`
- **الأنظمة القديمة**: متوفرة للتوافق العكسي

---

**💡 نصيحة**: ابدأ بالأنظمة الموحدة في المشاريع الجديدة واترقي تدريجياً في المشاريع الموجودة!
