import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/theme/index.dart';
import '../../../core/utils/index.dart';

import 'package:tajer_plus/core/widgets/custom_button.dart';
import 'package:tajer_plus/core/models/payroll.dart';
import 'package:tajer_plus/core/models/payroll_status.dart';

/// شاشة عرض تفاصيل الراتب
class PayrollDetailScreen extends StatefulWidget {
  final Payroll payroll;

  const PayrollDetailScreen({Key? key, required this.payroll})
      : super(key: key);

  @override
  State<PayrollDetailScreen> createState() => _PayrollDetailScreenState();
}

class _PayrollDetailScreenState extends State<PayrollDetailScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الراتب'),
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: () {
              // طباعة الراتب
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('جاري إعداد الطباعة...')),
              );
            },
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPayrollInfoCard(),
          const SizedBox(height: 16),
          _buildEmployeesPayrollCard(),
          const SizedBox(height: 24),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildPayrollInfoCard() {
    final dateFormat = DateFormat('yyyy/MM/dd');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الراتب',
              style: AppTypography(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Layout.isDesktop() || Layout.isTablet()
                ? _buildDesktopPayrollInfoFields(dateFormat)
                : _buildMobilePayrollInfoFields(dateFormat),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopPayrollInfoFields(DateFormat dateFormat) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child:
                  _buildInfoField('رقم الراتب', widget.payroll.payrollNumber),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildInfoField('الفرع', widget.payroll.branchName ?? ''),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildInfoField(
                'تاريخ بداية الفترة',
                dateFormat.format(widget.payroll.periodStartDate),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildInfoField(
                'تاريخ نهاية الفترة',
                dateFormat.format(widget.payroll.periodEndDate),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildInfoField(
                'تاريخ الدفع',
                dateFormat.format(widget.payroll.paymentDate),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatusField('الحالة', widget.payroll.status),
            ),
          ],
        ),
        if (widget.payroll.notes != null &&
            widget.payroll.notes!.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildInfoField('ملاحظات', widget.payroll.notes!),
        ],
      ],
    );
  }

  Widget _buildMobilePayrollInfoFields(DateFormat dateFormat) {
    return Column(
      children: [
        _buildInfoField('رقم الراتب', widget.payroll.payrollNumber),
        const SizedBox(height: 8),
        _buildInfoField('الفرع', widget.payroll.branchName ?? ''),
        const SizedBox(height: 8),
        _buildInfoField(
          'تاريخ بداية الفترة',
          dateFormat.format(widget.payroll.periodStartDate),
        ),
        const SizedBox(height: 8),
        _buildInfoField(
          'تاريخ نهاية الفترة',
          dateFormat.format(widget.payroll.periodEndDate),
        ),
        const SizedBox(height: 8),
        _buildInfoField(
          'تاريخ الدفع',
          dateFormat.format(widget.payroll.paymentDate),
        ),
        const SizedBox(height: 8),
        _buildStatusField('الحالة', widget.payroll.status),
        if (widget.payroll.notes != null &&
            widget.payroll.notes!.isNotEmpty) ...[
          const SizedBox(height: 8),
          _buildInfoField('ملاحظات', widget.payroll.notes!),
        ],
      ],
    );
  }

  Widget _buildInfoField(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const AppTypography(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 4),
        Text(value),
      ],
    );
  }

  Widget _buildStatusField(String label, PayrollStatus status) {
    Color color;
    String text;

    switch (status) {
      case PayrollStatus.draft:
        color = AppColors.lightTextSecondary;
        text = 'مسودة';
        break;
      case PayrollStatus.approved:
        color = AppColors.info;
        text = 'معتمد';
        break;
      case PayrollStatus.paid:
        color = AppColors.success;
        text = 'مدفوع';
        break;
      case PayrollStatus.cancelled:
        color = AppColors.error;
        text = 'ملغي';
        break;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const AppTypography(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 4),
        Chip(
          label: Text(
            text,
            style: const AppTypography(
                color: AppColors.lightTextSecondary, fontSize: 12),
          ),
          backgroundColor: color,
          padding: const EdgeInsets.all(4),
        ),
      ],
    );
  }

  Widget _buildEmployeesPayrollCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'تفاصيل رواتب الموظفين',
                  style:
                      AppTypography(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Text(
                  'إجمالي الرواتب: ${_calculateTotalSalaries()} ${AppConstants.appCurrency}',
                  style: const AppTypography(
                      fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildEmployeesPayrollTable(),
          ],
        ),
      ),
    );
  }

  double _calculateTotalSalaries() {
    if (widget.payroll.details == null || widget.payroll.details!.isEmpty) {
      return 0.0;
    }
    return widget.payroll.details!
        .fold(0, (sum, detail) => sum + detail.netSalary);
  }

  Widget _buildEmployeesPayrollTable() {
    if (widget.payroll.details == null || widget.payroll.details!.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('لا يوجد موظفين في كشف الراتب'),
        ),
      );
    }

    return Layout.isDesktop() || Layout.isTablet()
        ? _buildDesktopEmployeesTable()
        : _buildMobileEmployeesList();
  }

  Widget _buildDesktopEmployeesTable() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('الموظف')),
          DataColumn(label: Text('الراتب الأساسي')),
          DataColumn(label: Text('البدلات')),
          DataColumn(label: Text('الخصومات')),
          DataColumn(label: Text('العمل الإضافي')),
          DataColumn(label: Text('المكافآت')),
          DataColumn(label: Text('صافي الراتب')),
        ],
        rows: widget.payroll.details!.map((detail) {
          return DataRow(
            cells: [
              DataCell(Text(detail.employeeName)),
              DataCell(Text('${detail.basicSalary}')),
              DataCell(Text('${detail.allowances}')),
              DataCell(Text('${detail.deductions}')),
              DataCell(Text('${detail.overtime}')),
              DataCell(Text('${detail.bonus}')),
              DataCell(Text(
                '${detail.netSalary}',
                style: const AppTypography(fontWeight: FontWeight.bold),
              )),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildMobileEmployeesList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: widget.payroll.details!.length,
      itemBuilder: (context, index) {
        final detail = widget.payroll.details![index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ExpansionTile(
            title: Text(
              detail.employeeName,
              style: const AppTypography(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              'صافي الراتب: ${detail.netSalary} ${AppConstants.appCurrency}',
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    _buildPayrollDetailRow(
                        'الراتب الأساسي', '${detail.basicSalary}'),
                    _buildPayrollDetailRow('البدلات', '${detail.allowances}'),
                    _buildPayrollDetailRow('الخصومات', '${detail.deductions}'),
                    _buildPayrollDetailRow(
                        'العمل الإضافي', '${detail.overtime}'),
                    _buildPayrollDetailRow('المكافآت', '${detail.bonus}'),
                    const Divider(),
                    _buildPayrollDetailRow(
                      'صافي الراتب',
                      '${detail.netSalary} ${AppConstants.appCurrency}',
                      isBold: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPayrollDetailRow(String label, String value,
      {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: AppTypography(
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (widget.payroll.status == PayrollStatus.draft) ...[
          CustomButton(
            text: 'اعتماد الراتب',
            icon: Icons.check_circle,
            backgroundColor: AppColors.success,
            isLoading: _isLoading,
            onPressed: _approvePayroll,
          ),
          const SizedBox(width: 16),
        ],
        if (widget.payroll.status == PayrollStatus.approved) ...[
          CustomButton(
            text: 'تسجيل الدفع',
            icon: Icons.payments,
            backgroundColor: AppColors.info,
            isLoading: _isLoading,
            onPressed: _markAsPaid,
          ),
          const SizedBox(width: 16),
        ],
        CustomButton(
          text: 'طباعة',
          icon: Icons.print,
          backgroundColor: AppColors.teal,
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('جاري إعداد الطباعة...')),
            );
          },
        ),
      ],
    );
  }

  Future<void> _approvePayroll() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // هنا سيتم استدعاء خدمة اعتماد الراتب
      // مثال:
      // final payrollService = PayrollService();
      // await payrollService.approvePayroll(widget.payroll.id!);

      // محاكاة تأخير العملية
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم اعتماد الراتب بنجاح')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _markAsPaid() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // هنا سيتم استدعاء خدمة تسجيل دفع الراتب
      // مثال:
      // final payrollService = PayrollService();
      // await payrollService.markPayrollAsPaid(widget.payroll.id!);

      // محاكاة تأخير العملية
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تسجيل دفع الراتب بنجاح')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
