import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/index.dart';

/// نظام المساعدات العامة
/// يجمع جميع الدوال المساعدة في مكان واحد
///
/// الميزات:
/// - تنسيق البيانات (تاريخ، وقت، أرقام، عملة)
/// - الرسائل والحوارات (نجاح، خطأ، تحذير، معلومات)
/// - العمليات الآمنة (تنفيذ، تحميل، تنقل)
/// - التحقق من البيانات (بريد، هاتف، كلمة مرور)
/// - إدارة الألوان والحالات
/// - تنظيف الموارد
///
/// الاستخدام:
/// ```dart
/// // عرض رسالة نجاح
/// Helpers.showSuccess(context, 'تم الحفظ بنجاح');
///
/// // تنفيذ عملية آمنة
/// await Helpers.safeExecute(context, () async {
///   // كود العملية
/// });
///
/// // تنسيق التاريخ
/// final formattedDate = Helpers.formatDate(DateTime.now());
/// ```
class Helpers {
  // ========== تنسيق البيانات ==========

  /// تنسيق التاريخ
  static String formatDate(DateTime? date, {String format = 'yyyy-MM-dd'}) {
    if (date == null) return '';
    return DateFormat(format).format(date);
  }

  /// تنسيق التاريخ والوقت
  static String formatDateTime(DateTime? dateTime,
      {String format = 'yyyy-MM-dd HH:mm'}) {
    if (dateTime == null) return '';
    return DateFormat(format).format(dateTime);
  }

  /// تنسيق الرقم
  static String formatNumber(num? number, {int decimalPlaces = 2}) {
    if (number == null) return '';
    return number.toStringAsFixed(decimalPlaces);
  }

  /// تنسيق العملة
  static String formatCurrency(num? amount,
      {String symbol = 'ر.س', int decimalPlaces = 2}) {
    if (amount == null) return '';
    return '$symbol ${amount.toStringAsFixed(decimalPlaces)}';
  }

  /// تنسيق حجم الملف
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  // ========== الرسائل والحوارات ==========

  /// عرض رسالة نجاح
  static void showSuccess(BuildContext context, String message) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة خطأ
  static void showError(BuildContext context, String message) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// عرض رسالة تحذير
  static void showWarning(BuildContext context, String message) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.warning,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة معلومات
  static void showInfo(BuildContext context, String message) {
    if (!context.mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.info,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض مربع حوار تأكيد
  static Future<bool?> showConfirmDialog(
    BuildContext context, {
    String title = 'تأكيد',
    required String message,
    String confirmText = 'موافق',
    String cancelText = 'إلغاء',
    Color confirmColor = AppColors.error,
  }) async {
    if (!context.mounted) return null;

    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(
              confirmText,
              style: AppTypography(color: confirmColor),
            ),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار خطأ
  static Future<void> showErrorDialog(
    BuildContext context, {
    String title = 'خطأ',
    required String message,
    String buttonText = 'موافق',
  }) async {
    if (!context.mounted) return;

    return await showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(buttonText),
          ),
        ],
      ),
    );
  }

  /// عرض مؤشر التحميل
  static void showLoading(BuildContext context,
      {String message = 'جاري التحميل...'}) {
    if (!context.mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  /// إخفاء مؤشر التحميل
  static void hideLoading(BuildContext context) {
    if (context.mounted) {
      Navigator.of(context).pop();
    }
  }

  // ========== العمليات الآمنة ==========

  /// تنفيذ عملية مع فحص mounted
  static Future<T?> safeAsyncOperation<T>(
    BuildContext context,
    Future<T> Function() operation, {
    bool Function()? mountedCheck,
  }) async {
    final mounted = mountedCheck ?? () => context.mounted;
    if (!mounted()) return null;

    try {
      final result = await operation();
      if (!mounted()) return null;
      return result;
    } catch (e) {
      if (mounted()) rethrow;
      return null;
    }
  }

  /// تنفيذ عملية مع معالجة الأخطاء
  static Future<bool> safeExecute(
    BuildContext context,
    Future<void> Function() operation, {
    String? successMessage,
    String? errorMessage,
    bool showLoading = false,
  }) async {
    if (!context.mounted) return false;

    try {
      if (showLoading) {
        Helpers.showLoading(context);
      }

      await operation();

      if (showLoading && context.mounted) {
        hideLoading(context);
      }

      if (successMessage != null && context.mounted) {
        showSuccess(context, successMessage);
      }

      return true;
    } catch (e) {
      if (showLoading && context.mounted) {
        hideLoading(context);
      }

      if (context.mounted) {
        final message = errorMessage ?? 'حدث خطأ: $e';
        showError(context, message);
      }

      return false;
    }
  }

  /// تحميل البيانات مع معالجة الحالة
  static Future<T?> safeLoadData<T>(
    BuildContext context,
    Future<T> Function() loadOperation, {
    void Function(bool)? setLoading,
    String? errorMessage,
  }) async {
    if (!context.mounted) return null;

    try {
      setLoading?.call(true);
      final result = await loadOperation();

      if (!context.mounted) return null;

      setLoading?.call(false);
      return result;
    } catch (e) {
      if (context.mounted) {
        setLoading?.call(false);
        final message = errorMessage ?? 'فشل في تحميل البيانات: $e';
        showError(context, message);
      }
      return null;
    }
  }

  // ========== التنقل الآمن ==========

  /// التنقل بطريقة آمنة
  static Future<T?> safeNavigate<T>(
    BuildContext context,
    String routeName, {
    Object? arguments,
    bool replace = false,
  }) async {
    if (!context.mounted) return null;

    try {
      if (replace) {
        return await Navigator.pushReplacementNamed<T, void>(
          context,
          routeName,
          arguments: arguments,
        );
      } else {
        return await Navigator.pushNamed<T>(
          context,
          routeName,
          arguments: arguments,
        );
      }
    } catch (e) {
      debugPrint('خطأ في التنقل: $e');
      return null;
    }
  }

  /// الرجوع بطريقة آمنة
  static void safePop<T>(BuildContext context, [T? result]) {
    if (!context.mounted) return;

    try {
      Navigator.pop(context, result);
    } catch (e) {
      debugPrint('خطأ في الرجوع: $e');
    }
  }

  // ========== Provider الآمن ==========

  /// الحصول على Provider بطريقة آمنة
  static T? safeProvider<T extends ChangeNotifier>(
    BuildContext context, {
    bool listen = false,
  }) {
    try {
      if (!context.mounted) return null;
      return Provider.of<T>(context, listen: listen);
    } catch (e) {
      debugPrint('خطأ في الحصول على Provider: $e');
      return null;
    }
  }

  // ========== التحقق من البيانات ==========

  /// التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    final emailRegExp = RegExp(r'^[\w\.-]+@[\w\.-]+\.\w{2,}$');
    return emailRegExp.hasMatch(email.trim().toLowerCase());
  }

  /// التحقق من صحة رقم الهاتف
  static bool isValidPhone(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    final phoneRegExp = RegExp(r'^(\+966|966|05)[0-9]{8,9}$|^\+?[0-9]{8,15}$');
    return phoneRegExp.hasMatch(cleanPhone);
  }

  /// التحقق من قوة كلمة المرور
  static bool isStrongPassword(String password) {
    return password.length >= 8 &&
        RegExp(r'[A-Z]').hasMatch(password) &&
        RegExp(r'[a-z]').hasMatch(password) &&
        RegExp(r'[0-9]').hasMatch(password);
  }

  // ========== الألوان والحالات ==========

  /// الحصول على لون حالة تحويل المخزون
  static Color getTransferStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
      case 'مسودة':
        return AppColors.lightTextSecondary;
      case 'pending':
      case 'قيد الانتظار':
        return AppColors.info;
      case 'intransit':
      case 'قيد النقل':
        return AppColors.warning;
      case 'completed':
      case 'مكتمل':
        return AppColors.success;
      case 'cancelled':
      case 'ملغي':
        return AppColors.error;
      case 'rejected':
      case 'مرفوض':
        return AppColors.accent;
      default:
        return AppColors.lightTextSecondary;
    }
  }

  /// الحصول على اسم حالة تحويل المخزون
  static String getTransferStatusName(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
        return 'مسودة';
      case 'pending':
        return 'قيد الانتظار';
      case 'intransit':
        return 'قيد النقل';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'rejected':
        return 'مرفوض';
      default:
        return 'غير معروف';
    }
  }

  /// الحصول على اسم نوع تحويل المخزون
  static String getTransferTypeName(String type) {
    switch (type.toLowerCase()) {
      case 'transfer':
        return 'تحويل بين المخازن';
      case 'adjustment':
        return 'تعديل المخزون';
      case 'receipt':
        return 'استلام مخزون';
      case 'issue':
        return 'صرف مخزون';
      default:
        return 'غير معروف';
    }
  }

  // ========== تنظيف الموارد ==========

  /// تنظيف الموارد بطريقة آمنة
  static void safeDispose(List<dynamic> resources) {
    for (final resource in resources) {
      try {
        if (resource is TextEditingController) {
          resource.dispose();
        } else if (resource is AnimationController) {
          resource.dispose();
        } else if (resource is ScrollController) {
          resource.dispose();
        } else if (resource is FocusNode) {
          resource.dispose();
        }
      } catch (e) {
        debugPrint('خطأ في تنظيف المورد: $e');
      }
    }
  }
}
