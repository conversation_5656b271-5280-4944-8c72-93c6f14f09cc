import 'package:flutter/material.dart';

import 'index.dart';

/// نظام الثيم الذكي الذي يحل مشاكل التباين والرؤية تلقائياً
///
/// 🧠 الذكاء الاصطناعي للألوان:
/// - حل مشاكل التباين تلقائياً
/// - اختيار ألوان النصوص المناسبة للخلفيات
/// - إنشاء تدرجات لونية جميلة
/// - تطبيق ظلال متناسقة
/// - تكييف الألوان حسب الوضع الفاتح/الداكن
/// - دوال مساعدة للشفافية والإضاءة
class SmartThemeSystem {
  /// الحصول على لون النص الذكي بناءً على الخلفية
  static Color getSmartTextColor(BuildContext context,
      {Color? backgroundColor}) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (backgroundColor != null) {
      return AppColors.ensureTextContrast(
        isDark ? AppColors.darkTextPrimary : AppColors.lightTextPrimary,
        backgroundColor,
      );
    }

    return AppColors.getAdaptiveTextColor(isDark);
  }

  /// الحصول على لون الخلفية الذكي للبطاقات
  static Color getSmartCardBackground(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return AppColors.getAdaptiveCardBackground(isDark);
  }

  /// الحصول على لون الزر الذكي
  static Color getSmartButtonColor(BuildContext context,
      {Color? preferredColor}) {
    if (preferredColor != null) {
      return preferredColor;
    }
    return DynamicColors.primary;
  }

  /// الحصول على لون نص الزر الذكي
  static Color getSmartButtonTextColor(
      BuildContext context, Color buttonColor) {
    return AppColors.ensureTextContrast(AppColors.onPrimary, buttonColor);
  }

  /// الحصول على ألوان الحالة الذكية
  static Color getSmartStatusColor(String status, BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'active':
        return AppColors.success;
      case 'error':
      case 'failed':
      case 'cancelled':
        return AppColors.error;
      case 'warning':
      case 'pending':
      case 'draft':
        return AppColors.warning;
      case 'info':
      case 'processing':
        return AppColors.info;
      default:
        return AppColors.getAdaptiveSecondaryTextColor(isDark);
    }
  }

  /// إنشاء ثيم ذكي للبطاقة
  static BoxDecoration createSmartCardDecoration(
    BuildContext context, {
    Color? backgroundColor,
    bool withShadow = true,
    double borderRadius = 12.0,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final cardBg =
        backgroundColor ?? AppColors.getAdaptiveCardBackground(isDark);

    return BoxDecoration(
      color: cardBg,
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: AppColors.getAdaptiveBorderColor(isDark).withValues(alpha: 0.2),
        width: 0.5,
      ),
      boxShadow: withShadow
          ? [
              BoxShadow(
                color: AppColors.getAdaptiveShadowColor(isDark),
                blurRadius: 8,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ]
          : null,
    );
  }

  /// إنشاء نمط نص ذكي
  static AppTypography createSmartAppTypography(
    BuildContext context, {
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    Color? backgroundColor,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    Color textColor;
    if (color != null) {
      textColor = color;
    } else if (backgroundColor != null) {
      textColor = AppColors.getTextColorForBackground(backgroundColor);
    } else {
      textColor = AppColors.getAdaptiveTextColor(isDark);
    }

    return AppTypography(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: textColor,
    );
  }

  /// إنشاء نمط زر ذكي
  static ButtonStyle createSmartButtonStyle(
    BuildContext context, {
    Color? backgroundColor,
    Color? textColor,
    double borderRadius = 8.0,
    EdgeInsetsGeometry? padding,
  }) {
    final buttonBg = backgroundColor ?? DynamicColors.primary;
    final buttonText = textColor ??
        AppColors.ensureTextContrast(
          AppColors.onPrimary,
          buttonBg,
        );

    return ElevatedButton.styleFrom(
      backgroundColor: buttonBg,
      foregroundColor: buttonText,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      padding: padding,
      elevation: 2,
    );
  }

  /// فحص وإصلاح التباين تلقائياً
  static Map<String, Color> fixContrastIssues(
    BuildContext context,
    Color backgroundColor,
    Color textColor,
  ) {
    final contrast = AppColors.getContrastRatio(backgroundColor, textColor);

    if (contrast < 4.5) {
      // التباين ضعيف، نحتاج لإصلاحه
      final newTextColor = AppColors.getTextColorForBackground(backgroundColor);

      return {
        'backgroundColor': backgroundColor,
        'textColor': newTextColor,
        'needsFix': AppColors.lightTextPrimary, // مؤشر على الحاجة للإصلاح
      };
    }

    return {
      'backgroundColor': backgroundColor,
      'textColor': textColor,
      'needsFix': AppColors.onPrimary, // مؤشر على عدم الحاجة للإصلاح
    };
  }

  /// إنشاء ألوان متدرجة ذكية (فقط للهيدر)
  static LinearGradient createSmartGradient(
    BuildContext context, {
    List<Color>? colors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    List<Color> gradientColors;

    if (colors != null && colors.isNotEmpty) {
      gradientColors = colors;
    } else {
      // استخدام تدرج الثيم الحالي
      gradientColors = AppColors.primaryGradient;
    }

    return LinearGradient(
      begin: begin,
      end: end,
      colors: gradientColors,
    );
  }

  /// تطبيق الثيم الذكي على الودجت
  static Widget applySmartTheme(
    BuildContext context,
    Widget child, {
    Color? backgroundColor,
    bool adaptToTheme = true,
  }) {
    if (!adaptToTheme) return child;

    final isDark = Theme.of(context).brightness == Brightness.dark;
    final smartBg =
        backgroundColor ?? AppColors.getAdaptiveCardBackground(isDark);

    return Container(
      decoration: createSmartCardDecoration(context, backgroundColor: smartBg),
      child: child,
    );
  }

  /// الحصول على معلومات الثيم الحالي
  static Map<String, dynamic> getCurrentThemeInfo(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return {
      'isDark': isDark,
      'primaryColor': DynamicColors.primary,
      'backgroundColor': AppColors.getAdaptiveCardBackground(isDark),
      'textColor': AppColors.getAdaptiveTextColor(isDark),
      'secondaryTextColor': AppColors.getAdaptiveSecondaryTextColor(isDark),
      'borderColor': AppColors.getAdaptiveBorderColor(isDark),
      'shadowColor': AppColors.getAdaptiveShadowColor(isDark),
      'themeName': 'تاجر بلس',
    };
  }

  /// تسجيل مشاكل التباين للتطوير
  static void logContrastIssues(
    BuildContext context,
    String widgetName,
    Color backgroundColor,
    Color textColor,
  ) {
    final contrast = AppColors.getContrastRatio(backgroundColor, textColor);

    if (contrast < 4.5) {
      debugPrint(
        '⚠️ مشكلة تباين في $widgetName: '
        'التباين = ${contrast.toStringAsFixed(2)} '
        '(المطلوب: 4.5+)',
      );
      debugPrint(
        '🎨 الألوان: خلفية=${backgroundColor.toString()}, '
        'نص=${textColor.toString()}',
      );
    }
  }

  /// الحصول على لون مع شفافية محددة
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  /// شفافيات موحدة للاستخدام في التطبيق
  static const double veryLightOpacity = 0.05; // شفافية خفيفة جداً
  static const double lightOpacity = 0.08; // شفافية خفيفة
  static const double mediumOpacity = 0.12; // شفافية متوسطة
  static const double strongOpacity = 0.2; // شفافية قوية
  static const double veryStrongOpacity = 0.3; // شفافية قوية جداً

  /// الحصول على لون بشفافية خفيفة جداً (5%)
  static Color withVeryLightOpacity(Color color) {
    return color.withValues(alpha: veryLightOpacity);
  }

  /// الحصول على لون بشفافية خفيفة (8%)
  static Color withLightOpacity(Color color) {
    return color.withValues(alpha: lightOpacity);
  }

  /// الحصول على لون بشفافية متوسطة (12%)
  static Color withMediumOpacity(Color color) {
    return color.withValues(alpha: mediumOpacity);
  }

  /// الحصول على لون بشفافية قوية (20%)
  static Color withStrongOpacity(Color color) {
    return color.withValues(alpha: strongOpacity);
  }

  /// الحصول على لون بشفافية قوية جداً (30%)
  static Color withVeryStrongOpacity(Color color) {
    return color.withValues(alpha: veryStrongOpacity);
  }

  /// الحصول على لون أفتح
  static Color lighten(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// الحصول على لون أغمق
  static Color darken(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// الحصول على تدرج لوني جميل
  static LinearGradient createBeautifulGradient(
    Color primaryColor, {
    Color? secondaryColor,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
  }) {
    final secondary = secondaryColor ?? lighten(primaryColor, 0.2);
    return LinearGradient(
      begin: begin,
      end: end,
      colors: [primaryColor, secondary],
      stops: const [0.0, 1.0],
    );
  }

  /// الحصول على ظل جميل للبطاقات
  static List<BoxShadow> getBeautifulCardShadow({
    Color? color,
    double elevation = 2.0,
    double blurRadius = 8.0,
    Offset offset = const Offset(0, 2),
  }) {
    final shadowColor =
        color ?? AppColors.lightTextSecondary.withValues(alpha: 0.1);
    return [
      BoxShadow(
        color: shadowColor,
        blurRadius: blurRadius,
        offset: offset,
        spreadRadius: elevation / 2,
      ),
    ];
  }
}

/// امتداد للبناء السريع للودجات الذكية
extension SmartWidgetExtension on Widget {
  /// تطبيق الثيم الذكي على أي ودجت
  Widget withSmartTheme(
    BuildContext context, {
    Color? backgroundColor,
    bool adaptToTheme = true,
  }) {
    return SmartThemeSystem.applySmartTheme(
      context,
      this,
      backgroundColor: backgroundColor,
      adaptToTheme: adaptToTheme,
    );
  }
}
