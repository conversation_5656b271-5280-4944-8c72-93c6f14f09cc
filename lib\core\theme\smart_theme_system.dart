import 'package:flutter/material.dart';
import 'app_colors.dart';

/// نظام ثيمات ذكي مبسط
/// يوفر دوال مساعدة لحساب الألوان المناسبة والتباين
class SmartThemeSystem {
  /// الحصول على لون النص المناسب للخلفية
  static Color getSmartTextColor(
    BuildContext context, {
    Color? backgroundColor,
  }) {
    final theme = Theme.of(context);
    final bgColor = backgroundColor ?? theme.scaffoldBackgroundColor;
    
    // حساب السطوع
    final luminance = bgColor.computeLuminance();
    
    // إذا كانت الخلفية فاتحة، استخدم نص داكن
    // إذا كانت الخلفية داكنة، استخدم نص فاتح
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// الحصول على لون النص المناسب للأزرار
  static Color getSmartButtonTextColor(BuildContext context, Color buttonColor) {
    final luminance = buttonColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// فحص وإصلاح مشاكل التباين
  static Map<String, Color> fixContrastIssues(
    BuildContext context,
    Color backgroundColor,
    Color textColor,
  ) {
    final smartTextColor = getSmartTextColor(context, backgroundColor: backgroundColor);
    
    return {
      'backgroundColor': backgroundColor,
      'textColor': smartTextColor,
    };
  }

  /// الحصول على معلومات الثيم الحالي
  static Map<String, dynamic> getCurrentThemeInfo(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return {
      'isDark': isDark,
      'backgroundColor': theme.scaffoldBackgroundColor,
      'textColor': theme.textTheme.bodyLarge?.color ?? (isDark ? Colors.white : Colors.black),
      'primaryColor': theme.colorScheme.primary,
    };
  }

  /// حساب التباين بين لونين
  static double calculateContrast(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();
    
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// التحقق من أن التباين مقبول (WCAG AA)
  static bool hasGoodContrast(Color foreground, Color background) {
    return calculateContrast(foreground, background) >= 4.5;
  }

  /// الحصول على لون متباين مناسب
  static Color getContrastingColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white87;
  }
}
