import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/utils/error_tracker.dart';

/// اختبارات شاملة للنظام الموحد لتتبع الأخطاء والنشاطات
/// يتأكد من عمل جميع الوظائف بشكل صحيح بعد التوحيد
void main() {
  group('🔧 اختبارات النظام الموحد لتتبع الأخطاء والنشاطات', () {
    setUp(() {
      // تنظيف البيانات قبل كل اختبار
      ErrorTracker.clearHistory();
    });

    group('📊 اختبارات تتبع الأخطاء', () {
      test('✅ يجب أن يسجل الأخطاء بشكل صحيح', () {
        // ترتيب
        const errorMessage = 'خطأ تجريبي للاختبار';
        final testError = Exception('هذا خطأ للاختبار');
        final testStackTrace = StackTrace.current;

        // تنفيذ
        ErrorTracker.captureError(
          errorMessage,
          error: testError,
          stackTrace: testStackTrace,
          context: {
            'test': true,
            'module': 'testing',
          },
        );

        // تحقق
        final errors = ErrorTracker.getRecentErrors();
        expect(errors.length, 1);
        expect(errors[0].message, errorMessage);
        expect(errors[0].error, testError);
        expect(errors[0].context?['test'], true);
        expect(errors[0].context?['module'], 'testing');
      });

      test('✅ يجب أن يحسب الإحصائيات بشكل صحيح', () {
        // ترتيب - إضافة عدة أخطاء
        for (int i = 0; i < 5; i++) {
          ErrorTracker.captureError(
            'خطأ رقم $i',
            error: Exception('خطأ $i'),
            stackTrace: StackTrace.current,
          );
        }

        // تنفيذ
        final stats = ErrorTracker.getErrorStats();

        // تحقق
        expect(stats['totalErrors'], 5);
        expect(stats['mostCommonError'], isNotNull);
        expect(stats['recentErrorRate'], isA<double>());
        expect(stats['lastErrorTimestamp'], isNotNull);
      });

      test('✅ يجب أن يمسح السجل بشكل صحيح', () {
        // ترتيب - إضافة بعض الأخطاء
        ErrorTracker.captureError(
          'خطأ للمسح',
          error: Exception('خطأ'),
          stackTrace: StackTrace.current,
        );

        // تأكد من وجود الأخطاء
        expect(ErrorTracker.getRecentErrors().length, 1);

        // تنفيذ
        ErrorTracker.clearHistory();

        // تحقق
        expect(ErrorTracker.getRecentErrors().length, 0);
        final stats = ErrorTracker.getErrorStats();
        expect(stats['totalErrors'], 0);
      });

      test('✅ يجب أن يتعامل مع trackError بشكل صحيح', () {
        // ترتيب
        final testError = Exception('خطأ trackError');
        final testStackTrace = StackTrace.current;
        const source = 'test_source';

        // تنفيذ
        ErrorTracker.trackError(
          testError,
          testStackTrace,
          source,
          context: {'additional': 'data'},
        );

        // تحقق
        final errors = ErrorTracker.getRecentErrors();
        expect(errors.length, 1);
        expect(errors[0].message, 'Error in $source');
        expect(errors[0].error, testError);
        expect(errors[0].context?['source'], source);
        expect(errors[0].context?['additional'], 'data');
      });
    });

    group('📝 اختبارات تسجيل النشاطات', () {
      test('✅ يجب أن يسجل النشاطات بشكل صحيح', () async {
        // ترتيب
        const userId = 'test_user_123';
        const userName = 'مستخدم تجريبي';
        const action = 'إضافة منتج';
        const module = 'المنتجات';
        const details = 'تم إضافة منتج جديد للاختبار';

        // تنفيذ
        await ErrorTracker.logActivity(
          userId: userId,
          userName: userName,
          action: action,
          module: module,
          details: details,
        );

        // تحقق - هذا الاختبار يتأكد من عدم حدوث أخطاء
        // في التطبيق الحقيقي، يمكن التحقق من قاعدة البيانات
        expect(true, true); // النشاط تم تسجيله بنجاح
      });

      test('✅ يجب أن يتعامل مع أخطاء تسجيل النشاطات', () async {
        // ترتيب - بيانات غير صحيحة
        const userId = '';
        const userName = '';
        const action = '';
        const module = '';

        // تنفيذ - لا يجب أن يسبب crash
        await ErrorTracker.logActivity(
          userId: userId,
          userName: userName,
          action: action,
          module: module,
        );

        // تحقق - لا يجب أن يحدث crash
        expect(true, true);
      });

      test('✅ يجب أن يحصل على النشاطات بشكل صحيح', () async {
        // تنفيذ
        final activities = await ErrorTracker.getActivities();

        // تحقق - يجب أن ترجع قائمة (حتى لو فارغة)
        expect(activities, isA<List>());
      });

      test('✅ يجب أن ينظف النشاطات القديمة', () async {
        // تنفيذ
        final result = await ErrorTracker.cleanupOldActivities(30);

        // تحقق - يجب أن ترجع boolean
        expect(result, isA<bool>());
      });
    });

    group('⚙️ اختبارات الإعدادات', () {
      test('✅ يجب أن يتحكم في تفعيل/تعطيل التسجيل', () {
        // تنفيذ
        ErrorTracker.setLoggingEnabled(false);
        ErrorTracker.setLoggingEnabled(true);

        // تحقق - لا يجب أن يحدث crash
        expect(true, true);
      });
    });

    group('🔄 اختبارات التوافق مع النظام القديم', () {
      test('✅ جميع دوال النظام القديم تعمل بشكل صحيح', () {
        // تأكد من أن جميع الدوال متوفرة ولا تسبب أخطاء
        
        // تسجيل خطأ
        ErrorTracker.captureError(
          'اختبار التوافق',
          error: Exception('خطأ توافق'),
          stackTrace: StackTrace.current,
        );

        // الحصول على الأخطاء
        final errors = ErrorTracker.getRecentErrors();
        expect(errors.length, 1);

        // الحصول على الإحصائيات
        final stats = ErrorTracker.getErrorStats();
        expect(stats, isA<Map<String, dynamic>>());

        // مسح السجل
        ErrorTracker.clearHistory();
        expect(ErrorTracker.getRecentErrors().length, 0);

        // تحقق من النجاح
        expect(true, true);
      });
    });

    group('📊 اختبارات الأداء', () {
      test('✅ يجب أن يتعامل مع عدد كبير من الأخطاء', () {
        // ترتيب - إضافة 150 خطأ (أكثر من الحد الأقصى 100)
        for (int i = 0; i < 150; i++) {
          ErrorTracker.captureError(
            'خطأ أداء $i',
            error: Exception('خطأ $i'),
            stackTrace: StackTrace.current,
          );
        }

        // تحقق - يجب أن يحتفظ بـ 100 خطأ فقط
        final errors = ErrorTracker.getRecentErrors();
        expect(errors.length, 100);

        // تحقق من أن الأخطاء الأحدث محفوظة
        expect(errors.first.message, contains('خطأ أداء 149'));
      });

      test('✅ يجب أن يحسب معدل الأخطاء بدقة', () {
        // ترتيب - إضافة أخطاء في أوقات مختلفة
        for (int i = 0; i < 10; i++) {
          ErrorTracker.captureError(
            'خطأ معدل $i',
            error: Exception('خطأ $i'),
            stackTrace: StackTrace.current,
          );
        }

        // تنفيذ
        final stats = ErrorTracker.getErrorStats();

        // تحقق
        expect(stats['recentErrorRate'], isA<double>());
        expect(stats['recentErrorRate'], greaterThanOrEqualTo(0));
      });
    });
  });
}
