import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../../../core/utils/index.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/services/session_manager.dart';
import '../../../core/services/setup_flag_service.dart';

import '../../../core/theme/index.dart'; // استخدام الثيم الموحد
import '../../shared/widgets/custom_card.dart';
import '../../../features/branches/models/branch.dart';
import '../../branches/services/branch_service.dart';
import '../../../core/auth/services/auth_service.dart';
import '../../../features/users/models/user.dart';
import '../../../core/auth/models/user_role.dart';
import '../../../features/users/presenters/permission_presenter.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _usernameOrEmailController =
      TextEditingController(); // Cambiado de _emailController a _usernameOrEmailController
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _rememberMe = false;
  bool _showRegisterButton =
      true; // Variable para controlar la visibilidad del botón de registro

  // إضافة متغيرات للفروع
  String? _selectedBranchId;
  List<Branch> _branches = [];
  bool _isLoadingBranches = false;

  // إضافة متغيرات للرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // قائمة الأدوار
  List<UserRole> _roles = [];
  bool _isLoadingRoles = false;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );

    _animationController.forward();

    // تحميل بيانات تسجيل الدخول المحفوظة
    _loadSavedCredentials();

    // تحميل الفروع
    _loadBranches();

    // تحميل الأدوار بعد بناء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadRoles();
        _processArguments();
      }
    });
  }

  /// معالجة الوسيطات المرسلة من شاشة إنشاء الحساب
  void _processArguments() {
    // استخدام Future.delayed لضمان تنفيذ الكود بعد بناء الشاشة بالكامل
    Future.delayed(Duration.zero, () {
      if (!mounted) return;

      final args = ModalRoute.of(context)?.settings.arguments;
      AppLogger.info('معالجة الوسيطات: $args');

      if (args != null && args is Map<String, dynamic>) {
        AppLogger.info('تم استلام وسيطات صالحة من نوع Map');

        setState(() {
          if (args.containsKey('username')) {
            _usernameOrEmailController.text = args['username'] as String;
            AppLogger.info('تم تعيين اسم المستخدم: ${args['username']}');
          }
          if (args.containsKey('password')) {
            _passwordController.text = args['password'] as String;
            AppLogger.info('تم تعيين كلمة المرور');
          }

          // إخفاء زر التسجيل عند استلام بيانات من شاشة تعديل الحساب
          _showRegisterButton = false;
          AppLogger.info('تم إخفاء زر التسجيل');
        });

        AppLogger.info('تم استلام بيانات المستخدم من شاشة تعديل الحساب');

        // حفظ حالة إخفاء زر التسجيل في التخزين المحلي
        _saveHasLoggedInBefore();
      } else {
        AppLogger.info(
            'لم يتم استلام وسيطات أو الوسيطات ليست من النوع المتوقع');
      }
    });
  }

  /// حفظ حالة تسجيل الدخول السابق
  Future<void> _saveHasLoggedInBefore() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('hasLoggedInBefore', true);
      AppLogger.info('✅ تم حفظ حالة إخفاء زر التسجيل بنجاح');
    } catch (e) {
      AppLogger.error('❌ خطأ في حفظ حالة إخفاء زر التسجيل: $e');
    }
  }

  Future<void> _loadSavedCredentials() async {
    final prefs = await SharedPreferences.getInstance();

    // التحقق من وجود بيانات مؤقتة من شاشة التسجيل
    final tempUsername = prefs.getString('temp_username');
    final tempPassword = prefs.getString('temp_password');

    if (tempUsername != null && tempPassword != null) {
      AppLogger.info(
          'تم العثور على بيانات مؤقتة محفوظة: username=$tempUsername');

      setState(() {
        _usernameOrEmailController.text = tempUsername;
        _passwordController.text = tempPassword;
        _showRegisterButton = false;
      });

      // حذف البيانات المؤقتة بعد استخدامها
      await prefs.remove('temp_username');
      await prefs.remove('temp_password');

      // حفظ حالة إخفاء زر التسجيل
      await _saveHasLoggedInBefore();

      AppLogger.info('تم تحميل البيانات المؤقتة وحذفها بعد الاستخدام');
    } else {
      // تحميل بيانات تسجيل الدخول المحفوظة العادية
      setState(() {
        _usernameOrEmailController.text =
            prefs.getString('usernameOrEmail') ?? '';
        _passwordController.text = prefs.getString('password') ?? '';
        _rememberMe = prefs.getBool('rememberMe') ?? false;

        // Verificar si ya se ha iniciado sesión exitosamente antes
        _showRegisterButton = !(prefs.getBool('hasLoggedInBefore') ?? false);

        // تحميل معرف الفرع المحفوظ
        final savedBranchId = prefs.getString('branchId');
        if (savedBranchId != null && savedBranchId.isNotEmpty) {
          _selectedBranchId = savedBranchId;
          AppLogger.info('تم تحميل معرف الفرع المحفوظ: $_selectedBranchId');
        }
      });
    }
  }

  // هذه الدالة لم تعد مستخدمة حيث تم نقل وظيفتها إلى _performLoginInBackground
  // Future<void> _saveCredentials() async {
  //   if (_rememberMe) {
  //     final prefs = await SharedPreferences.getInstance();
  //     await prefs.setString('email', _emailController.text);
  //     await prefs.setString('password', _passwordController.text);
  //     await prefs.setBool('rememberMe', true);
  //   } else {
  //     final prefs = await SharedPreferences.getInstance();
  //     await prefs.remove('email');
  //     await prefs.remove('password');
  //     await prefs.setBool('rememberMe', false);
  //   }
  // }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // استدعاء معالجة الوسيطات عند تغيير التبعيات
    // هذا يضمن استدعاء الدالة بعد بناء الشاشة وعند تغيير الوسيطات
    _processArguments();
  }

  @override
  void dispose() {
    _usernameOrEmailController.dispose();
    _passwordController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  /// تحميل قائمة الفروع
  Future<void> _loadBranches() async {
    setState(() {
      _isLoadingBranches = true;
    });

    try {
      AppLogger.info('بدء تحميل الفروع...');

      final branchService = BranchService();
      final branches = await branchService.getBranches();

      AppLogger.info('تم استرجاع ${branches.length} فرع من الخدمة');

      // طباعة تفاصيل الفروع للتشخيص
      for (var branch in branches) {
        AppLogger.info(
            'فرع: ${branch.name}, معرف: ${branch.id}, رئيسي: ${branch.isMain}');
      }

      setState(() {
        _branches = branches;
        _isLoadingBranches = false;

        // تحديد الفرع الرئيسي كافتراضي
        if (branches.isNotEmpty) {
          final mainBranch = branches.firstWhere(
            (branch) => branch.isMain,
            orElse: () => branches.first,
          );

          _selectedBranchId = mainBranch.id;
          AppLogger.info(
              'تم تحديد الفرع: ${mainBranch.name} (${mainBranch.id})');
        } else {
          AppLogger.warning('لا توجد فروع متاحة! إنشاء فرع افتراضي للعرض فقط');
          _branches = [Branch(id: 'default', name: 'الفرع الرئيسي')];
          _selectedBranchId = 'default';
        }
      });
    } catch (e) {
      AppLogger.error('خطأ في تحميل الفروع: $e');
      setState(() {
        _isLoadingBranches = false;
        // إضافة فرع افتراضي في حالة الخطأ
        _branches = [Branch(id: 'default', name: 'الفرع الرئيسي')];
        _selectedBranchId = 'default';
      });
    }
  }

  /// تحميل قائمة الأدوار
  Future<void> _loadRoles() async {
    if (!mounted) return;

    setState(() {
      _isLoadingRoles = true;
    });

    try {
      AppLogger.info('بدء تحميل الأدوار...');

      // التحقق من وجود السياق قبل استخدامه
      if (!mounted) return;

      final permissionPresenter =
          Provider.of<PermissionPresenter>(context, listen: false);
      await permissionPresenter.loadRoles();

      // التحقق مرة أخرى بعد العملية غير المتزامنة
      if (!mounted) return;

      final roles = permissionPresenter.roles;

      AppLogger.info('تم استرجاع ${roles.length} دور من الخدمة');

      setState(() {
        _roles = roles;
        _isLoadingRoles = false;
      });
    } catch (e) {
      AppLogger.error('خطأ في تحميل الأدوار: $e');

      // التحقق من وجود السياق قبل استخدامه
      if (!mounted) return;

      setState(() {
        _isLoadingRoles = false;
        // إضافة أدوار افتراضية في حالة الخطأ
        _roles = [
          UserRole(
            id: 'admin',
            name: 'admin',
            displayName: 'مدير النظام',
            permissions: [],
          ),
          UserRole(
            id: 'manager',
            name: 'manager',
            displayName: 'مدير',
            permissions: [],
          ),
          UserRole(
            id: 'accountant',
            name: 'accountant',
            displayName: 'محاسب',
            permissions: [],
          ),
          UserRole(
            id: 'cashier',
            name: 'cashier',
            displayName: 'أمين صندوق',
            permissions: [],
          ),
          UserRole(
            id: 'user',
            name: 'user',
            displayName: 'مستخدم',
            permissions: [],
          ),
          UserRole(
            id: 'guest',
            name: 'guest',
            displayName: 'زائر',
            permissions: [],
          ),
        ];
      });
    }
  }

  /// عرض معلومات الأدوار
  void _showRolesInfo() {
    // تحميل الأدوار إذا لم تكن محملة بالفعل
    if (_roles.isEmpty && !_isLoadingRoles) {
      _loadRoles();
    }

    // استخدام نسخة محلية من الأدوار لتجنب مشاكل البناء
    final currentRoles = List<UserRole>.from(_roles);

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.shield,
                color: Theme.of(dialogContext).colorScheme.primary),
            const SizedBox(width: 8),
            const Text('معلومات الأدوار'),
          ],
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: _isLoadingRoles
              ? const Center(child: CircularProgressIndicator())
              : ListView(
                  shrinkWrap: true,
                  children: [
                    Text(
                      'يوفر النظام عدة أدوار مختلفة للمستخدمين، كل دور له صلاحيات محددة:',
                      style: Theme.of(dialogContext)
                          .textTheme
                          .titleSmall
                          ?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    ..._buildRoleInfoItemsWithRoles(
                        currentRoles, dialogContext),
                  ],
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة معلومات الأدوار باستخدام قائمة أدوار محددة
  List<Widget> _buildRoleInfoItemsWithRoles(
      List<UserRole> roles, BuildContext dialogContext) {
    final List<Widget> items = [];
    final theme = Theme.of(dialogContext);

    // إذا كانت قائمة الأدوار فارغة، استخدم الأدوار الافتراضية
    final rolesList = roles.isEmpty
        ? [
            UserRole(
              id: 'admin',
              name: 'admin',
              displayName: 'مدير النظام',
              permissions: [],
            ),
            UserRole(
              id: 'manager',
              name: 'manager',
              displayName: 'مدير',
              permissions: [],
            ),
            UserRole(
              id: 'accountant',
              name: 'accountant',
              displayName: 'محاسب',
              permissions: [],
            ),
            UserRole(
              id: 'cashier',
              name: 'cashier',
              displayName: 'أمين صندوق',
              permissions: [],
            ),
            UserRole(
              id: 'user',
              name: 'user',
              displayName: 'مستخدم',
              permissions: [],
            ),
            UserRole(
              id: 'guest',
              name: 'guest',
              displayName: 'زائر',
              permissions: [],
            ),
          ]
        : roles;

    // وصف كل دور
    final Map<String, String> roleDescriptions = {
      'admin':
          'يمتلك جميع الصلاحيات في النظام ويمكنه إدارة المستخدمين والإعدادات.',
      'manager': 'يمكنه إدارة العمليات اليومية والتقارير والموظفين.',
      'accountant': 'يمكنه إدارة الحسابات والفواتير والتقارير المالية.',
      'cashier': 'يمكنه إدارة المبيعات والمشتريات وعمليات الدفع.',
      'user': 'يمكنه استخدام النظام بصلاحيات محدودة حسب المجموعة.',
      'guest': 'يمكنه استعراض بعض البيانات فقط دون إجراء أي تعديلات.',
    };

    // إنشاء عنصر لكل دور
    for (final role in rolesList) {
      final roleId = role.id.toLowerCase();
      final description =
          roleDescriptions[roleId] ?? 'دور مخصص بصلاحيات محددة.';
      final roleColor = _getRoleColor(roleId, theme);

      items.add(
        Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              //backgroundColor: roleColor.withValues(alpha:0.2),
              backgroundColor: roleColor.withValues(alpha: 0.2),
              child: Icon(Icons.shield, color: roleColor),
            ),
            title: Text(role.displayName,
                style: theme.textTheme.titleSmall
                    ?.copyWith(fontWeight: FontWeight.bold)),
            subtitle: Text(description, style: theme.textTheme.bodySmall),
          ),
        ),
      );
    }

    return items;
  }

  /// الحصول على لون الدور
  Color _getRoleColor(String role, ThemeData theme) {
    switch (role.toLowerCase()) {
      case 'admin':
        return theme.colorScheme.primary;
      case 'manager':
        return theme.colorScheme.secondary; // Changed from primaryLight
      case 'accountant':
        return theme.colorScheme.tertiary; // Changed from AppColors.accent
      case 'cashier':
        return theme.colorScheme
            .primaryContainer; // Changed from AppColors.success, using a theme color
      case 'user':
        return theme.colorScheme.onSurface
            .withValues(alpha: 0.6); // Changed from AppColors.secondary
      case 'guest':
        return theme.colorScheme.onSurface
            //.withValues(alpha:0.4); // Changed from AppColors.secondary
            .withValues(alpha: 0.4); // Changed from AppColors.secondary
      default:
        return theme.colorScheme.primary; // Changed from primaryDark
    }
  }

  void _handleLogin() async {
    if (_formKey.currentState?.validate() ?? false) {
      // التحقق من اختيار الفرع
      if (_selectedBranchId == null || _selectedBranchId!.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            // Removed const
            content: const Text('الرجاء اختيار الفرع'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
        return;
      }

      try {
        // عرض مؤشر التحميل
        _showLoadingDialog();

        // حفظ بيانات الدخول إذا تم اختيار تذكرني
        if (_rememberMe) {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString(
              'usernameOrEmail', _usernameOrEmailController.text);
          await prefs.setString('password', _passwordController.text);
          await prefs.setString('branchId', _selectedBranchId!);
          await prefs.setBool('rememberMe', true);
        } else {
          final prefs = await SharedPreferences.getInstance();
          await prefs.remove('usernameOrEmail');
          await prefs.remove('password');
          await prefs.remove('branchId');
          await prefs.setBool('rememberMe', false);
        }

        // Guardar que el usuario ha iniciado sesión exitosamente
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('hasLoggedInBefore', true);

        // استخدام خدمة المصادقة الموحدة
        final authService = AuthService();
        final user = await authService.login(
          _usernameOrEmailController.text, // اسم المستخدم أو البريد الإلكتروني
          _passwordController.text, // كلمة المرور
          branchId: _selectedBranchId, // معرف الفرع
        );

        if (user == null) {
          throw Exception('فشل في تسجيل الدخول. تأكد من صحة بيانات الاعتماد.');
        }

        // تسجيل الدخول في مدير الجلسة
        final bool loginSuccess = await SessionManager.login(
          user.id, // معرف المستخدم
          user.username, // اسم المستخدم
          user.roleId ?? 'user', // دور المستخدم (استخدام 'user' كقيمة افتراضية)
          branchId: _selectedBranchId,
        );

        if (!loginSuccess) {
          throw Exception('فشل في حفظ بيانات تسجيل الدخول');
        }

        // تحديث حالة الإعداد في الملف المحلي أيضًا
        final bool fileSetupSaved =
            await SetupFlagService.setSetupCompleted(true);
        final bool fileFirstLaunchSaved =
            await SetupFlagService.setFirstLaunch(false);

        // التحقق من نجاح حفظ الإعدادات في الملف المحلي
        if (!fileSetupSaved || !fileFirstLaunchSaved) {
          AppLogger.error(
              '❌ فشل في حفظ حالة الإعداد في الملف المحلي عند تسجيل الدخول: fileSetupSaved=$fileSetupSaved, fileFirstLaunchSaved=$fileFirstLaunchSaved');
          throw Exception('فشل في حفظ حالة الإعداد في الملف المحلي');
        } else {
          AppLogger.info(
              '✅ تم حفظ حالة الإعداد في الملف المحلي بنجاح عند تسجيل الدخول');
        }

        AppLogger.info(
            'تم تسجيل الدخول بنجاح: ${user.username} (${user.fullName}) في الفرع: $_selectedBranchId');

        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();
        }

        // انتقال إلى لوحة المعلومات (الداش بورد)
        if (mounted) {
          Navigator.of(context).pushReplacementNamed(AppRoutes.dashboard);
        }
      } catch (e) {
        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();

          // عرض رسالة خطأ
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              // Removed const
              content: Text('فشل تسجيل الدخول: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }

        AppLogger.error('خطأ في تسجيل الدخول: $e');
        ErrorTracker.captureError(
          'فشل في تسجيل الدخول',
          error: e,
          stackTrace: StackTrace.current,
          context: {
            'username': _usernameOrEmailController.text,
            'branchId': _selectedBranchId,
          },
        );
      }
    }
  }

  // عرض مؤشر التحميل
  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  void _handleGuestLogin() async {
    // التحقق من اختيار الفرع
    if (_selectedBranchId == null || _selectedBranchId!.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          // Removed const
          content: const Text('الرجاء اختيار الفرع'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    try {
      // عرض مؤشر التحميل
      _showLoadingDialog();

      // إنشاء مستخدم زائر مؤقت
      final guestUser = User(
        id: 'guest',
        username: 'guest',
        password: '',
        fullName: 'زائر',
        email: '',
        phone: '',
        roleId: 'guest',
        userGroupId: 'guest',
        isActive: true,
      );

      // تسجيل الدخول كزائر
      final bool loginSuccess = await SessionManager.login(
        guestUser.id, // معرف المستخدم
        guestUser.username, // اسم المستخدم
        guestUser.roleId ?? 'guest', // دور المستخدم
        branchId: _selectedBranchId, // إضافة معرف الفرع
      );

      if (!loginSuccess) {
        throw Exception('فشل في حفظ بيانات تسجيل الدخول كزائر');
      }

      // تحديث حالة الإعداد في الملف المحلي أيضًا
      final bool fileSetupSaved =
          await SetupFlagService.setSetupCompleted(true);
      final bool fileFirstLaunchSaved =
          await SetupFlagService.setFirstLaunch(false);

      // التحقق من نجاح حفظ الإعدادات في الملف المحلي
      if (!fileSetupSaved || !fileFirstLaunchSaved) {
        AppLogger.error(
            '❌ فشل في حفظ حالة الإعداد في الملف المحلي عند تسجيل الدخول كزائر: fileSetupSaved=$fileSetupSaved, fileFirstLaunchSaved=$fileFirstLaunchSaved');
        throw Exception('فشل في حفظ حالة الإعداد في الملف المحلي');
      } else {
        AppLogger.info(
            '✅ تم حفظ حالة الإعداد في الملف المحلي بنجاح عند تسجيل الدخول كزائر');
      }

      // حفظ معرف الفرع في خدمة المصادقة
      final authService = AuthService();
      await authService.setCurrentBranch(_selectedBranchId!);

      AppLogger.info(
          'تم تسجيل الدخول كزائر بنجاح في الفرع: $_selectedBranchId');

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();
      }

      // انتقال إلى لوحة المعلومات (الداش بورد)
      if (mounted) {
        Navigator.of(context).pushReplacementNamed(AppRoutes.dashboard);
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();

        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            // Removed const
            content: Text('فشل تسجيل الدخول كزائر: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }

      AppLogger.error('خطأ في تسجيل الدخول كزائر: $e');
      ErrorTracker.captureError(
        'فشل في تسجيل الدخول كزائر',
        error: e,
        stackTrace: StackTrace.current,
        context: {
          'branchId': _selectedBranchId,
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    // تهيئة نظام التخطيط
    Layout.init(context);

    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: DynamicColors.background(context),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isDark
                ? AppColors
                    .darkGradient // Keep dark gradient for now, or make it themeable
                : [
                    Theme.of(context).colorScheme.surface,
                    Theme.of(context).colorScheme.surfaceContainerHighest,
                  ],
          ),
        ),
        child: SafeArea(
          child: LayoutBuilder(builder: (context, constraints) {
            return SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                  horizontal: Layout.w(6), // 6% من عرض الشاشة
                  vertical: Layout.h(2), // 2% من ارتفاع الشاشة
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SizedBox(height: Layout.h(3)),
                    _buildAppLogo(constraints),
                    SizedBox(height: Layout.h(3)),
                    Text(
                      'مرحباً بك في تاجر بلس',
                      style:
                          Theme.of(context).textTheme.headlineMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                                fontSize: Layout.getResponsiveFontSize(24),
                              ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: Layout.h(1)),
                    Text(
                      'برنامجك الشامل لإدارة الأعمال التجارية',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurfaceVariant, // Using onSurfaceVariant for secondary text
                            fontSize: Layout.getResponsiveFontSize(16),
                          ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: Layout.h(4)),
                    CustomCard(
                      child: Padding(
                        padding: EdgeInsets.all(Layout.w(5)),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              FadeTransition(
                                opacity: _fadeAnimation,
                                child: _buildTextField(
                                  controller: _usernameOrEmailController,
                                  label: 'اسم المستخدم أو البريد الإلكتروني',
                                  hint:
                                      'أدخل اسم المستخدم أو البريد الإلكتروني',
                                  icon: Icons.person,
                                  keyboardType: TextInputType.text,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال اسم المستخدم أو البريد الإلكتروني';
                                    }
                                    return null;
                                  },
                                ),
                              ),
                              SizedBox(height: Layout.h(2)),
                              FadeTransition(
                                opacity: _fadeAnimation,
                                child: _buildPasswordField(),
                              ),
                              SizedBox(height: Layout.h(2)),
                              FadeTransition(
                                opacity: _fadeAnimation,
                                child: _buildBranchDropdown(),
                              ),
                              SizedBox(height: Layout.h(2)),
                              FadeTransition(
                                opacity: _fadeAnimation,
                                child: _buildRememberMeRow(),
                              ),
                              SizedBox(height: Layout.h(3)),
                              _buildLoginButton(),
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: Layout.h(3)),
                    _buildSignUpRow(),
                    SizedBox(height: Layout.h(2)),
                    _buildGuestLoginButton(),
                    SizedBox(height: Layout.h(2)),
                    _buildRolesInfoButton(),
                    SizedBox(height: Layout.h(3)),
                    _buildSocialButtonsRow(),
                  ],
                ));
          }),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    required String? Function(String?) validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      textDirection: TextDirection.ltr,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, size: Layout.getResponsiveIconSize(22)),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: Layout.w(4),
          vertical: Layout.h(2),
        ),
      ),
      validator: validator,
      style: Theme.of(context)
          .textTheme
          .bodyLarge
          ?.copyWith(fontSize: Layout.getResponsiveFontSize(16)),
    );
  }

  Widget _buildPasswordField() {
    final theme = Theme.of(context);
    return TextFormField(
      controller: _passwordController,
      obscureText: !_isPasswordVisible,
      decoration: InputDecoration(
        labelText: 'كلمة المرور',
        hintText: 'أدخل كلمة المرور',
        prefixIcon: Icon(Icons.lock, size: Layout.getResponsiveIconSize(22)),
        suffixIcon: IconButton(
          icon: Icon(
            _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
            size: Layout.getResponsiveIconSize(22),
          ),
          onPressed: () {
            setState(() {
              _isPasswordVisible = !_isPasswordVisible;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: Layout.w(4),
          vertical: Layout.h(2),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'الرجاء إدخال كلمة المرور';
        }
        if (value.length < 6) {
          return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
        return null;
      },
      style: theme.textTheme.bodyLarge
          ?.copyWith(fontSize: Layout.getResponsiveFontSize(16)),
    );
  }

  Widget _buildRememberMeRow() {
    final theme = Theme.of(context);
    return Row(
      children: [
        Transform.scale(
          scale: Layout.isMobile() ? 0.9 : 1.0,
          child: Checkbox(
            value: _rememberMe,
            activeColor: theme.colorScheme.primary,
            onChanged: (value) {
              setState(() {
                _rememberMe = value ?? false;
              });
            },
          ),
        ),
        Text(
          'تذكرني',
          style: theme.textTheme.bodyMedium
              ?.copyWith(fontSize: Layout.getResponsiveFontSize(14)),
        ),
        const Spacer(),
        TextButton(
          onPressed: () {
            // TODO: نسيت كلمة المرور
          },
          child: Text(
            'نسيت كلمة المرور؟',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontSize: Layout.getResponsiveFontSize(14),
              color: theme.colorScheme.primary,
            ),
          ),
        ),
      ],
    );
  }

  /// بناء حقل الفرع (تم استبدال القائمة المنسدلة بحقل نصي غير قابل للتعديل)
  Widget _buildBranchDropdown() {
    // استخدام الفرع الرئيسي دائمًا
    if (_branches.isNotEmpty && _selectedBranchId == null) {
      final mainBranch = _branches.firstWhere(
        (branch) => branch.isMain,
        orElse: () => _branches.first,
      );
      _selectedBranchId = mainBranch.id;
    }

    // إذا لم تكن هناك فروع، استخدم فرع افتراضي
    if (_branches.isEmpty) {
      _branches = [Branch(id: 'default', name: 'الفرع الرئيسي')];
      _selectedBranchId = 'default';
    }

    // الحصول على اسم الفرع المحدد
    String branchName = 'الفرع الرئيسي';
    if (_selectedBranchId != null) {
      final selectedBranch = _branches.firstWhere(
        (branch) => branch.id == _selectedBranchId,
        orElse: () => Branch(id: 'default', name: 'الفرع الرئيسي'),
      );
      branchName = selectedBranch.name;
    }

    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.colorScheme.outline),
        borderRadius: BorderRadius.circular(12),
        color: theme.colorScheme.surfaceContainerHighest.withValues(
            alpha: 0.5), // خلفية ناعمة للإشارة إلى أنه غير قابل للتعديل
      ),
      child: _isLoadingBranches
          ? const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 12.0),
                child: CircularProgressIndicator(),
              ),
            )
          : TextFormField(
              enabled: false, // غير قابل للتعديل
              initialValue: branchName,
              decoration: InputDecoration(
                labelText: 'الفرع',
                prefixIcon: Icon(
                  Icons.store,
                  size: Layout.getResponsiveIconSize(22),
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: Layout.w(4),
                  vertical: Layout.h(2),
                ),
              ),
              style: theme.textTheme.titleMedium?.copyWith(
                fontSize: Layout.getResponsiveFontSize(16),
              ),
            ),
    );
  }

  Widget _buildLoginButton() {
    final theme = Theme.of(context);
    return FadeTransition(
      opacity: _fadeAnimation,
      child: ElevatedButton(
        onPressed: _handleLogin,
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          padding: EdgeInsets.symmetric(vertical: Layout.h(2)),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
          textStyle: theme.textTheme.labelLarge?.copyWith(
            fontSize: Layout.getResponsiveFontSize(16),
            fontWeight: FontWeight.bold,
          ),
        ),
        child: const Text(
          'تسجيل الدخول',
        ),
      ),
    );
  }

  Widget _buildSignUpRow() {
    final theme = Theme.of(context);
    // إذا كان _showRegisterButton هو false، لا تعرض زر التسجيل
    if (!_showRegisterButton) {
      return const SizedBox.shrink(); // لا تعرض شيئًا
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'ليس لديك حساب؟',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontSize: Layout.getResponsiveFontSize(14),
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pushNamed('/register');
          },
          child: Text(
            'إنشاء حساب جديد',
            style: theme.textTheme.labelLarge?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
              fontSize: Layout.getResponsiveFontSize(14),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGuestLoginButton() {
    final theme = Theme.of(context);
    return TextButton.icon(
      onPressed: _handleGuestLogin,
      icon: const Icon(Icons.person_outline),
      label: Text(
        'الدخول كزائر',
        style: theme.textTheme.labelLarge?.copyWith(
          fontSize: Layout.getResponsiveFontSize(16),
        ),
      ),
      style: TextButton.styleFrom(
        foregroundColor: theme.colorScheme.secondary,
      ),
    );
  }

  Widget _buildRolesInfoButton() {
    final theme = Theme.of(context);
    return TextButton.icon(
      onPressed: _showRolesInfo,
      icon: const Icon(Icons.info_outline),
      label: Text(
        'معلومات الأدوار والصلاحيات',
        style: theme.textTheme.labelLarge?.copyWith(
          fontSize: Layout.getResponsiveFontSize(16),
        ),
      ),
      style: TextButton.styleFrom(
        foregroundColor:
            theme.colorScheme.tertiary, // Assuming info maps to tertiary
      ),
    );
  }

  Widget _buildSocialButtonsRow() {
    final theme = Theme.of(context);
    final buttonSize = Layout.getResponsiveIconSize(30);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildSocialButton(
          icon: Icons.facebook,
          color: theme.colorScheme.tertiary, // Assuming info maps to tertiary
          size: buttonSize,
          onTap: () {
            // فتح صفحة فيسبوك
            _launchUrl('https://www.facebook.com');
          },
        ),
        SizedBox(width: Layout.w(5)),
        _buildSocialButton(
          icon: FontAwesomeIcons.whatsapp,
          color: theme.colorScheme
              .secondary, // Assuming success maps to secondary or a custom success color
          size: buttonSize,
          onTap: () {
            // فتح واتساب مع رسالة
            _openWhatsApp();
          },
        ),
        SizedBox(width: Layout.w(5)),
        _buildSocialButton(
          icon: Icons.g_mobiledata_rounded,
          color: theme.colorScheme
              .errorContainer, // Assuming warning maps to errorContainer or a custom warning color
          size: buttonSize,
          onTap: () {
            // فتح جوجل
            _launchUrl('https://www.google.com');
          },
        ),
      ],
    );
  }

  /// فتح رابط في المتصفح
  Future<void> _launchUrl(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        AppLogger.error('لا يمكن فتح الرابط: $url');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              // Removed const
              content: Text('لا يمكن فتح الرابط: $url'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في فتح الرابط: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            // Removed const
            content: Text('خطأ في فتح الرابط: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// فتح واتساب مع رسالة
  Future<void> _openWhatsApp() async {
    try {
      const String phoneNumber = '967770119544';
      const String message =
          'مرحباً، أنا مستخدم لتطبيق تاجر بلس وأحتاج إلى مساعدة.';

      // تشفير الرسالة
      final String encodedMessage = Uri.encodeComponent(message);

      // إنشاء رابط واتساب
      final String whatsappUrl =
          'https://wa.me/$phoneNumber?text=$encodedMessage';

      // فتح الرابط
      await _launchUrl(whatsappUrl);
    } catch (e) {
      AppLogger.error('خطأ في فتح واتساب: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            // Removed const
            content: Text('خطأ في فتح واتساب: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Widget _buildAppLogo(BoxConstraints constraints) {
    // حساب الحجم المناسب بناءً على عرض الشاشة
    final logoSize = constraints.maxWidth * 0.28; // 28% من عرض الشاشة
    final iconSize = logoSize * 0.25; // 25% من حجم الشعار

    return Center(
      child: Container(
        width: logoSize,
        height: logoSize,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            // Consider making this gradient themeable if AppColors.primaryGradient is static
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: (AppColors.availableThemes[
                    Provider.of<ThemeManager>(context, listen: false)
                        .colorTheme]?['gradient'] as List<Color>?) ??
                AppColors.primaryGradient,
          ),
          borderRadius:
              BorderRadius.circular(logoSize * 0.15), // 15% من حجم الشعار
          boxShadow: [
            BoxShadow(
              color:
                  Theme.of(context).colorScheme.shadow.withValues(alpha: 0.15),
              blurRadius: 8,
              spreadRadius: 1,
              offset: const Offset(0, 4),
            )
          ], // AppColors.createSoftShadow() might need to be theme aware
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_cart,
                  size: iconSize,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
                SizedBox(width: iconSize * 0.3),
                Icon(
                  Icons.store,
                  size: iconSize,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
              ],
            ),
            SizedBox(height: iconSize * 0.5),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inventory,
                  size: iconSize,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
                SizedBox(width: iconSize * 0.3),
                Icon(
                  Icons.people,
                  size: iconSize,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
              ],
            ),
            SizedBox(height: iconSize * 0.3),
            Text(
              'تاجر بلس',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontSize: iconSize * 0.6,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required Color
        color, // This color is now passed from the theme-aware _buildSocialButtonsRow
    required double size,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(size),
      child: Container(
        padding: EdgeInsets.all(size * 0.4),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1), // Use withOpacity
          borderRadius: BorderRadius.circular(size),
          border: Border.all(
              color: color.withValues(alpha: 0.3), width: 1), // Use withOpacity
        ),
        child: Icon(
          icon,
          color: color,
          size: size,
        ),
      ),
    );
  }
}
