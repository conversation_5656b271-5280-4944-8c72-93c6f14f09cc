import 'package:flutter/material.dart';
import 'dart:math' as math;

/// نظام الألوان المتقدم والذكي لتطبيق تاجر بلس
/// 🎨 خوارزميات متطورة لإنشاء ألوان متناسقة وجميلة
/// 🧠 ذكاء اصطناعي لاختيار الألوان المناسبة
/// 🌈 نظام تدرجات لونية متقدم
/// ♿ ضمان إمكانية الوصول والتباين المناسب
class AdvancedColorSystem {
  AdvancedColorSystem._();

  // ========== خوارزميات الألوان الذكية ==========

  /// إنشاء لوحة ألوان متناسقة من لون أساسي
  static Map<String, Color> generateHarmoniousPalette(Color baseColor) {
    final hsl = HSLColor.fromColor(baseColor);
    
    return {
      'primary': baseColor,
      'primaryDark': _adjustLightness(hsl, -0.15).toColor(),
      'primaryLight': _adjustLightness(hsl, 0.25).toColor(),
      'primaryVeryLight': _adjustLightness(hsl, 0.4).toColor(),
      'secondary': _generateSecondaryColor(hsl).toColor(),
      'accent': _generateAccentColor(hsl).toColor(),
      'surface': _generateSurfaceColor(hsl, false).toColor(),
      'surfaceDark': _generateSurfaceColor(hsl, true).toColor(),
      'onSurface': _generateOnSurfaceColor(hsl, false).toColor(),
      'onSurfaceDark': _generateOnSurfaceColor(hsl, true).toColor(),
    };
  }

  /// إنشاء تدرج لوني ذكي
  static LinearGradient createIntelligentGradient(
    Color primaryColor, {
    GradientDirection direction = GradientDirection.topLeftToBottomRight,
    GradientStyle style = GradientStyle.subtle,
  }) {
    final hsl = HSLColor.fromColor(primaryColor);
    List<Color> colors;
    
    switch (style) {
      case GradientStyle.subtle:
        colors = [
          primaryColor,
          _adjustLightness(hsl, -0.1).toColor(),
        ];
        break;
      case GradientStyle.vibrant:
        colors = [
          _adjustSaturation(hsl, 0.1).toColor(),
          _adjustLightness(hsl, -0.2).toColor(),
        ];
        break;
      case GradientStyle.dramatic:
        colors = [
          _adjustSaturation(hsl, 0.2).toColor(),
          _adjustLightness(hsl, -0.3).toColor(),
        ];
        break;
      case GradientStyle.complementary:
        colors = [
          primaryColor,
          _generateComplementaryColor(hsl).toColor(),
        ];
        break;
    }

    return LinearGradient(
      begin: _getGradientAlignment(direction).begin,
      end: _getGradientAlignment(direction).end,
      colors: colors,
      stops: const [0.0, 1.0],
    );
  }

  /// إنشاء تدرج متعدد الألوان
  static LinearGradient createMultiColorGradient(
    Color primaryColor, {
    int colorCount = 3,
    GradientDirection direction = GradientDirection.topLeftToBottomRight,
  }) {
    final hsl = HSLColor.fromColor(primaryColor);
    final colors = <Color>[];
    final stops = <double>[];
    
    for (int i = 0; i < colorCount; i++) {
      final progress = i / (colorCount - 1);
      final adjustedHue = (hsl.hue + (progress * 60)) % 360;
      final adjustedLightness = hsl.lightness - (progress * 0.1);
      
      colors.add(HSLColor.fromAHSL(
        1.0,
        adjustedHue,
        hsl.saturation,
        adjustedLightness.clamp(0.0, 1.0),
      ).toColor());
      
      stops.add(progress);
    }

    return LinearGradient(
      begin: _getGradientAlignment(direction).begin,
      end: _getGradientAlignment(direction).end,
      colors: colors,
      stops: stops,
    );
  }

  /// فحص وضمان التباين المناسب
  static Color ensureAccessibleContrast(
    Color textColor,
    Color backgroundColor, {
    double minimumRatio = 4.5,
  }) {
    final currentRatio = _calculateContrastRatio(textColor, backgroundColor);
    
    if (currentRatio >= minimumRatio) {
      return textColor;
    }

    // تجربة تعديل الإضاءة
    final textHsl = HSLColor.fromColor(textColor);
    final backgroundLuminance = backgroundColor.computeLuminance();
    
    // إذا كانت الخلفية فاتحة، نجعل النص أغمق
    if (backgroundLuminance > 0.5) {
      for (double lightness = textHsl.lightness; lightness >= 0.0; lightness -= 0.05) {
        final adjustedColor = textHsl.withLightness(lightness).toColor();
        if (_calculateContrastRatio(adjustedColor, backgroundColor) >= minimumRatio) {
          return adjustedColor;
        }
      }
      return Colors.black;
    } else {
      // إذا كانت الخلفية داكنة، نجعل النص أفتح
      for (double lightness = textHsl.lightness; lightness <= 1.0; lightness += 0.05) {
        final adjustedColor = textHsl.withLightness(lightness).toColor();
        if (_calculateContrastRatio(adjustedColor, backgroundColor) >= minimumRatio) {
          return adjustedColor;
        }
      }
      return Colors.white;
    }
  }

  /// إنشاء ظل ذكي متناسق مع اللون
  static List<BoxShadow> createIntelligentShadow(
    Color baseColor, {
    ShadowIntensity intensity = ShadowIntensity.medium,
    bool isDarkMode = false,
  }) {
    final shadowColor = isDarkMode 
        ? Colors.black.withValues(alpha: 0.3)
        : baseColor.withValues(alpha: 0.15);
    
    switch (intensity) {
      case ShadowIntensity.subtle:
        return [
          BoxShadow(
            color: shadowColor,
            blurRadius: 4,
            offset: const Offset(0, 1),
            spreadRadius: 0,
          ),
        ];
      case ShadowIntensity.medium:
        return [
          BoxShadow(
            color: shadowColor,
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ];
      case ShadowIntensity.strong:
        return [
          BoxShadow(
            color: shadowColor,
            blurRadius: 16,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ];
      case ShadowIntensity.dramatic:
        return [
          BoxShadow(
            color: shadowColor,
            blurRadius: 24,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: shadowColor.withValues(alpha: shadowColor.alpha * 0.5),
            blurRadius: 8,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ];
    }
  }

  /// تحليل اللون وإرجاع معلومات مفصلة
  static ColorAnalysis analyzeColor(Color color) {
    final hsl = HSLColor.fromColor(color);
    final luminance = color.computeLuminance();
    
    return ColorAnalysis(
      color: color,
      hue: hsl.hue,
      saturation: hsl.saturation,
      lightness: hsl.lightness,
      luminance: luminance,
      isLight: luminance > 0.5,
      isDark: luminance <= 0.5,
      temperature: _getColorTemperature(hsl.hue),
      mood: _getColorMood(hsl),
      accessibility: _getAccessibilityLevel(luminance),
    );
  }

  // ========== دوال مساعدة خاصة ==========

  static HSLColor _adjustLightness(HSLColor hsl, double adjustment) {
    return hsl.withLightness((hsl.lightness + adjustment).clamp(0.0, 1.0));
  }

  static HSLColor _adjustSaturation(HSLColor hsl, double adjustment) {
    return hsl.withSaturation((hsl.saturation + adjustment).clamp(0.0, 1.0));
  }

  static HSLColor _generateSecondaryColor(HSLColor baseHsl) {
    // إنشاء لون ثانوي بتحريك الهيو 30 درجة
    return HSLColor.fromAHSL(
      1.0,
      (baseHsl.hue + 30) % 360,
      (baseHsl.saturation * 0.7).clamp(0.0, 1.0),
      baseHsl.lightness,
    );
  }

  static HSLColor _generateAccentColor(HSLColor baseHsl) {
    // إنشاء لون مميز بتحريك الهيو 120 درجة
    return HSLColor.fromAHSL(
      1.0,
      (baseHsl.hue + 120) % 360,
      baseHsl.saturation,
      baseHsl.lightness,
    );
  }

  static HSLColor _generateComplementaryColor(HSLColor baseHsl) {
    // إنشاء لون مكمل بتحريك الهيو 180 درجة
    return HSLColor.fromAHSL(
      1.0,
      (baseHsl.hue + 180) % 360,
      baseHsl.saturation,
      baseHsl.lightness,
    );
  }

  static HSLColor _generateSurfaceColor(HSLColor baseHsl, bool isDark) {
    if (isDark) {
      return HSLColor.fromAHSL(1.0, baseHsl.hue, 0.1, 0.08);
    } else {
      return HSLColor.fromAHSL(1.0, baseHsl.hue, 0.05, 0.98);
    }
  }

  static HSLColor _generateOnSurfaceColor(HSLColor baseHsl, bool isDark) {
    if (isDark) {
      return HSLColor.fromAHSL(1.0, baseHsl.hue, 0.05, 0.95);
    } else {
      return HSLColor.fromAHSL(1.0, baseHsl.hue, 0.1, 0.1);
    }
  }

  static double _calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = color1.computeLuminance();
    final luminance2 = color2.computeLuminance();
    final lighter = math.max(luminance1, luminance2);
    final darker = math.min(luminance1, luminance2);
    return (lighter + 0.05) / (darker + 0.05);
  }

  static _GradientAlignment _getGradientAlignment(GradientDirection direction) {
    switch (direction) {
      case GradientDirection.topToBottom:
        return _GradientAlignment(Alignment.topCenter, Alignment.bottomCenter);
      case GradientDirection.leftToRight:
        return _GradientAlignment(Alignment.centerLeft, Alignment.centerRight);
      case GradientDirection.topLeftToBottomRight:
        return _GradientAlignment(Alignment.topLeft, Alignment.bottomRight);
      case GradientDirection.topRightToBottomLeft:
        return _GradientAlignment(Alignment.topRight, Alignment.bottomLeft);
    }
  }

  static ColorTemperature _getColorTemperature(double hue) {
    if (hue >= 0 && hue < 60 || hue >= 300) return ColorTemperature.warm;
    if (hue >= 60 && hue < 180) return ColorTemperature.cool;
    if (hue >= 180 && hue < 300) return ColorTemperature.neutral;
    return ColorTemperature.neutral;
  }

  static ColorMood _getColorMood(HSLColor hsl) {
    if (hsl.saturation > 0.7 && hsl.lightness > 0.6) return ColorMood.energetic;
    if (hsl.saturation < 0.3) return ColorMood.calm;
    if (hsl.lightness < 0.3) return ColorMood.serious;
    if (hsl.saturation > 0.8) return ColorMood.vibrant;
    return ColorMood.balanced;
  }

  static AccessibilityLevel _getAccessibilityLevel(double luminance) {
    if (luminance > 0.7 || luminance < 0.2) return AccessibilityLevel.AAA;
    if (luminance > 0.5 || luminance < 0.3) return AccessibilityLevel.AA;
    return AccessibilityLevel.A;
  }
}

// ========== التعدادات والكلاسات المساعدة ==========

enum GradientDirection {
  topToBottom,
  leftToRight,
  topLeftToBottomRight,
  topRightToBottomLeft,
}

enum GradientStyle {
  subtle,
  vibrant,
  dramatic,
  complementary,
}

enum ShadowIntensity {
  subtle,
  medium,
  strong,
  dramatic,
}

enum ColorTemperature {
  warm,
  cool,
  neutral,
}

enum ColorMood {
  energetic,
  calm,
  serious,
  vibrant,
  balanced,
}

enum AccessibilityLevel {
  A,
  AA,
  AAA,
}

class _GradientAlignment {
  final AlignmentGeometry begin;
  final AlignmentGeometry end;
  
  const _GradientAlignment(this.begin, this.end);
}

class ColorAnalysis {
  final Color color;
  final double hue;
  final double saturation;
  final double lightness;
  final double luminance;
  final bool isLight;
  final bool isDark;
  final ColorTemperature temperature;
  final ColorMood mood;
  final AccessibilityLevel accessibility;

  const ColorAnalysis({
    required this.color,
    required this.hue,
    required this.saturation,
    required this.lightness,
    required this.luminance,
    required this.isLight,
    required this.isDark,
    required this.temperature,
    required this.mood,
    required this.accessibility,
  });

  @override
  String toString() {
    return 'ColorAnalysis(hue: ${hue.toStringAsFixed(1)}°, '
           'saturation: ${(saturation * 100).toStringAsFixed(1)}%, '
           'lightness: ${(lightness * 100).toStringAsFixed(1)}%, '
           'temperature: $temperature, mood: $mood, accessibility: $accessibility)';
  }
}
