@TestOn('linux')
library;

import 'package:file_picker/src/file_picker.dart';
import 'package:file_picker/src/linux/qarma_and_zenity_handler.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  final imageTestFile = '/tmp/test_linux.jpg';
  final pdfTestFile = '/tmp/test_linux.pdf';
  final yamlTestFile = '/tmp/test_linux.yml';

  group('fileTypeToFileFilter()', () {
    test('should return the file filter string for predefined file types', () {
      final dialogHandler = QarmaAndZenityHandler();

      expect(
        dialogHandler.fileTypeToFileFilter(FileType.any, null),
        equals(''),
      );

      expect(
        dialogHandler.fileTypeToFileFilter(FileType.audio, null),
        equals(
            'Audio Files | *.[aA][aA][cC] *.[mM][iI][dD][iI] *.[mM][pP]3 *.[oO][gG][gG] *.[wW][aA][vV] *.[mM]4[aA]'),
      );

      expect(
        dialogHandler.fileTypeToFileFilter(FileType.image, null),
        equals(
            'Image Files | *.[bB][mM][pP] *.[gG][iI][fF] *.[jJ][pP][eE][gG] *.[jJ][pP][gG] *.[pP][nN][gG]'),
      );

      expect(
        dialogHandler.fileTypeToFileFilter(FileType.media, null),
        equals(
          'Media Files | *.[aA][vV][iI] *.[fF][lL][vV] *.[mM][kK][vV] *.[mM][oO][vV] *.[mM][pP]4 *.[mM][pP][eE][gG] *.[wW][eE][bB][mM] *.[wW][mM][vV] *.[bB][mM][pP] *.[gG][iI][fF] *.[jJ][pP][eE][gG] *.[jJ][pP][gG] *.[pP][nN][gG]',
        ),
      );

      expect(
        dialogHandler.fileTypeToFileFilter(FileType.video, null),
        equals(
            'Video Files | *.[aA][vV][iI] *.[fF][lL][vV] *.[mM][kK][vV] *.[mM][oO][vV] *.[mM][pP]4 *.[mM][pP][eE][gG] *.[wW][eE][bB][mM] *.[wW][mM][vV]'),
      );
    });

    test('should return the file filter string for custom file extensions', () {
      final dialogHandler = QarmaAndZenityHandler();

      expect(
        dialogHandler.fileTypeToFileFilter(FileType.custom, ['dart']),
        equals('Custom Files | *.[dD][aA][rR][tT]'),
      );

      expect(
        dialogHandler.fileTypeToFileFilter(FileType.custom, ['dart', 'html']),
        equals('Custom Files | *.[dD][aA][rR][tT] *.[hH][tT][mM][lL]'),
      );
    });
  });

  group('resultStringToFilePaths()', () {
    test('should interpret the result of picking a single file', () {
      final filePaths = QarmaAndZenityHandler().resultStringToFilePaths(
        imageTestFile,
      );

      expect(filePaths.length, equals(1));
      expect(filePaths[0], imageTestFile);
    });

    test('should return an empty list if the file picker result was empty', () {
      final filePaths = QarmaAndZenityHandler().resultStringToFilePaths('');

      expect(filePaths.length, equals(0));
    });

    test('should interpret the result of picking multiple files', () {
      final filePaths = QarmaAndZenityHandler().resultStringToFilePaths(
        '$imageTestFile|$pdfTestFile|$yamlTestFile',
      );

      expect(filePaths.length, equals(3));
      expect(filePaths[0], equals(imageTestFile));
      expect(filePaths[1], equals(pdfTestFile));
      expect(filePaths[2], equals(yamlTestFile));
    });

    test(
        'should interpret the result of file names that contain vertical pipes',
        () {
      final filePaths = QarmaAndZenityHandler().resultStringToFilePaths(
        '$imageTestFile|/home/<USER>/file-with-|-in-name.txt|/tmp/image.png',
      );

      expect(filePaths.length, equals(3));
      expect(filePaths[0], equals(imageTestFile));
      expect(filePaths[1], equals('/home/<USER>/file-with-|-in-name.txt'));
      expect(filePaths[2], equals('/tmp/image.png'));
    });

    test('should interpret the result of picking a directory', () {
      final filePaths = QarmaAndZenityHandler().resultStringToFilePaths(
        '/home/<USER>/studies',
      );

      expect(filePaths.length, equals(1));
      expect(filePaths[0], equals('/home/<USER>/studies'));
    });
  });

  group('generateCommandLineArguments()', () {
    test('should generate the arguments for picking a single file', () {
      final cliArguments = QarmaAndZenityHandler().generateCommandLineArguments(
        'Select a file:',
        multipleFiles: false,
        pickDirectory: false,
      );

      expect(
        cliArguments.join(' '),
        equals("""--file-selection --title Select a file:"""),
      );
    });

    test('should generate the arguments for the save-file dialog', () {
      final cliArguments = QarmaAndZenityHandler().generateCommandLineArguments(
        'Select output file:',
        multipleFiles: false,
        pickDirectory: false,
        saveFile: true,
        fileName: 'test.out',
      );

      expect(
        cliArguments.join(' '),
        equals(
            """--file-selection --title Select output file: --save --confirm-overwrite --filename=test.out"""),
      );
    });

    test('should generate the arguments for picking multiple files', () {
      final cliArguments = QarmaAndZenityHandler().generateCommandLineArguments(
        'Select files:',
        multipleFiles: true,
        pickDirectory: false,
      );

      expect(
        cliArguments.join(' '),
        equals("""--file-selection --title Select files: --multiple"""),
      );
    });

    test(
        'should generate the arguments for picking a single file with a custom file filter',
        () {
      final cliArguments = QarmaAndZenityHandler().generateCommandLineArguments(
        'Select a file:',
        fileFilter: '*.dart *.yml',
        multipleFiles: false,
        pickDirectory: false,
      );

      expect(
        cliArguments.join(' '),
        equals(
          """--file-selection --title Select a file: --file-filter=*.dart *.yml""",
        ),
      );
    });

    test(
        'should generate the arguments for picking multiple files with a custom file filter',
        () {
      final cliArguments = QarmaAndZenityHandler().generateCommandLineArguments(
        'Select HTML files:',
        fileFilter: '*.html',
        multipleFiles: true,
        pickDirectory: false,
      );

      expect(
        cliArguments.join(' '),
        equals(
            """--file-selection --title Select HTML files: --file-filter=*.html --multiple"""),
      );
    });

    test('should generate the arguments for picking a directory', () {
      final cliArguments = QarmaAndZenityHandler().generateCommandLineArguments(
        'Select a directory:',
        pickDirectory: true,
      );

      expect(
        cliArguments.join(' '),
        equals("""--file-selection --title Select a directory: --directory"""),
      );
    });

    test(
        'should generate the arguments for picking a file when an initial directory is given',
        () {
      final cliArguments = QarmaAndZenityHandler().generateCommandLineArguments(
        'Select a file:',
        initialDirectory: '/home/<USER>/Desktop/',
      );

      expect(
        cliArguments.join(' '),
        equals(
            """--file-selection --title Select a file: --filename=/home/<USER>/Desktop/"""),
      );
    });

    test(
        'should generate the arguments for saving a file when an initial directory is given',
        () {
      final cliArguments = QarmaAndZenityHandler().generateCommandLineArguments(
        'Save as:',
        initialDirectory: '/home/<USER>/Desktop/',
        saveFile: true,
      );

      expect(
        cliArguments.join(' '),
        equals(
            """--file-selection --title Save as: --save --confirm-overwrite --filename=/home/<USER>/Desktop/"""),
      );
    });

    test(
        'should generate the arguments for saving a file when an initial directory and the filename is given',
        () {
      final cliArguments = QarmaAndZenityHandler().generateCommandLineArguments(
        'Save as:',
        fileName: 'output.pdf',
        initialDirectory: '/home/<USER>/Desktop/',
        saveFile: true,
      );

      expect(
        cliArguments.join(' '),
        equals(
            """--file-selection --title Save as: --save --confirm-overwrite --filename=/home/<USER>/Desktop/output.pdf"""),
      );
    });
  });
}
