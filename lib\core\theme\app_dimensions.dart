import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../utils/index.dart';
import '../theme/index.dart';

/// نظام الأبعاد والمقاسات الموحد لتطبيق تاجر بلس
/// يحتوي على جميع الأبعاد والمقاسات المستخدمة في التطبيق
class AppDimensions {
  // منع إنشاء كائن من هذا الكلاس
  AppDimensions._();

  // ========== الهوامش القياسية ==========

  /// هامش صغير جداً
  static double get tinyMargin => Layout.w(1);

  /// هامش صغير
  static double get smallMargin => Layout.w(2);

  /// هامش افتراضي
  static double get defaultMargin => Layout.w(4);

  /// هامش متوسط
  static double get mediumMargin => Layout.w(6);

  /// هامش كبير
  static double get largeMargin => Layout.w(8);

  /// هامش كبير جداً
  static double get extraLargeMargin => Layout.w(12);

  // ========== المساحات بين العناصر ==========

  /// مسافة صغيرة جداً
  static double get tinySpacing => Layout.h(1);

  /// مسافة صغيرة
  static double get smallSpacing => Layout.h(2);

  /// مسافة افتراضية
  static double get defaultSpacing => Layout.h(4);

  /// مسافة متوسطة
  static double get mediumSpacing => Layout.h(6);

  /// مسافة كبيرة
  static double get largeSpacing => Layout.h(8);

  /// مسافة كبيرة جداً
  static double get extraLargeSpacing => Layout.h(12);

  // ========== أحجام الأيقونات ==========

  /// حجم أيقونة صغير جداً
  static double get tinyIconSize => Layout.getResponsiveIconSize(12);

  /// حجم أيقونة صغير
  static double get smallIconSize => Layout.getResponsiveIconSize(16);

  /// حجم أيقونة افتراضي
  static double get defaultIconSize => Layout.getResponsiveIconSize(24);

  /// حجم أيقونة متوسط
  static double get mediumIconSize => Layout.getResponsiveIconSize(32);

  /// حجم أيقونة كبير
  static double get largeIconSize => Layout.getResponsiveIconSize(48);

  /// حجم أيقونة كبير جداً
  static double get extraLargeIconSize => Layout.getResponsiveIconSize(64);

  // ========== أحجام الخطوط ==========

  /// حجم خط صغير جداً
  static double get tinyFontSize => Layout.getResponsiveFontSize(10);

  /// حجم خط صغير
  static double get smallFontSize => Layout.getResponsiveFontSize(12);

  /// حجم خط افتراضي
  static double get defaultFontSize => Layout.getResponsiveFontSize(14);

  /// حجم خط متوسط
  static double get mediumFontSize => Layout.getResponsiveFontSize(16);

  /// حجم خط كبير
  static double get largeFontSize => Layout.getResponsiveFontSize(18);

  /// حجم خط عنوان
  static double get titleFontSize => Layout.getResponsiveFontSize(20);

  /// حجم خط عنوان رئيسي
  static double get headingFontSize => Layout.getResponsiveFontSize(24);

  /// حجم خط كبير جداً
  static double get extraLargeFontSize => Layout.getResponsiveFontSize(28);

  // ========== نصف قطر الزوايا ==========

  /// نصف قطر صغير جداً
  static double get tinyRadius => 2.0;

  /// نصف قطر صغير
  static double get smallRadius => 4.0;

  /// نصف قطر افتراضي
  static double get defaultRadius => 8.0;

  /// نصف قطر متوسط
  static double get mediumRadius => 12.0;

  /// نصف قطر كبير
  static double get largeRadius => 16.0;

  /// نصف قطر كبير جداً
  static double get extraLargeRadius => 24.0;

  /// نصف قطر دائري كامل
  static double get circularRadius => 50.0;

  // ========== أحجام الودجات ==========

  /// حجم ودجت صغير جداً
  static double get tinyWidgetSize => Layout.w(15);

  /// حجم ودجت صغير
  static double get smallWidgetSize => Layout.w(20);

  /// حجم ودجت افتراضي
  static double get defaultWidgetSize => Layout.w(30);

  /// حجم ودجت متوسط
  static double get mediumWidgetSize => Layout.w(40);

  /// حجم ودجت كبير
  static double get largeWidgetSize => Layout.w(50);

  /// حجم ودجت كبير جداً
  static double get extraLargeWidgetSize => Layout.w(60);

  // ========== أحجام البطاقات ==========

  /// عرض البطاقة الافتراضي
  static double get cardWidth => Layout.w(80);

  /// ارتفاع بطاقة صغير
  static double get smallCardHeight => Layout.h(12);

  /// ارتفاع بطاقة افتراضي
  static double get defaultCardHeight => Layout.h(16);

  /// ارتفاع بطاقة متوسط
  static double get mediumCardHeight => Layout.h(20);

  /// ارتفاع بطاقة كبير
  static double get largeCardHeight => Layout.h(25);

  /// ارتفاع بطاقة كبير جداً
  static double get extraLargeCardHeight => Layout.h(30);

  // ========== أحجام الأزرار ==========

  /// ارتفاع الزر الصغير
  static double get smallButtonHeight => Layout.h(4);

  /// ارتفاع الزر الافتراضي
  static double get buttonHeight => Layout.h(6);

  /// ارتفاع الزر الكبير
  static double get largeButtonHeight => Layout.h(8);

  /// عرض زر صغير
  static double get smallButtonWidth => Layout.w(25);

  /// عرض زر افتراضي
  static double get defaultButtonWidth => Layout.w(40);

  /// عرض زر كبير
  static double get largeButtonWidth => Layout.w(60);

  /// عرض زر كامل
  static double get fullButtonWidth => double.infinity;

  // ========== أحجام الصور ==========

  /// حجم صورة صغير جداً
  static double get tinyImageSize => Layout.w(15);

  /// حجم صورة صغير
  static double get smallImageSize => Layout.w(20);

  /// حجم صورة افتراضي
  static double get defaultImageSize => Layout.w(30);

  /// حجم صورة متوسط
  static double get mediumImageSize => Layout.w(40);

  /// حجم صورة كبير
  static double get largeImageSize => Layout.w(50);

  /// حجم صورة الملف الشخصي
  static double get profileImageSize => Layout.w(15);

  /// حجم صورة الشعار
  static double get logoImageSize => Layout.w(25);

  // ========== أحجام الودجات في الشاشة الرئيسية ==========

  /// حجم أيقونة الوصول السريع
  static double get quickAccessIconSize => Layout.getResponsiveIconSize(24);

  /// حجم عنصر الوصول السريع
  static double get quickAccessItemSize => Layout.w(20);

  /// ارتفاع عنصر الوصول السريع
  static double get quickAccessItemHeight => Layout.h(11);

  /// مسافة شبكة الوصول السريع
  static double get quickAccessGridSpacing => Layout.w(4);

  /// هامش شبكة الوصول السريع
  static double get quickAccessGridPadding => Layout.w(2);

  // ========== دوال مساعدة ==========

  /// الحصول على عدد الأعمدة المناسب بناءً على حجم الشاشة
  static int getGridColumnCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 600) return 2; // الهواتف
    if (width < 900) return 3; // الأجهزة اللوحية الصغيرة
    if (width < 1200) return 4; // الأجهزة اللوحية الكبيرة
    return 5; // الشاشات الكبيرة
  }

  /// الحصول على نسبة العرض إلى الارتفاع المناسبة للشبكة
  static double getGridAspectRatio(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 600) return 1.2;
    return 1.5;
  }

  /// الحصول على حجم الشاشة
  static Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }

  /// الحصول على عرض الشاشة
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// الحصول على ارتفاع الشاشة
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// الحصول على الهوامش المتجاوبة
  static EdgeInsets getResponsivePadding({
    double horizontal = 4,
    double vertical = 2,
  }) {
    return EdgeInsets.symmetric(
      horizontal: Layout.w(horizontal),
      vertical: Layout.h(vertical),
    );
  }

  /// الحصول على حجم الودجات المتجاوب
  static double getResponsiveWidgetSize(double percentage) {
    return Layout.w(percentage);
  }

  /// الحصول على هوامش متناسقة للبطاقات
  static EdgeInsets get cardPadding => EdgeInsets.all(defaultMargin);

  /// الحصول على هوامش متناسقة للشاشات
  static EdgeInsets get screenPadding => EdgeInsets.all(defaultMargin);

  /// الحصول على هوامش متناسقة للحوارات
  static EdgeInsets get dialogPadding => EdgeInsets.all(mediumMargin);

  // ========== دوال التخطيط الآمن ==========

  /// معامل أمان للنصوص - يضمن عدم تجاوز النصوص للمساحة المتاحة
  static const double textSafetyFactor = 0.9;

  /// الحد الأدنى للارتفاع الآمن للصفوف
  static const double minRowHeight = 48.0;

  /// الحد الأدنى للعرض الآمن للأزرار
  static const double minButtonWidth = 64.0;

  /// الحد الأدنى للارتفاع الآمن للأزرار
  static const double minButtonHeight = 36.0;

  // ========== نظام التصميم الموحد الشامل ==========

  /// مسافات موحدة للتطبيق - نظام 4px grid
  static const double spacing2 = 2.0;
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing48 = 48.0;
  static const double spacing64 = 64.0;

  /// هوامش موحدة للبطاقات والمكونات
  static const EdgeInsets cardPaddingTiny = EdgeInsets.all(8.0);
  static const EdgeInsets cardPaddingSmall = EdgeInsets.all(12.0);
  static const EdgeInsets cardPaddingMedium = EdgeInsets.all(16.0);
  static const EdgeInsets cardPaddingLarge = EdgeInsets.all(20.0);
  static const EdgeInsets cardPaddingXLarge = EdgeInsets.all(24.0);

  /// هوامش موحدة للشاشات
  static const EdgeInsets screenPaddingSmall = EdgeInsets.all(12.0);
  static const EdgeInsets screenPaddingMedium = EdgeInsets.all(16.0);
  static const EdgeInsets screenPaddingLarge = EdgeInsets.all(20.0);
  static const EdgeInsets screenPaddingXLarge = EdgeInsets.all(24.0);

  /// هوامش موحدة للأقسام
  static const EdgeInsets sectionPadding = EdgeInsets.symmetric(
    horizontal: 16.0,
    vertical: 12.0,
  );
  static const EdgeInsets sectionPaddingLarge = EdgeInsets.symmetric(
    horizontal: 20.0,
    vertical: 16.0,
  );

  /// هوامش موحدة للعناصر
  static const EdgeInsets itemPadding = EdgeInsets.symmetric(
    horizontal: 12.0,
    vertical: 8.0,
  );
  static const EdgeInsets itemPaddingLarge = EdgeInsets.symmetric(
    horizontal: 16.0,
    vertical: 12.0,
  );

  /// هوامش موحدة للأزرار
  static const EdgeInsets buttonPaddingSmall = EdgeInsets.symmetric(
    horizontal: 12.0,
    vertical: 6.0,
  );
  static const EdgeInsets buttonPaddingMedium = EdgeInsets.symmetric(
    horizontal: 16.0,
    vertical: 8.0,
  );
  static const EdgeInsets buttonPaddingLarge = EdgeInsets.symmetric(
    horizontal: 20.0,
    vertical: 12.0,
  );

  /// أحجام موحدة للبطاقات
  static const double cardHeightTiny = 60.0;
  static const double cardHeightSmall = 80.0;
  static const double cardHeightMedium = 120.0;
  static const double cardHeightLarge = 160.0;
  static const double cardHeightXLarge = 200.0;
  static const double cardHeightXXLarge = 240.0;

  /// عروض موحدة للبطاقات
  static const double cardWidthSmall = 150.0;
  static const double cardWidthMedium = 200.0;
  static const double cardWidthLarge = 250.0;
  static const double cardWidthXLarge = 300.0;

  /// أحجام موحدة للأيقونات في السياقات المختلفة
  static const double iconSizeTiny = 12.0;
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;
  static const double iconSizeXXLarge = 64.0;

  /// أحجام موحدة للأزرار
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightMedium = 44.0;
  static const double buttonHeightLarge = 52.0;
  static const double buttonHeightXLarge = 60.0;

  /// عروض موحدة للأزرار
  static const double buttonWidthSmall = 80.0;
  static const double buttonWidthMedium = 120.0;
  static const double buttonWidthLarge = 160.0;
  static const double buttonWidthXLarge = 200.0;

  /// نصف أقطار موحدة للعناصر المختلفة
  static const double radiusTiny = 4.0;
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 20.0;
  static const double radiusXXLarge = 24.0;
  static const double radiusCircular = 50.0;

  /// ارتفاعات موحدة للظلال
  static const double elevationNone = 0.0;
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationXHigh = 16.0;
  static const double elevationXXHigh = 24.0;

  // ========== دوال مساعدة للتصميم الموحد ==========

  /// الحصول على مسافة موحدة بناءً على الحجم
  static double getSpacing(String size) {
    switch (size.toLowerCase()) {
      case 'tiny':
        return spacing2;
      case 'small':
        return spacing8;
      case 'medium':
        return spacing16;
      case 'large':
        return spacing24;
      case 'xlarge':
        return spacing32;
      default:
        return spacing16;
    }
  }

  /// الحصول على هوامش موحدة للبطاقات
  static EdgeInsets getCardPadding(String size) {
    switch (size.toLowerCase()) {
      case 'tiny':
        return cardPaddingTiny;
      case 'small':
        return cardPaddingSmall;
      case 'medium':
        return cardPaddingMedium;
      case 'large':
        return cardPaddingLarge;
      case 'xlarge':
        return cardPaddingXLarge;
      default:
        return cardPaddingMedium;
    }
  }

  /// الحصول على نصف قطر موحد
  static double getRadius(String size) {
    switch (size.toLowerCase()) {
      case 'tiny':
        return radiusTiny;
      case 'small':
        return radiusSmall;
      case 'medium':
        return radiusMedium;
      case 'large':
        return radiusLarge;
      case 'xlarge':
        return radiusXLarge;
      case 'xxlarge':
        return radiusXXLarge;
      case 'circular':
        return radiusCircular;
      default:
        return radiusMedium;
    }
  }

  /// الحصول على ارتفاع ظل موحد
  static double getElevation(String level) {
    switch (level.toLowerCase()) {
      case 'none':
        return elevationNone;
      case 'low':
        return elevationLow;
      case 'medium':
        return elevationMedium;
      case 'high':
        return elevationHigh;
      case 'xhigh':
        return elevationXHigh;
      case 'xxhigh':
        return elevationXXHigh;
      default:
        return elevationMedium;
    }
  }

  /// الحصول على حجم أيقونة موحد
  static double getIconSize(String size) {
    switch (size.toLowerCase()) {
      case 'tiny':
        return iconSizeTiny;
      case 'small':
        return iconSizeSmall;
      case 'medium':
        return iconSizeMedium;
      case 'large':
        return iconSizeLarge;
      case 'xlarge':
        return iconSizeXLarge;
      case 'xxlarge':
        return iconSizeXXLarge;
      default:
        return iconSizeMedium;
    }
  }

  /// الحصول على ارتفاع زر موحد
  static double getButtonHeight(String size) {
    switch (size.toLowerCase()) {
      case 'small':
        return buttonHeightSmall;
      case 'medium':
        return buttonHeightMedium;
      case 'large':
        return buttonHeightLarge;
      case 'xlarge':
        return buttonHeightXLarge;
      default:
        return buttonHeightMedium;
    }
  }

  /// الحصول على عرض زر موحد
  static double getButtonWidth(String size) {
    switch (size.toLowerCase()) {
      case 'small':
        return buttonWidthSmall;
      case 'medium':
        return buttonWidthMedium;
      case 'large':
        return buttonWidthLarge;
      case 'xlarge':
        return buttonWidthXLarge;
      default:
        return buttonWidthMedium;
    }
  }

  /// الحصول على ارتفاع بطاقة موحد
  static double getCardHeight(String size) {
    switch (size.toLowerCase()) {
      case 'tiny':
        return cardHeightTiny;
      case 'small':
        return cardHeightSmall;
      case 'medium':
        return cardHeightMedium;
      case 'large':
        return cardHeightLarge;
      case 'xlarge':
        return cardHeightXLarge;
      case 'xxlarge':
        return cardHeightXXLarge;
      default:
        return cardHeightMedium;
    }
  }

  /// الحصول على عرض بطاقة موحد
  static double getCardWidth(String size) {
    switch (size.toLowerCase()) {
      case 'small':
        return cardWidthSmall;
      case 'medium':
        return cardWidthMedium;
      case 'large':
        return cardWidthLarge;
      case 'xlarge':
        return cardWidthXLarge;
      default:
        return cardWidthMedium;
    }
  }

  /// الحصول على هوامش شاشة موحدة
  static EdgeInsets getScreenPadding(String size) {
    switch (size.toLowerCase()) {
      case 'small':
        return screenPaddingSmall;
      case 'medium':
        return screenPaddingMedium;
      case 'large':
        return screenPaddingLarge;
      case 'xlarge':
        return screenPaddingXLarge;
      default:
        return screenPaddingMedium;
    }
  }

  /// الحصول على هوامش أزرار موحدة
  static EdgeInsets getButtonPadding(String size) {
    switch (size.toLowerCase()) {
      case 'small':
        return buttonPaddingSmall;
      case 'medium':
        return buttonPaddingMedium;
      case 'large':
        return buttonPaddingLarge;
      default:
        return buttonPaddingMedium;
    }
  }

  /// الحصول على عرض آمن للنص بناءً على عدد الأحرف
  static double getSafeTextWidth(
      String text, AppTypography style, BuildContext context) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: 1,
      textDirection: TextDirection.rtl,
    )..layout(maxWidth: double.infinity);

    return textPainter.width * textSafetyFactor;
  }

  /// الحصول على ارتفاع آمن للنص بناءً على عدد الأسطر
  static double getSafeTextHeight(String text, AppTypography style,
      double maxWidth, int maxLines, BuildContext context) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      maxLines: maxLines,
      textDirection: TextDirection.rtl,
    )..layout(maxWidth: maxWidth);

    return textPainter.height * 1.1; // إضافة هامش أمان بنسبة 10%
  }

  /// الحصول على حجم خط آمن بناءً على عرض المساحة المتاحة
  static double getSafeFontSize(double fontSize, double availableWidth,
      String text, BuildContext context) {
    final baseStyle = AppTypography(fontSize: fontSize);
    final textWidth = getSafeTextWidth(text, baseStyle, context);

    if (textWidth > availableWidth) {
      return fontSize * (availableWidth / textWidth) * textSafetyFactor;
    }

    return fontSize;
  }

  /// الحصول على أبعاد آمنة للزر تضمن سهولة النقر والمظهر المناسب
  static Size getSafeButtonSize(
      String label, AppTypography? style, BuildContext context) {
    final textWidth =
        getSafeTextWidth(label, style ?? const AppTypography(), context);
    final width = math.max(textWidth + 32, minButtonWidth);
    final height = math.max(minButtonHeight, Layout.h(6));

    return Size(width, height);
  }
}
