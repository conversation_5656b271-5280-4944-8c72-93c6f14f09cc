import 'package:flutter/material.dart';

/// 🎨 نظام الألوان الموحد والمبسط لتطبيق تاجر بلس
/// 
/// هذا الملف يحتوي على جميع الألوان المستخدمة في التطبيق
/// مع تعليقات واضحة بالعربية لكل لون ومتى يُستخدم
/// 
/// 📋 الهيكل:
/// 1. الألوان الأساسية (الثيمات الـ 8)
/// 2. ألوان النظام (نجاح، تحذير، خطأ)
/// 3. ألوان الوضع الفاتح
/// 4. ألوان الوضع الداكن
/// 5. ألوان خاصة بالتطبيق
class AppColors {
  AppColors._(); // منع إنشاء كائن

  // ========== 🎨 الثيمات الأساسية (8 ثيمات) ==========
  
  /// 🔴 الثيم الأحمر - اللون الافتراضي لتاجر بلس
  /// 📍 يُستخدم في: الأزرار الرئيسية، شريط التطبيق، العناصر المهمة
  static const Color redPrimary = Color(0xFFE53E3E);
  static const Color redSecondary = Color(0xFFFEB2B2);
  
  /// 🔵 الثيم الأزرق - للمظهر المهني
  /// 📍 يُستخدم في: الشاشات المالية، التقارير، البيانات
  static const Color bluePrimary = Color(0xFF3182CE);
  static const Color blueSecondary = Color(0xFFBEE3F8);
  
  /// 🟢 الثيم الأخضر - للعمليات الناجحة
  /// 📍 يُستخدم في: المبيعات، الأرباح، العمليات المكتملة
  static const Color greenPrimary = Color(0xFF38A169);
  static const Color greenSecondary = Color(0xFFC6F6D5);
  
  /// 🟣 الثيم البنفسجي - للإبداع والتميز
  /// 📍 يُستخدم في: الميزات الخاصة، العروض، الإبداع
  static const Color purplePrimary = Color(0xFF805AD5);
  static const Color purpleSecondary = Color(0xFFE9D8FD);
  
  /// 🟠 الثيم البرتقالي - للطاقة والحيوية
  /// 📍 يُستخدم في: التنبيهات، الأنشطة، الطاقة
  static const Color orangePrimary = Color(0xFFDD6B20);
  static const Color orangeSecondary = Color(0xFFFBD38D);
  
  /// 🟡 الثيم الأصفر - للتحذيرات والانتباه
  /// 📍 يُستخدم في: التحذيرات، الملاحظات، الانتباه
  static const Color yellowPrimary = Color(0xFFD69E2E);
  static const Color yellowSecondary = Color(0xFFFAF089);
  
  /// 🩷 الثيم الوردي - للأناقة والجمال
  /// 📍 يُستخدم في: التصميم الأنيق، العروض الخاصة
  static const Color pinkPrimary = Color(0xFFD53F8C);
  static const Color pinkSecondary = Color(0xFFFBB6CE);
  
  /// 🩵 الثيم الفيروزي - للهدوء والاستقرار
  /// 📍 يُستخدم في: الإعدادات، الاستقرار، الهدوء
  static const Color tealPrimary = Color(0xFF319795);
  static const Color tealSecondary = Color(0xFFB2F5EA);

  // ========== 🚦 ألوان النظام ==========
  
  /// ✅ لون النجاح - للعمليات المكتملة بنجاح
  /// 📍 يُستخدم في: رسائل النجاح، العمليات المكتملة، الحالة الإيجابية
  static const Color success = Color(0xFF38A169);
  
  /// ⚠️ لون التحذير - للتنبيهات والملاحظات المهمة
  /// 📍 يُستخدم في: رسائل التحذير، الملاحظات، الحالة المتوسطة
  static const Color warning = Color(0xFFD69E2E);
  
  /// ❌ لون الخطأ - للأخطاء والعمليات الفاشلة
  /// 📍 يُستخدم في: رسائل الخطأ، العمليات الفاشلة، الحالة السلبية
  static const Color error = Color(0xFFE53E3E);
  
  /// ℹ️ لون المعلومات - للمعلومات العامة والتلميحات
  /// 📍 يُستخدم في: رسائل المعلومات، التلميحات، الحالة المحايدة
  static const Color info = Color(0xFF3182CE);

  // ========== 🌞 ألوان الوضع الفاتح ==========
  
  /// 📱 خلفية الشاشة الرئيسية - أبيض مكسور مريح للعين
  static const Color lightBackground = Color(0xFFFAFAFA);
  
  /// 🃏 خلفية البطاقات والأسطح - أبيض نقي
  static const Color lightSurface = Color(0xFFFFFFFF);
  
  /// 📝 النص الأساسي - أسود نقي للوضوح الكامل
  static const Color lightTextPrimary = Color(0xFF000000);
  
  /// 🔤 النص الثانوي - رمادي داكن للنصوص الفرعية
  static const Color lightTextSecondary = Color(0xFF424242);
  
  /// 💬 النص المساعد - رمادي متوسط للتلميحات
  static const Color lightTextHint = Color(0xFF757575);
  
  /// 🔲 الحدود والفواصل - رمادي فاتح للحدود
  static const Color lightBorder = Color(0xFFE0E0E0);
  
  /// 🌫️ الظلال - رمادي شفاف للظلال
  static const Color lightShadow = Color(0x1F000000);

  // ========== 🌙 ألوان الوضع الداكن (مريحة مثل VS Code) ==========
  
  /// 📱 خلفية الشاشة الرئيسية - كحلي داكن مريح للاستخدام الليلي
  static const Color darkBackground = Color(0xFF1E1E1E);
  
  /// 🃏 خلفية البطاقات والأسطح - رمادي داكن مريح
  static const Color darkSurface = Color(0xFF252526);
  
  /// 📝 النص الأساسي - أبيض مكسور مريح للعين
  static const Color darkTextPrimary = Color(0xFFCCCCCC);
  
  /// 🔤 النص الثانوي - أزرق فاتح مريح للتسميات
  static const Color darkTextSecondary = Color(0xFF9CDCFE);
  
  /// 💬 النص المساعد - رمادي متوسط للتلميحات
  static const Color darkTextHint = Color(0xFF808080);
  
  /// 🔲 الحدود والفواصل - رمادي داكن للحدود
  static const Color darkBorder = Color(0xFF3E3E42);
  
  /// 🌫️ الظلال - أسود شفاف للظلال
  static const Color darkShadow = Color(0x40000000);

  // ========== 💼 ألوان خاصة بالتطبيق ==========
  
  /// 💰 لون الأرباح - أخضر للمبالغ الإيجابية
  static const Color profit = success;
  
  /// 📉 لون الخسائر - أحمر للمبالغ السلبية
  static const Color loss = error;
  
  /// ⚖️ لون المحايد - رمادي للمبالغ المحايدة
  static const Color neutral = Color(0xFF757575);

  // ========== 🎨 الثيمات المتاحة ==========
  
  /// خريطة الثيمات المتاحة للمستخدم
  static const Map<String, ThemeColors> availableThemes = {
    'red': ThemeColors(
      name: 'أحمر تاجر بلس',
      primary: redPrimary,
      secondary: redSecondary,
    ),
    'blue': ThemeColors(
      name: 'أزرق مهني',
      primary: bluePrimary,
      secondary: blueSecondary,
    ),
    'green': ThemeColors(
      name: 'أخضر طبيعي',
      primary: greenPrimary,
      secondary: greenSecondary,
    ),
    'purple': ThemeColors(
      name: 'بنفسجي إبداعي',
      primary: purplePrimary,
      secondary: purpleSecondary,
    ),
    'orange': ThemeColors(
      name: 'برتقالي حيوي',
      primary: orangePrimary,
      secondary: orangeSecondary,
    ),
    'yellow': ThemeColors(
      name: 'أصفر تحذيري',
      primary: yellowPrimary,
      secondary: yellowSecondary,
    ),
    'pink': ThemeColors(
      name: 'وردي أنيق',
      primary: pinkPrimary,
      secondary: pinkSecondary,
    ),
    'teal': ThemeColors(
      name: 'فيروزي هادئ',
      primary: tealPrimary,
      secondary: tealSecondary,
    ),
  };

  // ========== 🔧 دوال مساعدة ==========
  
  /// الحصول على ألوان الثيم حسب المفتاح
  static ThemeColors getTheme(String key) {
    return availableThemes[key] ?? availableThemes['red']!;
  }
  
  /// الحصول على لون النص المناسب للخلفية
  static Color getTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? lightTextPrimary : darkTextPrimary;
  }
  
  /// فحص ما إذا كان اللون داكن
  static bool isDark(Color color) {
    return color.computeLuminance() < 0.5;
  }
}

/// 🎨 فئة ألوان الثيم
class ThemeColors {
  final String name;
  final Color primary;
  final Color secondary;
  
  const ThemeColors({
    required this.name,
    required this.primary,
    required this.secondary,
  });
}
