import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'color_harmony_system.dart';
import 'advanced_color_system.dart';
import 'app_colors.dart';
import 'app_dimensions.dart';
import 'app_typography.dart';

/// النظام الموحد للثيمات - يضمن التوافق المثالي بين جميع العناصر
/// 🎨 تطبيق التوافق اللوني على كل مكون
/// 🔄 تحديث تلقائي لجميع الطبقات
/// ♿ ضمان إمكانية الوصول في كل عنصر
/// 🎭 انتقالات سلسة ومتناسقة
class UnifiedThemeSystem {
  UnifiedThemeSystem._();

  /// إنشاء ثيم موحد ومتوافق بالكامل
  static ThemeData createUnifiedTheme({
    required Color primaryColor,
    required Brightness brightness,
    String? fontFamily,
  }) {
    final isDarkMode = brightness == Brightness.dark;

    // إنشاء نظام الطبقات المتوافقة
    final colorLayers = ColorHarmonySystem.createHarmoniousLayers(
      primaryColor: primaryColor,
      isDarkMode: isDarkMode,
    );

    // إنشاء لوحات الألوان المتخصصة
    final textColors = ColorHarmonySystem.createTextColorPalette(
      backgroundColor: colorLayers.backgroundLayer.primary,
      primaryColor: primaryColor,
      isDarkMode: isDarkMode,
    );

    final buttonColors = ColorHarmonySystem.createButtonColorPalette(
      primaryColor: primaryColor,
      backgroundColor: colorLayers.backgroundLayer.primary,
      isDarkMode: isDarkMode,
    );

    final borderColors = ColorHarmonySystem.createBorderColorPalette(
      backgroundColor: colorLayers.backgroundLayer.primary,
      primaryColor: primaryColor,
      isDarkMode: isDarkMode,
    );

    final shadowColors = ColorHarmonySystem.createShadowColorPalette(
      backgroundColor: colorLayers.backgroundLayer.primary,
      primaryColor: primaryColor,
      isDarkMode: isDarkMode,
    );

    return ThemeData(
      useMaterial3: true,
      brightness: brightness,

      // نظام الألوان الموحد
      colorScheme:
          _createUnifiedColorScheme(colorLayers, textColors, isDarkMode),

      // الخلفيات والأسطح
      scaffoldBackgroundColor: colorLayers.backgroundLayer.primary,
      canvasColor: colorLayers.backgroundLayer.secondary,
      cardColor: colorLayers.surfaceLayer.primary,

      // الألوان الأساسية
      primaryColor: primaryColor,
      primaryColorLight: colorLayers.interactionLayer.secondary,
      primaryColorDark: colorLayers.interactionLayer.tertiary,

      // الطباعة الموحدة
      textTheme: _createUnifiedTextTheme(textColors, fontFamily, isDarkMode),
      primaryTextTheme:
          _createUnifiedTextTheme(textColors, fontFamily, isDarkMode),

      // شريط التطبيق الموحد
      appBarTheme: _createUnifiedAppBarTheme(
          colorLayers, textColors, shadowColors, isDarkMode),

      // البطاقات الموحدة
      cardTheme:
          _createUnifiedCardTheme(colorLayers, shadowColors, borderColors),

      // الأزرار الموحدة
      elevatedButtonTheme:
          _createUnifiedElevatedButtonTheme(buttonColors, shadowColors),
      outlinedButtonTheme:
          _createUnifiedOutlinedButtonTheme(buttonColors, borderColors),
      textButtonTheme: _createUnifiedTextButtonTheme(buttonColors),

      // حقول الإدخال الموحدة
      inputDecorationTheme: _createUnifiedInputTheme(
          colorLayers, textColors, borderColors, primaryColor),

      // شريط التنقل السفلي الموحد
      bottomNavigationBarTheme:
          _createUnifiedBottomNavTheme(colorLayers, textColors, primaryColor),

      // التبويبات الموحدة
      tabBarTheme:
          _createUnifiedTabBarTheme(colorLayers, textColors, primaryColor),

      // القوائم الموحدة
      listTileTheme: _createUnifiedListTileTheme(colorLayers, textColors),

      // أشرطة التقدم الموحدة
      progressIndicatorTheme: _createUnifiedProgressTheme(primaryColor),

      // المفاتيح الموحدة
      switchTheme: _createUnifiedSwitchTheme(primaryColor, colorLayers),
      checkboxTheme: _createUnifiedCheckboxTheme(primaryColor),
      radioTheme: _createUnifiedRadioTheme(primaryColor),

      // الحوارات الموحدة
      dialogTheme:
          _createUnifiedDialogTheme(colorLayers, shadowColors, borderColors),

      // القوائم المنبثقة الموحدة
      popupMenuTheme:
          _createUnifiedPopupMenuTheme(colorLayers, shadowColors, borderColors),

      // أشرطة التمرير الموحدة
      scrollbarTheme: _createUnifiedScrollbarTheme(primaryColor, isDarkMode),

      // الفواصل الموحدة
      dividerColor: borderColors.subtle,
      dividerTheme: DividerThemeData(
        color: borderColors.subtle,
        thickness: 1,
        space: 1,
      ),

      // الظلال الموحدة
      shadowColor: shadowColors.normal,

      // إعدادات إضافية
      visualDensity: VisualDensity.adaptivePlatformDensity,
      materialTapTargetSize: MaterialTapTargetSize.padded,
      pageTransitionsTheme: _createUnifiedPageTransitions(),

      // ألوان التمييز
      highlightColor: primaryColor.withValues(alpha: 0.12),
      splashColor: primaryColor.withValues(alpha: 0.16),
      hoverColor: primaryColor.withValues(alpha: 0.08),
      focusColor: primaryColor.withValues(alpha: 0.12),

      // إعدادات النظام
      platform: TargetPlatform.android,
    );
  }

  // ========== إنشاء المكونات الموحدة ==========

  static ColorScheme _createUnifiedColorScheme(
    ColorLayerSystem colorLayers,
    TextColorPalette textColors,
    bool isDarkMode,
  ) {
    if (isDarkMode) {
      return ColorScheme.dark(
        primary: colorLayers.interactionLayer.primary,
        primaryContainer: colorLayers.interactionLayer.secondary,
        secondary: colorLayers.emphasisLayer.primary,
        secondaryContainer: colorLayers.emphasisLayer.secondary,
        tertiary: colorLayers.emphasisLayer.tertiary,
        surface: colorLayers.surfaceLayer.primary,
        surfaceContainerHighest: colorLayers.surfaceLayer.secondary,
        background: colorLayers.backgroundLayer.primary,
        error: textColors.error,
        onPrimary: textColors.primary,
        onPrimaryContainer: textColors.primary,
        onSecondary: textColors.primary,
        onSecondaryContainer: textColors.primary,
        onTertiary: textColors.primary,
        onSurface: textColors.primary,
        onBackground: textColors.primary,
        onError: Colors.white,
        outline: colorLayers.contentLayer.tertiary,
        shadow: Colors.black.withValues(alpha: 0.3),
      );
    } else {
      return ColorScheme.light(
        primary: colorLayers.interactionLayer.primary,
        primaryContainer: colorLayers.interactionLayer.secondary,
        secondary: colorLayers.emphasisLayer.primary,
        secondaryContainer: colorLayers.emphasisLayer.secondary,
        tertiary: colorLayers.emphasisLayer.tertiary,
        surface: colorLayers.surfaceLayer.primary,
        surfaceContainerHighest: colorLayers.surfaceLayer.secondary,
        background: colorLayers.backgroundLayer.primary,
        error: textColors.error,
        onPrimary: Colors.white,
        onPrimaryContainer: textColors.primary,
        onSecondary: Colors.white,
        onSecondaryContainer: textColors.primary,
        onTertiary: Colors.white,
        onSurface: textColors.primary,
        onBackground: textColors.primary,
        onError: Colors.white,
        outline: colorLayers.contentLayer.tertiary,
        shadow: colorLayers.interactionLayer.primary.withValues(alpha: 0.1),
      );
    }
  }

  static TextTheme _createUnifiedTextTheme(
    TextColorPalette textColors,
    String? fontFamily,
    bool isDarkMode,
  ) {
    final baseTheme =
        isDarkMode ? AppTypography.darkTextTheme : AppTypography.lightTextTheme;

    return baseTheme.copyWith(
      displayLarge: baseTheme.displayLarge?.copyWith(
        color: textColors.primary,
        fontFamily: fontFamily,
      ),
      displayMedium: baseTheme.displayMedium?.copyWith(
        color: textColors.primary,
        fontFamily: fontFamily,
      ),
      displaySmall: baseTheme.displaySmall?.copyWith(
        color: textColors.primary,
        fontFamily: fontFamily,
      ),
      headlineLarge: baseTheme.headlineLarge?.copyWith(
        color: textColors.primary,
        fontFamily: fontFamily,
      ),
      headlineMedium: baseTheme.headlineMedium?.copyWith(
        color: textColors.primary,
        fontFamily: fontFamily,
      ),
      headlineSmall: baseTheme.headlineSmall?.copyWith(
        color: textColors.primary,
        fontFamily: fontFamily,
      ),
      titleLarge: baseTheme.titleLarge?.copyWith(
        color: textColors.primary,
        fontFamily: fontFamily,
      ),
      titleMedium: baseTheme.titleMedium?.copyWith(
        color: textColors.primary,
        fontFamily: fontFamily,
      ),
      titleSmall: baseTheme.titleSmall?.copyWith(
        color: textColors.secondary,
        fontFamily: fontFamily,
      ),
      bodyLarge: baseTheme.bodyLarge?.copyWith(
        color: textColors.primary,
        fontFamily: fontFamily,
      ),
      bodyMedium: baseTheme.bodyMedium?.copyWith(
        color: textColors.secondary,
        fontFamily: fontFamily,
      ),
      bodySmall: baseTheme.bodySmall?.copyWith(
        color: textColors.tertiary,
        fontFamily: fontFamily,
      ),
      labelLarge: baseTheme.labelLarge?.copyWith(
        color: textColors.primary,
        fontFamily: fontFamily,
      ),
      labelMedium: baseTheme.labelMedium?.copyWith(
        color: textColors.secondary,
        fontFamily: fontFamily,
      ),
      labelSmall: baseTheme.labelSmall?.copyWith(
        color: textColors.tertiary,
        fontFamily: fontFamily,
      ),
    );
  }

  static AppBarTheme _createUnifiedAppBarTheme(
    ColorLayerSystem colorLayers,
    TextColorPalette textColors,
    ShadowColorPalette shadowColors,
    bool isDarkMode,
  ) {
    return AppBarTheme(
      backgroundColor: colorLayers.surfaceLayer.primary,
      foregroundColor: textColors.primary,
      elevation: 0,
      shadowColor: shadowColors.subtle,
      surfaceTintColor: Colors.transparent,
      centerTitle: true,
      titleTextStyle: AppTypography.lightTextTheme.titleLarge?.copyWith(
        color: textColors.primary,
        fontWeight: FontWeight.bold,
      ),
      iconTheme: IconThemeData(
        color: textColors.primary,
        size: 24,
      ),
      actionsIconTheme: IconThemeData(
        color: textColors.primary,
        size: 24,
      ),
      systemOverlayStyle:
          isDarkMode ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark,
    );
  }

  static CardTheme _createUnifiedCardTheme(
    ColorLayerSystem colorLayers,
    ShadowColorPalette shadowColors,
    BorderColorPalette borderColors,
  ) {
    return CardTheme(
      color: colorLayers.surfaceLayer.primary,
      shadowColor: shadowColors.normal,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        side: BorderSide(
          color: borderColors.subtle,
          width: 0.5,
        ),
      ),
      margin: const EdgeInsets.all(8),
      clipBehavior: Clip.antiAlias,
    );
  }

  static ElevatedButtonThemeData _createUnifiedElevatedButtonTheme(
    ButtonColorPalette buttonColors,
    ShadowColorPalette shadowColors,
  ) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: buttonColors.primary.background,
        foregroundColor: buttonColors.primary.foreground,
        disabledBackgroundColor: buttonColors.primary.disabled,
        disabledForegroundColor:
            buttonColors.primary.foreground.withValues(alpha: 0.38),
        shadowColor: shadowColors.colored,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        minimumSize: const Size(64, 40),
        textStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ).copyWith(
        overlayColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.hovered)) {
            return buttonColors.primary.hover;
          }
          if (states.contains(MaterialState.pressed)) {
            return buttonColors.primary.pressed;
          }
          return null;
        }),
      ),
    );
  }

  static OutlinedButtonThemeData _createUnifiedOutlinedButtonTheme(
    ButtonColorPalette buttonColors,
    BorderColorPalette borderColors,
  ) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: buttonColors.secondary.foreground,
        disabledForegroundColor: buttonColors.secondary.disabled,
        side: BorderSide(
          color: buttonColors.secondary.border,
          width: 1.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        minimumSize: const Size(64, 40),
        textStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ).copyWith(
        overlayColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.hovered)) {
            return buttonColors.secondary.hover;
          }
          if (states.contains(MaterialState.pressed)) {
            return buttonColors.secondary.pressed;
          }
          return null;
        }),
        side: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.disabled)) {
            return BorderSide(
              color: buttonColors.secondary.disabled,
              width: 1.5,
            );
          }
          return BorderSide(
            color: buttonColors.secondary.border,
            width: 1.5,
          );
        }),
      ),
    );
  }

  static TextButtonThemeData _createUnifiedTextButtonTheme(
      ButtonColorPalette buttonColors) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: buttonColors.text.foreground,
        disabledForegroundColor: buttonColors.text.disabled,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        minimumSize: const Size(64, 36),
        textStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ).copyWith(
        overlayColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.hovered)) {
            return buttonColors.text.hover;
          }
          if (states.contains(MaterialState.pressed)) {
            return buttonColors.text.pressed;
          }
          return null;
        }),
      ),
    );
  }

  // ========== دوال مساعدة إضافية ==========

  static PageTransitionsTheme _createUnifiedPageTransitions() {
    return const PageTransitionsTheme(
      builders: {
        TargetPlatform.android: CupertinoPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        TargetPlatform.macOS: CupertinoPageTransitionsBuilder(),
        TargetPlatform.windows: FadeUpwardsPageTransitionsBuilder(),
        TargetPlatform.linux: FadeUpwardsPageTransitionsBuilder(),
      },
    );
  }

  static InputDecorationTheme _createUnifiedInputTheme(
    ColorLayerSystem colorLayers,
    TextColorPalette textColors,
    BorderColorPalette borderColors,
    Color primaryColor,
  ) {
    return InputDecorationTheme(
      filled: true,
      fillColor: colorLayers.surfaceLayer.secondary,
      labelStyle: TextStyle(color: textColors.secondary),
      hintStyle: TextStyle(color: textColors.tertiary),
      helperStyle: TextStyle(color: textColors.tertiary),
      errorStyle: TextStyle(color: textColors.error),
      prefixIconColor: textColors.secondary,
      suffixIconColor: textColors.secondary,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        borderSide: BorderSide(color: borderColors.normal),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        borderSide: BorderSide(color: borderColors.normal),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        borderSide: BorderSide(color: borderColors.focus, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        borderSide: BorderSide(color: borderColors.error, width: 2),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        borderSide: BorderSide(color: borderColors.error, width: 2),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        borderSide: BorderSide(color: borderColors.subtle),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      isDense: false,
      floatingLabelBehavior: FloatingLabelBehavior.auto,
    );
  }

  static BottomNavigationBarThemeData _createUnifiedBottomNavTheme(
    ColorLayerSystem colorLayers,
    TextColorPalette textColors,
    Color primaryColor,
  ) {
    return BottomNavigationBarThemeData(
      backgroundColor: colorLayers.surfaceLayer.primary,
      selectedItemColor: primaryColor,
      unselectedItemColor: textColors.tertiary,
      selectedIconTheme: IconThemeData(
        color: primaryColor,
        size: 24,
      ),
      unselectedIconTheme: IconThemeData(
        color: textColors.tertiary,
        size: 24,
      ),
      selectedLabelStyle: AppTypography.lightTextTheme.bodySmall?.copyWith(
        color: primaryColor,
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: AppTypography.lightTextTheme.bodySmall?.copyWith(
        color: textColors.tertiary,
      ),
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      enableFeedback: true,
    );
  }

  static TabBarTheme _createUnifiedTabBarTheme(
    ColorLayerSystem colorLayers,
    TextColorPalette textColors,
    Color primaryColor,
  ) {
    return TabBarTheme(
      labelColor: primaryColor,
      unselectedLabelColor: textColors.tertiary,
      labelStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: AppTypography.lightTextTheme.labelLarge,
      indicator: UnderlineTabIndicator(
        borderSide: BorderSide(
          color: primaryColor,
          width: 3,
        ),
        insets: const EdgeInsets.symmetric(horizontal: 16),
      ),
      indicatorSize: TabBarIndicatorSize.tab,
      dividerColor: Colors.transparent,
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.hovered)) {
          return primaryColor.withValues(alpha: 0.08);
        }
        if (states.contains(MaterialState.pressed)) {
          return primaryColor.withValues(alpha: 0.12);
        }
        return null;
      }),
    );
  }

  static ListTileThemeData _createUnifiedListTileTheme(
    ColorLayerSystem colorLayers,
    TextColorPalette textColors,
  ) {
    return ListTileThemeData(
      tileColor: Colors.transparent,
      selectedTileColor:
          colorLayers.interactionLayer.primary.withValues(alpha: 0.12),
      iconColor: textColors.secondary,
      selectedColor: colorLayers.interactionLayer.primary,
      textColor: textColors.primary,
      titleTextStyle: AppTypography.lightTextTheme.bodyLarge?.copyWith(
        color: textColors.primary,
      ),
      subtitleTextStyle: AppTypography.lightTextTheme.bodyMedium?.copyWith(
        color: textColors.secondary,
      ),
      leadingAndTrailingTextStyle:
          AppTypography.lightTextTheme.bodyMedium?.copyWith(
        color: textColors.secondary,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      horizontalTitleGap: 16,
      minVerticalPadding: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
      ),
    );
  }

  static ProgressIndicatorThemeData _createUnifiedProgressTheme(
      Color primaryColor) {
    return ProgressIndicatorThemeData(
      color: primaryColor,
      linearTrackColor: primaryColor.withValues(alpha: 0.2),
      circularTrackColor: primaryColor.withValues(alpha: 0.2),
      refreshBackgroundColor: primaryColor.withValues(alpha: 0.1),
    );
  }

  static SwitchThemeData _createUnifiedSwitchTheme(
    Color primaryColor,
    ColorLayerSystem colorLayers,
  ) {
    return SwitchThemeData(
      thumbColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return primaryColor;
        }
        return colorLayers.contentLayer.tertiary;
      }),
      trackColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return primaryColor.withValues(alpha: 0.5);
        }
        return colorLayers.contentLayer.tertiary.withValues(alpha: 0.3);
      }),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.pressed)) {
          return primaryColor.withValues(alpha: 0.12);
        }
        return null;
      }),
    );
  }

  static CheckboxThemeData _createUnifiedCheckboxTheme(Color primaryColor) {
    return CheckboxThemeData(
      fillColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return primaryColor;
        }
        return Colors.transparent;
      }),
      checkColor: MaterialStateProperty.all(Colors.white),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.pressed)) {
          return primaryColor.withValues(alpha: 0.12);
        }
        return null;
      }),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }

  static RadioThemeData _createUnifiedRadioTheme(Color primaryColor) {
    return RadioThemeData(
      fillColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.selected)) {
          return primaryColor;
        }
        return Colors.transparent;
      }),
      overlayColor: MaterialStateProperty.resolveWith((states) {
        if (states.contains(MaterialState.pressed)) {
          return primaryColor.withValues(alpha: 0.12);
        }
        return null;
      }),
    );
  }

  static DialogTheme _createUnifiedDialogTheme(
    ColorLayerSystem colorLayers,
    ShadowColorPalette shadowColors,
    BorderColorPalette borderColors,
  ) {
    return DialogTheme(
      backgroundColor: colorLayers.surfaceLayer.primary,
      shadowColor: shadowColors.elevated,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius * 1.5),
        side: BorderSide(
          color: borderColors.subtle,
          width: 0.5,
        ),
      ),
      alignment: Alignment.center,
      insetPadding: const EdgeInsets.all(24),
      clipBehavior: Clip.antiAlias,
    );
  }

  static PopupMenuThemeData _createUnifiedPopupMenuTheme(
    ColorLayerSystem colorLayers,
    ShadowColorPalette shadowColors,
    BorderColorPalette borderColors,
  ) {
    return PopupMenuThemeData(
      color: colorLayers.surfaceLayer.primary,
      shadowColor: shadowColors.elevated,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        side: BorderSide(
          color: borderColors.subtle,
          width: 0.5,
        ),
      ),
      position: PopupMenuPosition.under,
    );
  }

  static ScrollbarThemeData _createUnifiedScrollbarTheme(
      Color primaryColor, bool isDarkMode) {
    return ScrollbarThemeData(
      thumbColor: MaterialStateProperty.all(
        primaryColor.withValues(alpha: isDarkMode ? 0.6 : 0.4),
      ),
      trackColor: MaterialStateProperty.all(
        primaryColor.withValues(alpha: isDarkMode ? 0.2 : 0.1),
      ),
      radius: const Radius.circular(4),
      thickness: MaterialStateProperty.all(6),
      minThumbLength: 48,
      interactive: true,
      crossAxisMargin: 4,
      mainAxisMargin: 4,
    );
  }
}
