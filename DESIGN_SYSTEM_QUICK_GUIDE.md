# 🚀 دليل النظام الموحد السريع - تطبيق تاجر بلس

## 📖 **دليل الاستخدام السريع**

### 🎯 **الهدف**
استخدام نظام تصميم موحد لضمان الاتساق والأداء الأمثل.

---

## 🏗️ **الاستيراد**

```dart
import '../../../core/theme/index.dart';
```

---

## 📐 **المسافات والهوامش**

### **المسافات بين العناصر**
```dart
// استخدم هذه بدلاً من الأرقام المباشرة
SizedBox(height: AppDimensions.spacing8)   // 8px
SizedBox(height: AppDimensions.spacing16)  // 16px
SizedBox(height: AppDimensions.spacing24)  // 24px
SizedBox(height: AppDimensions.spacing32)  // 32px

// أو استخدم الدالة المساعدة
SizedBox(height: AppDimensions.getSpacing('small'))   // 8px
SizedBox(height: AppDimensions.getSpacing('medium'))  // 16px
SizedBox(height: AppDimensions.getSpacing('large'))   // 24px
```

### **هوامش الشاشات**
```dart
// للشاشات
Padding(
  padding: AppDimensions.screenPaddingMedium, // 16px من جميع الجهات
  child: content,
)

// أو استخدم الدالة
Padding(
  padding: AppDimensions.getScreenPadding('medium'),
  child: content,
)
```

---

## 🎨 **البطاقات**

### **AdaptiveCard - البطاقات الذكية**
```dart
// بسيط وسريع
AdaptiveCard.small(
  child: Text('محتوى صغير'),
)

AdaptiveCard.medium(
  child: Text('محتوى متوسط'),
)

AdaptiveCard.large(
  child: Text('محتوى كبير'),
)

// مع تخصيص
AdaptiveCard.medium(
  onTap: () => print('تم النقر'),
  child: Column(
    children: [
      Text('عنوان'),
      Text('محتوى'),
    ],
  ),
)
```

### **CustomCard - البطاقات التقليدية**
```dart
// بسيط
CustomCard.medium(
  child: Text('محتوى'),
)

// مع تخصيص
CustomCard.large(
  backgroundColor: AppColors.lightSurface,
  onTap: () => print('تم النقر'),
  child: Text('محتوى'),
)
```

---

## 📊 **بطاقات الإحصائيات**

### **AdaptiveStatsCard**
```dart
// بطاقة إحصائيات صغيرة
AdaptiveStatsCard.small(
  title: 'المبيعات',
  value: '1,250 ر.س',
  subtitle: 'اليوم',
  icon: Icons.shopping_cart,
  iconColor: AppColors.info,
)

// بطاقة إحصائيات متوسطة مع اتجاه
AdaptiveStatsCard.medium(
  title: 'الأرباح',
  value: '850 ر.س',
  subtitle: 'هذا الشهر',
  icon: Icons.trending_up,
  iconColor: AppColors.success,
  showTrend: true,
  trendValue: 12.5, // موجب = صاعد، سالب = هابط
  onTap: () => Navigator.pushNamed(context, '/profits'),
)

// بطاقة إحصائيات كبيرة
AdaptiveStatsCard.large(
  title: 'إجمالي المبيعات',
  value: '25,480 ر.س',
  subtitle: 'هذا العام',
  icon: Icons.analytics,
  iconColor: AppColors.primary,
  showTrend: true,
  trendValue: 8.3,
)
```

---

## 🔘 **الأزرار**

### **AdaptiveButton**
```dart
// أزرار بأحجام مختلفة
AdaptiveButton.small(
  text: 'حفظ',
  onPressed: () => save(),
)

AdaptiveButton.medium(
  text: 'إرسال',
  icon: Icons.send,
  onPressed: () => send(),
)

AdaptiveButton.large(
  text: 'تأكيد الطلب',
  icon: Icons.check_circle,
  backgroundColor: AppColors.success,
  onPressed: () => confirm(),
)

// زر محدد (outlined)
AdaptiveButton.medium(
  text: 'إلغاء',
  isOutlined: true,
  onPressed: () => cancel(),
)
```

---

## 🎭 **الأيقونات**

### **أحجام الأيقونات**
```dart
Icon(
  Icons.home,
  size: AppDimensions.iconSizeSmall,   // 16px
)

Icon(
  Icons.settings,
  size: AppDimensions.iconSizeMedium,  // 24px
)

Icon(
  Icons.dashboard,
  size: AppDimensions.iconSizeLarge,   // 32px
)

// أو استخدم الدالة
Icon(
  Icons.star,
  size: AppDimensions.getIconSize('medium'), // 24px
)
```

---

## 🔄 **نصف الأقطار والظلال**

### **نصف الأقطار**
```dart
Container(
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),  // 8px
    // أو
    borderRadius: BorderRadius.circular(AppDimensions.getRadius('medium')), // 12px
  ),
)
```

### **الظلال**
```dart
Container(
  decoration: BoxDecoration(
    boxShadow: [
      BoxShadow(
        blurRadius: AppDimensions.elevationMedium,  // 4px
        // أو
        blurRadius: AppDimensions.getElevation('high'), // 8px
      ),
    ],
  ),
)
```

---

## 📱 **التخطيط المتجاوب**

### **الشاشات المختلفة**
```dart
// في الشاشات
Widget build(BuildContext context) {
  return Scaffold(
    body: Padding(
      padding: AppDimensions.screenPaddingMedium,
      child: Column(
        children: [
          // العنوان
          Text('العنوان'),
          SizedBox(height: AppDimensions.spacing16),
          
          // المحتوى
          AdaptiveCard.medium(
            child: Text('المحتوى'),
          ),
          SizedBox(height: AppDimensions.spacing24),
          
          // الأزرار
          Row(
            children: [
              Expanded(
                child: AdaptiveButton.medium(
                  text: 'حفظ',
                  onPressed: () => save(),
                ),
              ),
              SizedBox(width: AppDimensions.spacing16),
              Expanded(
                child: AdaptiveButton.medium(
                  text: 'إلغاء',
                  isOutlined: true,
                  onPressed: () => cancel(),
                ),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}
```

---

## ⚡ **نصائح سريعة**

### ✅ **افعل**
```dart
// استخدم النظام الموحد
SizedBox(height: AppDimensions.spacing16)
AdaptiveCard.medium(child: content)
AppDimensions.getSpacing('large')

// استخدم const عند الإمكان
const SizedBox(height: AppDimensions.spacing16)
```

### ❌ **لا تفعل**
```dart
// لا تستخدم أرقام مباشرة
SizedBox(height: 16)
Container(padding: EdgeInsets.all(12))

// لا تنشئ بطاقات معقدة يدوياً
Container(
  padding: EdgeInsets.all(16),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(12),
    boxShadow: [...],
  ),
  child: content,
)
```

---

## 🎨 **أمثلة شائعة**

### **قائمة بطاقات**
```dart
ListView.separated(
  itemCount: items.length,
  separatorBuilder: (context, index) => 
    SizedBox(height: AppDimensions.spacing12),
  itemBuilder: (context, index) => AdaptiveCard.medium(
    onTap: () => openItem(items[index]),
    child: ListTile(
      title: Text(items[index].title),
      subtitle: Text(items[index].subtitle),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: AppDimensions.iconSizeSmall,
      ),
    ),
  ),
)
```

### **شبكة إحصائيات**
```dart
GridView.count(
  crossAxisCount: 2,
  crossAxisSpacing: AppDimensions.spacing16,
  mainAxisSpacing: AppDimensions.spacing16,
  children: [
    AdaptiveStatsCard.medium(
      title: 'المبيعات',
      value: '1,250 ر.س',
      subtitle: 'اليوم',
      icon: Icons.shopping_cart,
      iconColor: AppColors.info,
    ),
    AdaptiveStatsCard.medium(
      title: 'الطلبات',
      value: '45',
      subtitle: 'جديد',
      icon: Icons.shopping_bag,
      iconColor: AppColors.warning,
    ),
    // المزيد...
  ],
)
```

### **نموذج بسيط**
```dart
Column(
  children: [
    AdaptiveCard.medium(
      child: Column(
        children: [
          TextField(decoration: InputDecoration(labelText: 'الاسم')),
          SizedBox(height: AppDimensions.spacing16),
          TextField(decoration: InputDecoration(labelText: 'البريد')),
        ],
      ),
    ),
    SizedBox(height: AppDimensions.spacing24),
    AdaptiveButton.large(
      text: 'حفظ',
      icon: Icons.save,
      onPressed: () => save(),
    ),
  ],
)
```

---

## 🏆 **الخلاصة**

- **استخدم** النظام الموحد دائماً
- **فضل** المُنشئات المبسطة (.small, .medium, .large)
- **استخدم** const عند الإمكان
- **تجنب** الأرقام المباشرة
- **استفد** من الدوال المساعدة

هذا النظام يضمن **اتساق التصميم** و **سهولة الصيانة** و **الأداء الأمثل**! 🚀
