# دليل تعليمي لمطوري تطبيق تاجر بلس

## مقدمة

هذا الدليل يشرح كيفية استخدام الكود وطريقة عمله، وكيفية تطوير أو بناء جداول أو شاشات جديدة في تطبيق تاجر بلس. سنقوم بشرح الهيكل العام للتطبيق، وكيفية إضافة جداول جديدة إلى قاعدة البيانات، وكيفية إنشاء شاشات جديدة.

## هيكل التطبيق

تطبيق تاجر بلس مبني باستخدام Flutter ويستخدم قاعدة بيانات SQLite المحلية. يتكون التطبيق من المجلدات الرئيسية التالية:

1. **lib/core**: يحتوي على المكونات الأساسية للتطبيق مثل:
   - **database**: إدارة قاعدة البيانات
   - **models**: نماذج البيانات
   - **services**: خدمات التطبيق
   - **utils**: أدوات مساعدة

2. **lib/features**: يحتوي على ميزات التطبيق المختلفة مثل:
   - **products**: إدارة المنتجات
   - **sales**: إدارة المبيعات
   - **purchases**: إدارة المشتريات
   - **accounts**: إدارة الحسابات
   - **expenses**: إدارة المصروفات

3. **lib/ui**: يحتوي على واجهة المستخدم مثل:
   - **screens**: شاشات التطبيق
   - **widgets**: عناصر واجهة المستخدم المشتركة

## إضافة جدول جديد إلى قاعدة البيانات

لإضافة جدول جديد إلى قاعدة البيانات، اتبع الخطوات التالية:

### الخطوة 1: إنشاء ملف Schema للجدول

أولاً، قم بإنشاء ملف Schema جديد في المجلد `lib/core/database/schema/` يحتوي على تعريف الجدول. على سبيل المثال، لإنشاء جدول للمخزون:

```dart
// lib/core/database/schema/inventory_schema.dart
import 'package:sqflite/sqflite.dart';
import '../../utils/app_logger.dart';

/// Schema for inventory-related tables
class InventorySchema {
  /// Create the inventory table
  static Future<void> createTable(Transaction txn) async {
    await txn.execute('''
      CREATE TABLE IF NOT EXISTS inventory (
        id TEXT PRIMARY KEY,
        product_id TEXT NOT NULL,
        warehouse_id TEXT NOT NULL,
        quantity REAL NOT NULL DEFAULT 0.0,
        min_quantity REAL DEFAULT 0.0,
        max_quantity REAL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        is_deleted INTEGER NOT NULL DEFAULT 0,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id) ON DELETE CASCADE,
        UNIQUE (product_id, warehouse_id)
      )
    ''');

    // Create indexes
    await txn.execute(
      'CREATE INDEX IF NOT EXISTS idx_inventory_product_id ON inventory (product_id)',
    );
    await txn.execute(
      'CREATE INDEX IF NOT EXISTS idx_inventory_warehouse_id ON inventory (warehouse_id)',
    );
    
    AppLogger.info('Created inventory table and indexes');
  }
}
```

### الخطوة 2: تحديث مدير الجداول

بعد إنشاء ملف Schema، قم بتحديث ملف `database_tables_manager.dart` لإضافة الجدول الجديد:

1. استيراد ملف Schema الجديد:
```dart
import 'schema/inventory_schema.dart';
```

2. إضافة الجدول إلى قائمة الجداول المطلوبة:
```dart
static const List<String> requiredTables = [
  // ...
  'inventory',
  // ...
];
```

3. إضافة دالة إنشاء الجدول إلى `_tableCreators`:
```dart
final Map<String, Future<void> Function(Transaction txn)> _tableCreators = {
  // ...
  'inventory': InventorySchema.createTable,
  // ...
};
```

### الخطوة 3: إضافة اسم الجدول إلى DatabaseService

قم بإضافة اسم الجدول إلى ملف `database_service.dart`:

```dart
// lib/core/database/database_service.dart
static const String tableInventory = 'inventory';
```

### الخطوة 4: إنشاء نموذج البيانات (Model)

قم بإنشاء نموذج بيانات للجدول في المجلد `lib/core/models/`:

```dart
// lib/core/models/inventory.dart
import 'package:uuid/uuid.dart';
import 'base_model.dart';

class Inventory extends BaseModel {
  final String productId;
  final String warehouseId;
  final double quantity;
  final double? minQuantity;
  final double? maxQuantity;

  Inventory({
    String? id,
    required this.productId,
    required this.warehouseId,
    required this.quantity,
    this.minQuantity,
    this.maxQuantity,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
    bool isDeleted = false,
  }) : super(
          id: id ?? const Uuid().v4(),
          createdAt: createdAt ?? DateTime.now(),
          createdBy: createdBy,
          updatedAt: updatedAt,
          updatedBy: updatedBy,
          isDeleted: isDeleted,
        );

  factory Inventory.fromMap(Map<String, dynamic> map) {
    return Inventory(
      id: map['id'],
      productId: map['product_id'],
      warehouseId: map['warehouse_id'],
      quantity: map['quantity'] is int
          ? (map['quantity'] as int).toDouble()
          : map['quantity'] ?? 0.0,
      minQuantity: map['min_quantity'] is int
          ? (map['min_quantity'] as int).toDouble()
          : map['min_quantity'],
      maxQuantity: map['max_quantity'] is int
          ? (map['max_quantity'] as int).toDouble()
          : map['max_quantity'],
      createdAt: map['created_at'] != null
          ? DateTime.parse(map['created_at'])
          : DateTime.now(),
      createdBy: map['created_by'],
      updatedAt:
          map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_id': productId,
      'warehouse_id': warehouseId,
      'quantity': quantity,
      'min_quantity': minQuantity,
      'max_quantity': maxQuantity,
      'created_at': createdAt.toIso8601String(),
      'created_by': createdBy,
      'updated_at': updatedAt?.toIso8601String(),
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }
}
```

### الخطوة 5: إنشاء خدمة (Service)

قم بإنشاء خدمة للتعامل مع الجدول في المجلد `lib/core/services/`:

```dart
// lib/core/services/inventory_service.dart
import '../database/database_service.dart';
import '../models/inventory.dart';
import '../utils/app_logger.dart';
import '../utils/error_tracker.dart';

/// خدمة للعمليات المتعلقة بالمخزون
class InventoryService {
  // نمط Singleton
  static final InventoryService _instance = InventoryService._internal();
  factory InventoryService() => _instance;
  InventoryService._internal();

  final DatabaseService _db = DatabaseService.instance;

  /// الحصول على مخزون منتج في مستودع معين
  Future<Inventory?> getInventory(String productId, String warehouseId) async {
    try {
      AppLogger.info('الحصول على مخزون المنتج: $productId في المستودع: $warehouseId');

      final List<Map<String, dynamic>> maps = await _db.query(
        DatabaseService.tableInventory,
        where: 'product_id = ? AND warehouse_id = ? AND is_deleted = 0',
        whereArgs: [productId, warehouseId],
      );

      if (maps.isEmpty) {
        return null;
      }

      return Inventory.fromMap(maps.first);
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في الحصول على مخزون المنتج',
        error: e,
        stackTrace: stackTrace,
        context: {'productId': productId, 'warehouseId': warehouseId},
      );
      return null;
    }
  }

  /// إضافة أو تحديث مخزون
  Future<bool> updateInventory(Inventory inventory, {String? userId}) async {
    try {
      AppLogger.info('تحديث مخزون المنتج: ${inventory.productId}');

      // التحقق من وجود المخزون
      final existingInventory = await getInventory(
        inventory.productId,
        inventory.warehouseId,
      );

      final inventoryMap = inventory.toMap();

      if (existingInventory == null) {
        // إضافة مخزون جديد
        if (userId != null) {
          inventoryMap['created_by'] = userId;
        }
        await _db.insert(DatabaseService.tableInventory, inventoryMap);
      } else {
        // تحديث المخزون الموجود
        if (userId != null) {
          inventoryMap['updated_by'] = userId;
        }
        inventoryMap['updated_at'] = DateTime.now().toIso8601String();
        await _db.update(
          DatabaseService.tableInventory,
          inventoryMap,
          where: 'id = ?',
          whereArgs: [existingInventory.id],
        );
      }

      return true;
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تحديث مخزون المنتج',
        error: e,
        stackTrace: stackTrace,
        context: {'inventory': inventory.toString()},
      );
      return false;
    }
  }
}
```
