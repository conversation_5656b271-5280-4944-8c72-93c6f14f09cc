import 'package:flutter/material.dart';
import 'advanced_color_system.dart';
import 'intelligent_theme_engine.dart';
import 'app_colors.dart';
import 'app_dimensions.dart';
import 'app_typography.dart';

/// نظام معاينة الثيمات الفورية
/// 🎨 معاينة فورية للثيمات قبل التطبيق
/// 🔄 انتقالات سلسة ومتحركة
/// 📱 عرض جميع مكونات الواجهة
/// ⚡ أداء محسن ومعاينة سريعة
class ThemePreviewSystem extends StatefulWidget {
  final List<String> availableThemes;
  final String currentTheme;
  final Function(String) onThemeChanged;
  final bool showAnimations;

  const ThemePreviewSystem({
    Key? key,
    required this.availableThemes,
    required this.currentTheme,
    required this.onThemeChanged,
    this.showAnimations = true,
  }) : super(key: key);

  @override
  State<ThemePreviewSystem> createState() => _ThemePreviewSystemState();
}

class _ThemePreviewSystemState extends State<ThemePreviewSystem>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  String? _previewTheme;
  bool _isPreviewMode = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startPreview(String themeKey) {
    setState(() {
      _previewTheme = themeKey;
      _isPreviewMode = true;
    });
    
    if (widget.showAnimations) {
      _animationController.forward();
    }
  }

  void _endPreview() {
    setState(() {
      _isPreviewMode = false;
      _previewTheme = null;
    });
    
    if (widget.showAnimations) {
      _animationController.reverse();
    }
  }

  void _applyTheme(String themeKey) {
    widget.onThemeChanged(themeKey);
    _endPreview();
  }

  @override
  Widget build(BuildContext context) {
    final currentThemeData = AppColors.availableThemes[widget.currentTheme]!;
    final previewThemeData = _previewTheme != null 
        ? AppColors.availableThemes[_previewTheme!]!
        : currentThemeData;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        const SizedBox(height: 16),
        _buildThemeGrid(),
        if (_isPreviewMode) ...[
          const SizedBox(height: 24),
          _buildPreviewSection(previewThemeData),
        ],
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.palette_outlined,
          color: Theme.of(context).colorScheme.primary,
          size: 28,
        ),
        const SizedBox(width: 12),
        Text(
          'اختر الثيم المفضل',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        if (_isPreviewMode)
          TextButton.icon(
            onPressed: _endPreview,
            icon: const Icon(Icons.close),
            label: const Text('إلغاء المعاينة'),
          ),
      ],
    );
  }

  Widget _buildThemeGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 0.8,
      ),
      itemCount: widget.availableThemes.length,
      itemBuilder: (context, index) {
        final themeKey = widget.availableThemes[index];
        final themeData = AppColors.availableThemes[themeKey]!;
        final isSelected = widget.currentTheme == themeKey;
        final isPreview = _previewTheme == themeKey;

        return _buildThemeCard(
          themeKey: themeKey,
          themeData: themeData,
          isSelected: isSelected,
          isPreview: isPreview,
        );
      },
    );
  }

  Widget _buildThemeCard({
    required String themeKey,
    required Map<String, dynamic> themeData,
    required bool isSelected,
    required bool isPreview,
  }) {
    final primaryColor = themeData['primary'] as Color;
    final name = themeData['name'] as String;

    return GestureDetector(
      onTap: () => _startPreview(themeKey),
      onLongPress: () => _applyTheme(themeKey),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          gradient: AdvancedColorSystem.createIntelligentGradient(
            primaryColor,
            style: GradientStyle.subtle,
          ),
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          border: Border.all(
            color: isSelected 
                ? Colors.white
                : isPreview 
                    ? Colors.white.withValues(alpha: 0.7)
                    : Colors.transparent,
            width: isSelected ? 3 : isPreview ? 2 : 0,
          ),
          boxShadow: AdvancedColorSystem.createIntelligentShadow(
            primaryColor,
            intensity: isSelected || isPreview 
                ? ShadowIntensity.strong 
                : ShadowIntensity.medium,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            AnimatedScale(
              scale: isSelected ? 1.2 : isPreview ? 1.1 : 1.0,
              duration: const Duration(milliseconds: 200),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isSelected 
                      ? Icons.check_circle
                      : isPreview
                          ? Icons.visibility
                          : Icons.circle_outlined,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Text(
                _getShortName(name),
                style: AppTypography.lightTextTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewSection(Map<String, dynamic> themeData) {
    final primaryColor = themeData['primary'] as Color;
    final name = themeData['name'] as String;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
                border: Border.all(
                  color: primaryColor.withValues(alpha: 0.3),
                  width: 2,
                ),
                boxShadow: AdvancedColorSystem.createIntelligentShadow(
                  primaryColor,
                  intensity: ShadowIntensity.medium,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPreviewHeader(name, primaryColor),
                  const SizedBox(height: 16),
                  _buildPreviewComponents(primaryColor),
                  const SizedBox(height: 16),
                  _buildPreviewActions(primaryColor),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPreviewHeader(String name, Color primaryColor) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.preview,
            color: primaryColor,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'معاينة الثيم',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                name,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: primaryColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewComponents(Color primaryColor) {
    return Row(
      children: [
        Expanded(
          child: _buildSampleCard(primaryColor, 'بطاقة عينة', Icons.card_giftcard),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSampleButton(primaryColor, 'زر عينة'),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSampleInput(primaryColor),
        ),
      ],
    );
  }

  Widget _buildSampleCard(Color primaryColor, String title, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: primaryColor, size: 20),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSampleButton(Color primaryColor, String text) {
    return ElevatedButton(
      onPressed: () {},
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 8),
      ),
      child: Text(
        text,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  Widget _buildSampleInput(Color primaryColor) {
    return TextField(
      enabled: false,
      decoration: InputDecoration(
        hintText: 'حقل إدخال',
        hintStyle: TextStyle(fontSize: 12),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: primaryColor),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      ),
    );
  }

  Widget _buildPreviewActions(Color primaryColor) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: _endPreview,
            icon: const Icon(Icons.close, size: 16),
            label: const Text('إلغاء'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _applyTheme(_previewTheme!),
            icon: const Icon(Icons.check, size: 16),
            label: const Text('تطبيق'),
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  String _getShortName(String fullName) {
    return fullName.split(' ').first;
  }
}
