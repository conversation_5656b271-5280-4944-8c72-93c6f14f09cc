import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_logger.dart';
import 'colors.dart';
import 'themes.dart';

/// 🎨 مدير الثيمات المبسط لتطبيق تاجر بلس
/// 
/// يتحكم في:
/// - تبديل الوضع الفاتح/الداكن/النظام
/// - اختيار الثيم اللوني من 8 ثيمات
/// - حفظ واسترجاع الإعدادات تلقائياً
class ThemeManager extends ChangeNotifier {
  static const String _themeKey = 'app_theme_mode';
  static const String _colorThemeKey = 'app_color_theme';

  ThemeMode _themeMode = ThemeMode.system;
  String _colorTheme = 'red';

  /// الحصول على وضع الثيم الحالي
  ThemeMode get themeMode => _themeMode;

  /// فحص ما إذا كان الوضع الداكن مفعل
  bool get isDarkMode => _themeMode == ThemeMode.dark;

  /// فحص ما إذا كان الوضع الفاتح مفعل
  bool get isLightMode => _themeMode == ThemeMode.light;

  /// فحص ما إذا كان يتبع ثيم النظام
  bool get isSystemMode => _themeMode == ThemeMode.system;

  /// الحصول على مفتاح الثيم اللوني الحالي
  String get colorTheme => _colorTheme;

  /// الحصول على اسم الثيم اللوني الحالي
  String get colorThemeName {
    final themeColors = AppColors.getTheme(_colorTheme);
    return themeColors.name;
  }

  /// الحصول على اللون الأساسي للثيم الحالي
  Color get currentPrimaryColor {
    final themeColors = AppColors.getTheme(_colorTheme);
    return themeColors.primary;
  }

  /// إنشاء ثيم فاتح للثيم الحالي
  ThemeData createLightTheme() {
    return AppThemes.createLightTheme(_colorTheme);
  }

  /// إنشاء ثيم داكن للثيم الحالي
  ThemeData createDarkTheme() {
    return AppThemes.createDarkTheme(_colorTheme);
  }

  /// الحصول على جميع الثيمات المتاحة
  List<String> get availableThemeKeys {
    return AppColors.availableThemes.keys.toList();
  }

  /// الحصول على معلومات الثيم الحالي
  Map<String, dynamic> getCurrentThemeInfo() {
    final themeColors = AppColors.getTheme(_colorTheme);

    return {
      'themeName': themeColors.name,
      'themeKey': _colorTheme,
      'primaryColor': '#${themeColors.primary.value.toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}',
      'isDarkMode': isDarkMode,
      'isLightMode': isLightMode,
      'isSystemMode': isSystemMode,
    };
  }

  // ========== 🔧 إدارة الثيمات ==========

  /// تهيئة مدير الثيم
  Future<void> initialize() async {
    try {
      AppLogger.info('🎨 بدء تهيئة مدير الثيم...');

      final prefs = await SharedPreferences.getInstance();

      // تحميل وضع الثيم
      final themeModeIndex = prefs.getInt(_themeKey) ?? ThemeMode.system.index;
      _themeMode = ThemeMode.values[themeModeIndex];
      AppLogger.info('📱 استخدام وضع الثيم الافتراضي: ${_getThemeModeString(_themeMode)}');

      // تحميل الثيم اللوني
      _colorTheme = prefs.getString(_colorThemeKey) ?? 'red';
      AppLogger.info('🎨 تم تحميل الثيم اللوني: $_colorTheme');

      AppLogger.info('✅ تم تهيئة مدير الثيم بنجاح');
      notifyListeners();
    } catch (e) {
      AppLogger.error('❌ خطأ في تهيئة مدير الثيم: $e');
      // في حالة الخطأ، استخدم الإعدادات الافتراضية
      _themeMode = ThemeMode.system;
      _colorTheme = 'red';
      notifyListeners();
    }
  }

  /// تغيير وضع الثيم
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    try {
      AppLogger.info('🎨 تغيير وضع الثيم إلى: ${_getThemeModeString(mode)}');

      _themeMode = mode;

      // حفظ الإعدادات
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, mode.index);

      AppLogger.info('💾 تم حفظ إعدادات الثيم بنجاح');
      notifyListeners();
    } catch (e) {
      AppLogger.error('❌ خطأ في حفظ وضع الثيم: $e');
    }
  }

  /// تفعيل الوضع الفاتح
  Future<void> setLightMode() async {
    await setThemeMode(ThemeMode.light);
  }

  /// تفعيل الوضع الداكن
  Future<void> setDarkMode() async {
    await setThemeMode(ThemeMode.dark);
  }

  /// تفعيل وضع النظام
  Future<void> setSystemMode() async {
    await setThemeMode(ThemeMode.system);
  }

  /// تغيير الثيم اللوني
  Future<void> setColorTheme(String themeKey) async {
    if (_colorTheme == themeKey) return;

    try {
      AppLogger.info('🎨 تغيير الثيم اللوني إلى: $themeKey');

      _colorTheme = themeKey;

      // حفظ الإعدادات
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_colorThemeKey, themeKey);

      AppLogger.info('💾 تم حفظ الثيم اللوني بنجاح');
      notifyListeners();
    } catch (e) {
      AppLogger.error('❌ خطأ في حفظ الثيم اللوني: $e');
    }
  }

  // ========== 🔧 دوال مساعدة ==========

  /// الحصول على اسم وضع الثيم
  String _getThemeModeString(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'فاتح';
      case ThemeMode.dark:
        return 'داكن';
      case ThemeMode.system:
        return 'النظام';
    }
  }

  /// الحصول على وصف وضع الثيم
  String getThemeModeDescription() {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'الوضع الفاتح مفعل';
      case ThemeMode.dark:
        return 'الوضع الداكن مفعل';
      case ThemeMode.system:
        return 'يتبع إعدادات النظام';
    }
  }

  /// الحصول على أيقونة وضع الثيم
  IconData getThemeModeIcon() {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  /// الحصول على اسم عرض وضع الثيم
  String getThemeModeDisplayName() {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'الوضع الفاتح';
      case ThemeMode.dark:
        return 'الوضع الداكن';
      case ThemeMode.system:
        return 'وضع النظام';
    }
  }
}
