import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/app_logger.dart';
import 'advanced_color_system.dart';
import 'intelligent_theme_engine.dart';
import 'unified_theme_system.dart';
import 'color_harmony_system.dart';
import 'app_colors.dart';

/// مدير الثيم - يتحكم في حفظ واسترجاع إعدادات الثيم والألوان
///
/// 🎛️ الوظائف:
/// - حفظ واسترجاع إعدادات الثيم تلقائياً
/// - التبديل بين الوضع الفاتح والداكن ووضع النظام
/// - إدارة الثيمات اللونية المختلفة (8 ثيمات)
/// - إشعار المستمعين عند تغيير الثيم
/// - معالجة الأخطاء وحفظ الإعدادات الافتراضية
class ThemeManager extends ChangeNotifier {
  static const String _themeKey = 'app_theme_mode';
  static const String _systemThemeKey = 'follow_system_theme';
  static const String _colorThemeKey = 'app_color_theme';

  ThemeMode _themeMode = ThemeMode.system;
  bool _followSystemTheme = true;
  bool _isInitialized = false;
  String _colorTheme = 'red'; // الثيم اللوني الافتراضي

  /// الحصول على وضع الثيم الحالي
  ThemeMode get themeMode => _themeMode;

  /// هل يتبع ثيم النظام؟
  bool get followSystemTheme => _followSystemTheme;

  /// هل تم تهيئة المدير؟
  bool get isInitialized => _isInitialized;

  /// الحصول على الثيم اللوني الحالي
  String get colorTheme => _colorTheme;

  /// الحصول على اسم الثيم اللوني الحالي
  String get colorThemeName {
    final themeData = AppColors.availableThemes[_colorTheme];
    return themeData?['name'] ?? 'أحمر تاجر بلس العصري';
  }

  /// الحصول على اللون الأساسي للثيم الحالي
  Color get currentPrimaryColor {
    final themeData = AppColors.availableThemes[_colorTheme];
    return themeData?['primary'] ?? AppColors.primary;
  }

  /// إنشاء ثيم ذكي للوضع الفاتح
  ThemeData createIntelligentLightTheme() {
    return IntelligentThemeEngine.createIntelligentTheme(
      primaryColor: currentPrimaryColor,
      brightness: Brightness.light,
    );
  }

  /// إنشاء ثيم ذكي للوضع الداكن
  ThemeData createIntelligentDarkTheme() {
    return IntelligentThemeEngine.createIntelligentTheme(
      primaryColor: currentPrimaryColor,
      brightness: Brightness.dark,
    );
  }

  /// تحليل الثيم الحالي
  ThemeAnalysis analyzeCurrentTheme(ThemeData theme) {
    return IntelligentThemeEngine.analyzeCurrentTheme(theme);
  }

  /// إنشاء لوحة ألوان متناسقة للثيم الحالي
  Map<String, Color> generateCurrentColorPalette() {
    return AdvancedColorSystem.generateHarmoniousPalette(currentPrimaryColor);
  }

  /// الحصول على جميع الثيمات المتاحة
  List<String> get availableThemeKeys {
    return AppColors.availableThemes.keys.toList();
  }

  /// فحص ما إذا كان الثيم يدعم إمكانية الوصول
  bool isCurrentThemeAccessible() {
    final analysis = AdvancedColorSystem.analyzeColor(currentPrimaryColor);
    return analysis.accessibility == AccessibilityLevel.AAA;
  }

  /// إنشاء ثيم موحد ومتوافق بالكامل للوضع الفاتح
  ThemeData createUnifiedLightTheme() {
    return UnifiedThemeSystem.createUnifiedTheme(
      primaryColor: currentPrimaryColor,
      brightness: Brightness.light,
    );
  }

  /// إنشاء ثيم موحد ومتوافق بالكامل للوضع الداكن
  ThemeData createUnifiedDarkTheme() {
    return UnifiedThemeSystem.createUnifiedTheme(
      primaryColor: currentPrimaryColor,
      brightness: Brightness.dark,
    );
  }

  /// إنشاء نظام طبقات متوافقة للثيم الحالي
  ColorLayerSystem createCurrentColorLayers({bool isDarkMode = false}) {
    return ColorHarmonySystem.createHarmoniousLayers(
      primaryColor: currentPrimaryColor,
      isDarkMode: isDarkMode,
    );
  }

  /// إنشاء لوحة ألوان النصوص المتوافقة
  TextColorPalette createCurrentTextColors({
    required Color backgroundColor,
    bool isDarkMode = false,
  }) {
    return ColorHarmonySystem.createTextColorPalette(
      backgroundColor: backgroundColor,
      primaryColor: currentPrimaryColor,
      isDarkMode: isDarkMode,
    );
  }

  /// إنشاء لوحة ألوان الأزرار المتوافقة
  ButtonColorPalette createCurrentButtonColors({
    required Color backgroundColor,
    bool isDarkMode = false,
  }) {
    return ColorHarmonySystem.createButtonColorPalette(
      primaryColor: currentPrimaryColor,
      backgroundColor: backgroundColor,
      isDarkMode: isDarkMode,
    );
  }

  /// فحص التوافق بين لونين
  ColorCompatibilityResult checkColorCompatibility(
    Color foregroundColor,
    Color backgroundColor, {
    double minimumContrast = 4.5,
    bool autoFix = true,
  }) {
    return ColorHarmonySystem.checkLayerCompatibility(
      foregroundColor,
      backgroundColor,
      minimumContrast: minimumContrast,
      autoFix: autoFix,
    );
  }

  /// الحصول على معلومات مفصلة عن الثيم الحالي
  Map<String, dynamic> getCurrentThemeInfo() {
    final analysis = AdvancedColorSystem.analyzeColor(currentPrimaryColor);
    final themeData = AppColors.availableThemes[_colorTheme];

    return {
      'themeName': colorThemeName,
      'themeKey': _colorTheme,
      'primaryColor':
          '#${currentPrimaryColor.value.toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}',
      'isDarkMode': isDarkMode,
      'colorAnalysis': {
        'hue': analysis.hue,
        'saturation': analysis.saturation,
        'lightness': analysis.lightness,
        'luminance': analysis.luminance,
        'temperature': analysis.temperature.toString(),
        'mood': analysis.mood.toString(),
        'accessibility': analysis.accessibility.toString(),
      },
      'isAccessible': isCurrentThemeAccessible(),
      'category': themeData?['category'] ?? 'unknown',
      'mood': themeData?['mood'] ?? 'unknown',
    };
  }

  /// هل الوضع الحالي داكن؟
  bool get isDarkMode {
    switch (_themeMode) {
      case ThemeMode.dark:
        return true;
      case ThemeMode.light:
        return false;
      case ThemeMode.system:
        // في حالة النظام، نحتاج للتحقق من إعدادات النظام
        final window = WidgetsBinding.instance.platformDispatcher;
        return window.platformBrightness == Brightness.dark;
    }
  }

  /// تهيئة مدير الثيم وتحميل الإعدادات المحفوظة
  Future<void> initialize() async {
    try {
      AppLogger.info('🎨 بدء تهيئة مدير الثيم...');

      final prefs = await SharedPreferences.getInstance();

      // تحميل وضع الثيم
      final savedThemeIndex = prefs.getInt(_themeKey);
      if (savedThemeIndex != null &&
          savedThemeIndex < ThemeMode.values.length) {
        _themeMode = ThemeMode.values[savedThemeIndex];
        AppLogger.info(
            '📱 تم تحميل وضع الثيم المحفوظ: ${_getThemeModeString(_themeMode)}');
      } else {
        _themeMode = ThemeMode.system;
        AppLogger.info('📱 استخدام وضع الثيم الافتراضي: النظام');
      }

      // تحميل إعداد اتباع ثيم النظام
      _followSystemTheme = prefs.getBool(_systemThemeKey) ?? true;
      AppLogger.info('🔄 اتباع ثيم النظام: $_followSystemTheme');

      // تحميل الثيم اللوني
      _colorTheme = prefs.getString(_colorThemeKey) ?? 'red';
      AppLogger.info('🎨 تم تحميل الثيم اللوني: $_colorTheme');

      // تحديث الألوان الديناميكية
      // DynamicColors.setTheme(_colorTheme); // سيتم تنفيذه لاحقاً

      _isInitialized = true;
      AppLogger.info('✅ تم تهيئة مدير الثيم بنجاح');

      notifyListeners();
    } catch (e) {
      AppLogger.error('❌ خطأ في تهيئة مدير الثيم: $e');
      // في حالة الخطأ، استخدم الإعدادات الافتراضية
      _themeMode = ThemeMode.system;
      _followSystemTheme = true;
      _isInitialized = true;
      notifyListeners();
    }
  }

  /// تغيير وضع الثيم
  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;

    try {
      AppLogger.info('🎨 تغيير وضع الثيم إلى: ${_getThemeModeString(mode)}');

      _themeMode = mode;
      _followSystemTheme = mode == ThemeMode.system;

      // حفظ الإعدادات
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, mode.index);
      await prefs.setBool(_systemThemeKey, _followSystemTheme);

      AppLogger.info('💾 تم حفظ إعدادات الثيم بنجاح');

      notifyListeners();
    } catch (e) {
      AppLogger.error('❌ خطأ في تغيير وضع الثيم: $e');
    }
  }

  /// تفعيل الوضع الفاتح
  Future<void> setLightMode() async {
    await setThemeMode(ThemeMode.light);
  }

  /// تفعيل الوضع الداكن
  Future<void> setDarkMode() async {
    await setThemeMode(ThemeMode.dark);
  }

  /// تفعيل وضع النظام
  Future<void> setSystemMode() async {
    await setThemeMode(ThemeMode.system);
  }

  /// تغيير الثيم اللوني
  Future<void> setColorTheme(String colorTheme) async {
    if (_colorTheme == colorTheme) return;

    try {
      AppLogger.info('🎨 تغيير الثيم اللوني إلى: $colorTheme');

      _colorTheme = colorTheme;

      // تحديث الألوان الديناميكية
      // DynamicColors.setTheme(colorTheme); // سيتم تنفيذه لاحقاً

      // حفظ الإعدادات
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_colorThemeKey, colorTheme);

      AppLogger.info('💾 تم حفظ الثيم اللوني بنجاح');

      notifyListeners();
    } catch (e) {
      AppLogger.error('❌ خطأ في تغيير الثيم اللوني: $e');
    }
  }

  /// تبديل بين الوضع الفاتح والداكن
  Future<void> toggleTheme() async {
    switch (_themeMode) {
      case ThemeMode.light:
        await setDarkMode();
        break;
      case ThemeMode.dark:
        await setLightMode();
        break;
      case ThemeMode.system:
        // في وضع النظام، نبدل حسب الوضع الحالي للنظام
        final window = WidgetsBinding.instance.platformDispatcher;
        if (window.platformBrightness == Brightness.dark) {
          await setLightMode();
        } else {
          await setDarkMode();
        }
        break;
    }
  }

  /// تحديث إعداد اتباع ثيم النظام
  Future<void> setFollowSystemTheme(bool follow) async {
    if (_followSystemTheme == follow) return;

    try {
      AppLogger.info('🔄 تحديث إعداد اتباع ثيم النظام: $follow');

      _followSystemTheme = follow;

      if (follow) {
        _themeMode = ThemeMode.system;
      }

      // حفظ الإعدادات
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_systemThemeKey, follow);
      await prefs.setInt(_themeKey, _themeMode.index);

      AppLogger.info('💾 تم حفظ إعداد اتباع ثيم النظام بنجاح');

      notifyListeners();
    } catch (e) {
      AppLogger.error('❌ خطأ في تحديث إعداد اتباع ثيم النظام: $e');
    }
  }

  /// الحصول على اسم وضع الثيم بالعربية
  String getThemeModeDisplayName() {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'الوضع الفاتح';
      case ThemeMode.dark:
        return 'الوضع الداكن';
      case ThemeMode.system:
        return 'حسب النظام';
    }
  }

  /// الحصول على أيقونة وضع الثيم
  IconData getThemeModeIcon() {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  /// الحصول على وصف وضع الثيم
  String getThemeModeDescription() {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'استخدام الوضع الفاتح دائماً';
      case ThemeMode.dark:
        return 'استخدام الوضع الداكن دائماً';
      case ThemeMode.system:
        return 'يتبع إعدادات النظام تلقائياً';
    }
  }

  /// إعادة تعيين إعدادات الثيم للافتراضية
  Future<void> resetToDefault() async {
    try {
      AppLogger.info('🔄 إعادة تعيين إعدادات الثيم للافتراضية...');

      _themeMode = ThemeMode.system;
      _followSystemTheme = true;

      // حذف الإعدادات المحفوظة
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_themeKey);
      await prefs.remove(_systemThemeKey);

      AppLogger.info('✅ تم إعادة تعيين إعدادات الثيم بنجاح');

      notifyListeners();
    } catch (e) {
      AppLogger.error('❌ خطأ في إعادة تعيين إعدادات الثيم: $e');
    }
  }

  /// الحصول على معلومات مفصلة عن حالة الثيم
  Map<String, dynamic> getThemeInfo() {
    return {
      'themeMode': _getThemeModeString(_themeMode),
      'followSystemTheme': _followSystemTheme,
      'isDarkMode': isDarkMode,
      'isInitialized': _isInitialized,
      'displayName': getThemeModeDisplayName(),
      'description': getThemeModeDescription(),
      'colorTheme': _colorTheme,
      'colorThemeName': colorThemeName,
    };
  }

  /// دالة مساعدة للحصول على نص وضع الثيم
  String _getThemeModeString(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }

  /// معالج تغيير إعدادات النظام
  void handleSystemThemeChange() {
    if (_themeMode == ThemeMode.system) {
      AppLogger.info('🔄 تم تغيير ثيم النظام، تحديث الواجهة...');
      notifyListeners();
    }
  }

  /// تنظيف الموارد
  @override
  void dispose() {
    AppLogger.info('🧹 تنظيف موارد مدير الثيم');
    super.dispose();
  }
}

/// مثيل مشترك من مدير الثيم
final ThemeManager themeManager = ThemeManager();
