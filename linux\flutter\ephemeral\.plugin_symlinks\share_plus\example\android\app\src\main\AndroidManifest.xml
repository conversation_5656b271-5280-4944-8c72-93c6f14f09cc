<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="io.flutter.plugins.shareexample">

    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:label="share_example">
        <activity
            android:name="io.flutter.embedding.android.FlutterActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:theme="@android:style/Theme.Black.NoTitleBar"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>
