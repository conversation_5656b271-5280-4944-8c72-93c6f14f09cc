import 'package:flutter_test/flutter_test.dart';

import 'package:tajer_plus/core/providers/app_providers.dart';
import 'package:tajer_plus/features/dashboard/presenters/dashboard_presenter.dart';

void main() {
  group('تطبيق تاجر بلس - اختبارات أساسية', () {
    test('اختبار أساسي للتأكد من عمل التطبيق', () {
      expect(true, isTrue);
    });

    test('اختبار مدير التحميل الكسول', () {
      // اختبار إنشاء presenter جديد
      final presenter1 =
          LazyPresenterManager.getLazyPresenter<DashboardPresenter>(
        () => DashboardPresenter(),
      );

      // اختبار الحصول على نفس الـ instance
      final presenter2 =
          LazyPresenterManager.getLazyPresenter<DashboardPresenter>(
        () => DashboardPresenter(),
      );

      // التحقق من أنهما نفس الـ instance
      expect(presenter1, equals(presenter2));

      // تنظيف
      LazyPresenterManager.clearAll();
    });

    test('اختبار تنظيف الموارد', () {
      // إنشاء presenter
      LazyPresenterManager.getLazyPresenter<DashboardPresenter>(
        () => DashboardPresenter(),
      );

      // تنظيف
      LazyPresenterManager.clear<DashboardPresenter>();

      // إنشاء presenter جديد
      final newPresenter =
          LazyPresenterManager.getLazyPresenter<DashboardPresenter>(
        () => DashboardPresenter(),
      );

      // التحقق من إنشاء instance جديد
      expect(newPresenter, isNotNull);
    });

    test('اختبار استهلاك الذاكرة للـ Providers', () {
      final providers = AppProviders.getAllProviders();

      // التحقق من عدد الـ providers
      expect(providers.length, greaterThan(0));
      expect(providers.length, lessThan(50)); // حد أقصى معقول

      // تنظيف
      AppProviders.dispose();
    });
  });
}
