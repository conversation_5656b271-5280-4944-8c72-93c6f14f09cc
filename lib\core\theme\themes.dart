import 'package:flutter/material.dart';
import 'colors.dart';
import 'app_typography.dart';
import 'app_dimensions.dart';

/// 🎨 نظام الثيمات الموحد والمبسط لتطبيق تاجر بلس
/// 
/// هذا الملف يحتوي على جميع إعدادات الثيمات للوضعين الفاتح والداكن
/// مع تطبيق نظام الألوان الجديد المبسط
/// 
/// 📋 الميزات:
/// - ثيمات بسيطة وواضحة
/// - دعم كامل للوضع الداكن والفاتح
/// - تطبيق Material Design 3
/// - ألوان متناسقة ومريحة للعين
class AppThemes {
  AppThemes._(); // منع إنشاء كائن

  // ========== 🌞 الثيم الفاتح ==========
  
  /// إنشاء ثيم فاتح بلون أساسي محدد
  static ThemeData createLightTheme(String themeKey) {
    final themeColors = AppColors.getTheme(themeKey);
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      
      // 🎨 نظام الألوان
      colorScheme: ColorScheme.light(
        primary: themeColors.primary,
        primaryContainer: themeColors.secondary,
        secondary: themeColors.secondary,
        surface: AppColors.lightSurface,
        background: AppColors.lightBackground,
        error: AppColors.error,
        onPrimary: AppColors.getTextColor(themeColors.primary),
        onSecondary: AppColors.getTextColor(themeColors.secondary),
        onSurface: AppColors.lightTextPrimary,
        onBackground: AppColors.lightTextPrimary,
        onError: AppColors.getTextColor(AppColors.error),
        outline: AppColors.lightBorder,
        shadow: AppColors.lightShadow,
      ),
      
      // 📱 خلفية التطبيق
      scaffoldBackgroundColor: AppColors.lightBackground,
      
      // 🔤 نظام الخطوط
      textTheme: _createTextTheme(AppColors.lightTextPrimary),
      
      // 🔘 أزرار مرتفعة
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: themeColors.primary,
          foregroundColor: AppColors.getTextColor(themeColors.primary),
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          ),
        ),
      ),
      
      // 🔲 أزرار مسطحة
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: themeColors.primary,
          side: BorderSide(color: themeColors.primary),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          ),
        ),
      ),
      
      // 📝 حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.lightSurface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          borderSide: const BorderSide(color: AppColors.lightBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          borderSide: const BorderSide(color: AppColors.lightBorder),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          borderSide: BorderSide(color: themeColors.primary, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      
      // 🃏 البطاقات
      cardTheme: CardTheme(
        color: AppColors.lightSurface,
        elevation: 2,
        shadowColor: AppColors.lightShadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
        ),
      ),
      
      // 📱 شريط التطبيق
      appBarTheme: AppBarTheme(
        backgroundColor: themeColors.primary,
        foregroundColor: AppColors.getTextColor(themeColors.primary),
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: AppColors.getTextColor(themeColors.primary),
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      
      // 🔘 الزر العائم
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: themeColors.primary,
        foregroundColor: AppColors.getTextColor(themeColors.primary),
        elevation: 4,
      ),
      
      // 📋 القوائم
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      
      // 🔄 مؤشر التحميل
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: themeColors.primary,
      ),
      
      // 🎛️ المفاتيح
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return themeColors.primary;
          }
          return AppColors.lightTextHint;
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return themeColors.secondary;
          }
          return AppColors.lightBorder;
        }),
      ),
    );
  }

  // ========== 🌙 الثيم الداكن ==========
  
  /// إنشاء ثيم داكن بلون أساسي محدد
  static ThemeData createDarkTheme(String themeKey) {
    final themeColors = AppColors.getTheme(themeKey);
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      
      // 🎨 نظام الألوان
      colorScheme: ColorScheme.dark(
        primary: themeColors.primary,
        primaryContainer: themeColors.secondary,
        secondary: themeColors.secondary,
        surface: AppColors.darkSurface,
        background: AppColors.darkBackground,
        error: AppColors.error,
        onPrimary: AppColors.getTextColor(themeColors.primary),
        onSecondary: AppColors.getTextColor(themeColors.secondary),
        onSurface: AppColors.darkTextPrimary,
        onBackground: AppColors.darkTextPrimary,
        onError: AppColors.getTextColor(AppColors.error),
        outline: AppColors.darkBorder,
        shadow: AppColors.darkShadow,
      ),
      
      // 📱 خلفية التطبيق
      scaffoldBackgroundColor: AppColors.darkBackground,
      
      // 🔤 نظام الخطوط
      textTheme: _createTextTheme(AppColors.darkTextPrimary),
      
      // 🔘 أزرار مرتفعة
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: themeColors.primary,
          foregroundColor: AppColors.getTextColor(themeColors.primary),
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          ),
        ),
      ),
      
      // 🔲 أزرار مسطحة
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: themeColors.primary,
          side: BorderSide(color: themeColors.primary),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          ),
        ),
      ),
      
      // 📝 حقول الإدخال
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.darkSurface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          borderSide: const BorderSide(color: AppColors.darkBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          borderSide: const BorderSide(color: AppColors.darkBorder),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
          borderSide: BorderSide(color: themeColors.primary, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      
      // 🃏 البطاقات
      cardTheme: CardTheme(
        color: AppColors.darkSurface,
        elevation: 2,
        shadowColor: AppColors.darkShadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadius),
        ),
      ),
      
      // 📱 شريط التطبيق
      appBarTheme: AppBarTheme(
        backgroundColor: themeColors.primary,
        foregroundColor: AppColors.getTextColor(themeColors.primary),
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: AppColors.getTextColor(themeColors.primary),
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      
      // 🔘 الزر العائم
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: themeColors.primary,
        foregroundColor: AppColors.getTextColor(themeColors.primary),
        elevation: 4,
      ),
      
      // 📋 القوائم
      listTileTheme: const ListTileThemeData(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      
      // 🔄 مؤشر التحميل
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: themeColors.primary,
      ),
      
      // 🎛️ المفاتيح
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return themeColors.primary;
          }
          return AppColors.darkTextHint;
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return themeColors.secondary;
          }
          return AppColors.darkBorder;
        }),
      ),
    );
  }

  // ========== 🔧 دوال مساعدة ==========
  
  /// إنشاء نظام خطوط موحد
  static TextTheme _createTextTheme(Color textColor) {
    return TextTheme(
      displayLarge: TextStyle(color: textColor, fontSize: 32, fontWeight: FontWeight.bold),
      displayMedium: TextStyle(color: textColor, fontSize: 28, fontWeight: FontWeight.bold),
      displaySmall: TextStyle(color: textColor, fontSize: 24, fontWeight: FontWeight.bold),
      headlineLarge: TextStyle(color: textColor, fontSize: 22, fontWeight: FontWeight.bold),
      headlineMedium: TextStyle(color: textColor, fontSize: 20, fontWeight: FontWeight.bold),
      headlineSmall: TextStyle(color: textColor, fontSize: 18, fontWeight: FontWeight.bold),
      titleLarge: TextStyle(color: textColor, fontSize: 16, fontWeight: FontWeight.bold),
      titleMedium: TextStyle(color: textColor, fontSize: 14, fontWeight: FontWeight.w500),
      titleSmall: TextStyle(color: textColor, fontSize: 12, fontWeight: FontWeight.w500),
      bodyLarge: TextStyle(color: textColor, fontSize: 16),
      bodyMedium: TextStyle(color: textColor, fontSize: 14),
      bodySmall: TextStyle(color: textColor, fontSize: 12),
      labelLarge: TextStyle(color: textColor, fontSize: 14, fontWeight: FontWeight.w500),
      labelMedium: TextStyle(color: textColor, fontSize: 12, fontWeight: FontWeight.w500),
      labelSmall: TextStyle(color: textColor, fontSize: 10, fontWeight: FontWeight.w500),
    );
  }

  // ========== 🎨 الثيمات الافتراضية ==========
  
  /// الثيم الفاتح الافتراضي (أحمر)
  static ThemeData get lightTheme => createLightTheme('red');
  
  /// الثيم الداكن الافتراضي (أحمر)
  static ThemeData get darkTheme => createDarkTheme('red');
}
