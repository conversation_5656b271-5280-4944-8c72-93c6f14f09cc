name: printing
description: >
  Plugin that allows Flutter apps to generate and print documents to
  compatible printers on Android, iOS, macOS, Windows, and Linux,
  as well as web print.
homepage: https://github.com/DavBfr/dart_pdf/tree/master/printing
repository: https://github.com/DavBfr/dart_pdf
issue_tracker: https://github.com/DavBfr/dart_pdf/issues
screenshots:
  - description: 'Printing a document on iOS'
    path: example.png
topics:
  - pdf
  - printer
  - print
  - printing
  - report
version: 5.14.2

environment:
  sdk: ">=3.3.0 <4.0.0"
  flutter: ">=3.22.0"

dependencies:
  ffi: ">=1.1.0 <3.0.0"
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  http: ">=0.13.0 <2.0.0"
  image: ">=4.1.0 <4.6.0"
  meta: ">=1.3.0 <2.0.0"
  pdf: ^3.10.0
  pdf_widget_wrapper: '>=1.0.4 <2.0.0'
  plugin_platform_interface: ^2.1.0
  web: ^1.0.0

dev_dependencies:
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter
  mockito: ^5.4.4

_dependency_overrides:
  pdf:
    path: ../pdf
  pdf_widget_wrapper:
   path: ../widget_wrapper

flutter:
  plugin:
    platforms:
      android:
        package: net.nfet.flutter.printing
        pluginClass: PrintingPlugin
      ios:
        pluginClass: PrintingPlugin
      linux:
        pluginClass: PrintingPlugin
      macos:
        pluginClass: PrintingPlugin
      web:
        fileName: printing_web.dart
        pluginClass: PrintingPlugin
      windows:
        pluginClass: PrintingPlugin
