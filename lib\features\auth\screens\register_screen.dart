import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'dart:convert'; // Para utf8
import 'package:crypto/crypto.dart'; // Para sha256
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/utils/index.dart';

import '../../shared/widgets/custom_card.dart';
import '../../../core/database/database_helper.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/theme/index.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _agreeToTerms = false;

  // معرف المستخدم الافتراضي
  String? _defaultAdminId;
  final bool _isLoadingDefaultAdmin = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _handleRegister() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_agreeToTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            // Removed const
            content: const Text('يجب الموافقة على الشروط والأحكام'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
        return;
      }

      // سنقوم بالبحث عن المستخدم الافتراضي مباشرة عند الضغط على زر التسجيل
      try {
        // عرض مؤشر التحميل
        _showLoadingDialog();

        // البحث عن المستخدم الافتراضي
        final db = await DatabaseHelper().database;
        final List<Map<String, dynamic>> users = await db.rawQuery(
            'SELECT id FROM users WHERE is_deleted = 0 ORDER BY rowid ASC LIMIT 1');

        if (users.isEmpty) {
          // إغلاق مؤشر التحميل
          if (mounted) {
            Navigator.of(context).pop();

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                // Removed const
                content: const Text('لم يتم العثور على المستخدم الافتراضي'),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
          return;
        }

        // تعيين معرف المستخدم الافتراضي
        _defaultAdminId = users.first['id'] as String;
        AppLogger.info('تم العثور على المستخدم الافتراضي: $_defaultAdminId');
      } catch (e) {
        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              // Removed const
              content: Text('خطأ في البحث عن المستخدم الافتراضي: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
        AppLogger.error('خطأ في البحث عن المستخدم الافتراضي: $e');
        return;
      }

      try {
        // تحديث المستخدم مباشرة في قاعدة البيانات
        final db = await DatabaseHelper().database;

        // تشفير كلمة المرور
        final String hashedPassword = _hashPassword(_passwordController.text);

        // تحديث بيانات المستخدم
        final int updatedRows = await db.update(
          'users',
          {
            'username': _usernameController.text,
            'password': hashedPassword,
            'full_name': _nameController.text,
            'email': _emailController.text,
            'phone': _phoneController.text,
            'updated_at': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [_defaultAdminId],
        );

        if (updatedRows == 0) {
          throw Exception('لم يتم تحديث أي صفوف. تأكد من وجود المستخدم.');
        }

        AppLogger.info(
            'تم تحديث بيانات المستخدم بنجاح: ${_usernameController.text}');

        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();
        }

        // عرض رسالة نجاح
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              // Removed const
              content: Text(
                'تم تحديث بيانات الحساب بنجاح!',
                style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimaryContainer),
              ),
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
            ),
          );
        }

        // حفظ حالة إخفاء زر التسجيل في التخزين المحلي
        try {
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('hasLoggedInBefore', true);
          AppLogger.info('✅ تم حفظ حالة إخفاء زر التسجيل بنجاح');
        } catch (e) {
          AppLogger.error('❌ خطأ في حفظ حالة إخفاء زر التسجيل: $e');
        }

        // نقل البيانات إلى شاشة تسجيل الدخول
        if (mounted) {
          // حفظ البيانات في SharedPreferences قبل الانتقال
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('temp_username', _usernameController.text);
          await prefs.setString('temp_password', _passwordController.text);

          // طباعة رسالة توضيحية للتأكد من حفظ البيانات
          AppLogger.info(
              'تم حفظ البيانات مؤقتاً: username=${_usernameController.text}, password=${_passwordController.text}');

          // التحقق من أن الشاشة لا تزال مثبتة قبل الانتقال
          if (mounted) {
            // الانتقال إلى شاشة تسجيل الدخول مع تمرير البيانات
            Navigator.of(context).pushReplacementNamed(
              AppRoutes.login,
              arguments: {
                'username': _usernameController.text,
                'password': _passwordController.text,
              },
            );
          }
        }
      } catch (e) {
        // إغلاق مؤشر التحميل
        if (mounted) {
          Navigator.of(context).pop();
        }

        // عرض رسالة خطأ
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              // Removed const
              content: Text('فشل تحديث بيانات الحساب: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }

        AppLogger.error('خطأ في تحديث بيانات الحساب: $e');
        ErrorTracker.captureError(
          'فشل في تحديث بيانات الحساب',
          error: e,
          stackTrace: StackTrace.current,
          context: {
            'username': _usernameController.text,
            'email': _emailController.text,
            'userId': _defaultAdminId,
          },
        );
      }
    }
  }

  /// تشفير كلمة المرور
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // عرض مؤشر التحميل
  void _showLoadingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'إضافة حساب مدير النظام',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontSize: Layout.getResponsiveFontSize(18),
              ),
        ),
        centerTitle: true,
      ),
      body: _isLoadingDefaultAdmin
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(),
                  SizedBox(height: Layout.h(2)),
                  Text(
                    'جاري تحميل بيانات المستخدم...',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontSize: Layout.getResponsiveFontSize(16),
                        ),
                  ),
                ],
              ),
            )
          : SafeArea(
              child: LayoutBuilder(builder: (context, constraints) {
                return SingleChildScrollView(
                  padding: EdgeInsets.symmetric(
                    horizontal: Layout.w(6),
                    vertical: Layout.h(2),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildAppLogo(constraints),
                      SizedBox(height: Layout.h(2)),
                      Text(
                        'إضافة حساب مدير النظام',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                              fontSize: Layout.getResponsiveFontSize(22),
                            ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: Layout.h(1)),
                      Text(
                        'قم بإضافة بيانات حساب مدير النظام',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                              fontSize: Layout.getResponsiveFontSize(14),
                            ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: Layout.h(3)),
                      CustomCard(
                        child: Padding(
                          padding: EdgeInsets.all(Layout.w(5)),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                _buildTextField(
                                  controller: _nameController,
                                  label: 'الاسم الكامل',
                                  hint: 'عبدالملك الصماط',
                                  icon: Icons.person,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال الاسم الكامل';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: Layout.h(2)),
                                _buildTextField(
                                  controller: _usernameController,
                                  label: 'اسم المستخدم',
                                  hint: 'الصماط',
                                  icon: Icons.account_circle,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال اسم المستخدم';
                                    }
                                    if (value.length < 3) {
                                      return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: Layout.h(2)),
                                _buildTextField(
                                  controller: _emailController,
                                  label: 'البريد الإلكتروني',
                                  hint: '<EMAIL>',
                                  icon: Icons.email,
                                  keyboardType: TextInputType.emailAddress,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال البريد الإلكتروني';
                                    }
                                    if (!RegExp(
                                            r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                                        .hasMatch(value)) {
                                      return 'الرجاء إدخال بريد إلكتروني صحيح';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: Layout.h(2)),
                                _buildTextField(
                                  controller: _phoneController,
                                  label: 'رقم الهاتف',
                                  hint: '967770119544',
                                  icon: Icons.phone,
                                  keyboardType: TextInputType.phone,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال رقم الهاتف';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: Layout.h(2)),
                                _buildPasswordField(
                                  controller: _passwordController,
                                  label: 'كلمة المرور',
                                  hint: 'أدخل كلمة المرور',
                                  isVisible: _isPasswordVisible,
                                  onToggleVisibility: () {
                                    setState(() {
                                      _isPasswordVisible = !_isPasswordVisible;
                                    });
                                  },
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء إدخال كلمة المرور';
                                    }
                                    if (value.length < 6) {
                                      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: Layout.h(2)),
                                _buildPasswordField(
                                  controller: _confirmPasswordController,
                                  label: 'تأكيد كلمة المرور',
                                  hint: 'أعد إدخال كلمة المرور',
                                  isVisible: _isConfirmPasswordVisible,
                                  onToggleVisibility: () {
                                    setState(() {
                                      _isConfirmPasswordVisible =
                                          !_isConfirmPasswordVisible;
                                    });
                                  },
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'الرجاء تأكيد كلمة المرور';
                                    }
                                    if (value != _passwordController.text) {
                                      return 'كلمة المرور غير متطابقة';
                                    }
                                    return null;
                                  },
                                ),
                                SizedBox(height: Layout.h(3)),
                                SizedBox(height: Layout.h(2)),
                                _buildTermsRow(),
                                SizedBox(height: Layout.h(3)),
                                _buildRegisterButton(),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: Layout.h(3)),
                      _buildLoginRow(),
                      SizedBox(height: Layout.h(5)),
                    ],
                  ),
                );
              }),
            ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType keyboardType = TextInputType.text,
    required String? Function(String?) validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      textDirection: TextDirection.ltr,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, size: Layout.getResponsiveIconSize(22)),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: Layout.w(4),
          vertical: Layout.h(2),
        ),
      ),
      validator: validator,
      style: Theme.of(context)
          .textTheme
          .bodyLarge
          ?.copyWith(fontSize: Layout.getResponsiveFontSize(14)),
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required bool isVisible,
    required VoidCallback onToggleVisibility,
    required String? Function(String?) validator,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: !isVisible,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(Icons.lock, size: Layout.getResponsiveIconSize(22)),
        suffixIcon: IconButton(
          icon: Icon(
            isVisible ? Icons.visibility_off : Icons.visibility,
            size: Layout.getResponsiveIconSize(22),
          ),
          onPressed: onToggleVisibility,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: Layout.w(4),
          vertical: Layout.h(2),
        ),
      ),
      validator: validator,
      style: Theme.of(context)
          .textTheme
          .bodyLarge
          ?.copyWith(fontSize: Layout.getResponsiveFontSize(14)),
    );
  }

  Widget _buildTermsRow() {
    final theme = Theme.of(context);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Transform.scale(
          scale: Layout.isMobile() ? 0.9 : 1.0,
          child: Checkbox(
            value: _agreeToTerms,
            activeColor: theme.colorScheme.primary,
            onChanged: (value) {
              setState(() {
                _agreeToTerms = value ?? false;
              });
            },
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _agreeToTerms = !_agreeToTerms;
              });
            },
            child: RichText(
              text: TextSpan(
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontSize: Layout.getResponsiveFontSize(14),
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                children: [
                  const TextSpan(
                    text: 'أوافق على ',
                  ),
                  TextSpan(
                    text: 'الشروط والأحكام',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      decoration: TextDecoration.underline,
                      fontSize: Layout.getResponsiveFontSize(14),
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // فتح صفحة الشروط والأحكام
                        Navigator.of(context).pushNamed(AppRoutes.legal);
                      },
                  ),
                  const TextSpan(
                    text: ' و',
                  ),
                  TextSpan(
                    text: 'سياسة الخصوصية',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      decoration: TextDecoration.underline,
                      fontSize: Layout.getResponsiveFontSize(14),
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        // فتح صفحة سياسة الخصوصية
                        Navigator.of(context).pushNamed(AppRoutes.legal);
                      },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRegisterButton() {
    final theme = Theme.of(context);
    return ElevatedButton(
      onPressed: _agreeToTerms ? _handleRegister : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: _agreeToTerms
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurface
                .withValues(alpha: 0.12), // Standard disabled color
        foregroundColor: _agreeToTerms
            ? theme.colorScheme.onPrimary
            : theme.colorScheme.onSurface
                .withValues(alpha: 0.38), // Standard disabled text color
        padding: EdgeInsets.symmetric(vertical: Layout.h(2)),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 2,
        textStyle: theme.textTheme.labelLarge?.copyWith(
          fontSize: Layout.getResponsiveFontSize(16),
          fontWeight: FontWeight.bold,
        ),
      ),
      child: const Text(
        'إنشاء حساب',
      ),
    );
  }

  Widget _buildLoginRow() {
    final theme = Theme.of(context);
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'العودة إلى',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontSize: Layout.getResponsiveFontSize(14),
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text(
            'شاشة تسجيل الدخول',
            style: theme.textTheme.labelLarge?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.bold,
              fontSize: Layout.getResponsiveFontSize(14),
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAppLogo(BoxConstraints constraints) {
    // حساب الحجم المناسب بناءً على عرض الشاشة
    final logoSize = constraints.maxWidth * 0.22; // 22% من عرض الشاشة
    final iconSize = logoSize * 0.25; // 25% من حجم الشعار

    return Center(
      child: Container(
        width: logoSize,
        height: logoSize,
        decoration: BoxDecoration(
          color: AppColors.lightTextSecondary,
          borderRadius:
              BorderRadius.circular(logoSize * 0.15), // 15% من حجم الشعار
          boxShadow: [
            BoxShadow(
              color: AppColors.lightTextPrimary.withValues(alpha: 0.1),
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_cart,
                  size: iconSize,
                  color: AppColors.lightTextSecondary,
                ),
                SizedBox(width: iconSize * 0.3),
                Icon(
                  Icons.store,
                  size: iconSize,
                  color: AppColors.lightTextSecondary,
                ),
              ],
            ),
            SizedBox(height: iconSize * 0.5),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inventory,
                  size: iconSize,
                  color: AppColors.lightTextSecondary,
                ),
                SizedBox(width: iconSize * 0.3),
                Icon(
                  Icons.people,
                  size: iconSize,
                  color: AppColors.lightTextSecondary,
                ),
              ],
            ),
            SizedBox(height: iconSize * 0.3),
            Text(
              'تاجر بلس',
              style: AppTypography(
                fontSize: iconSize * 0.6,
                fontWeight: FontWeight.bold,
                color: AppColors.lightTextSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
