name: share_plus
description: Flutter plugin for sharing content via the platform share UI, using the ACTION_SEND intent on Android and UIActivityViewController on iOS.
version: 11.0.0
homepage: https://github.com/fluttercommunity/plus_plugins
repository: https://github.com/fluttercommunity/plus_plugins/tree/main/packages/share_plus/share_plus
issue_tracker: https://github.com/fluttercommunity/plus_plugins/labels/share_plus
topics:
  - share
  - utils

flutter:
  plugin:
    platforms:
      android:
        package: dev.fluttercommunity.plus.share
        pluginClass: SharePlusPlugin
      ios:
        pluginClass: FPPSharePlusPlugin
      linux:
        dartPluginClass: SharePlusLinuxPlugin
      macos:
        pluginClass: SharePlusMacosPlugin
      web:
        pluginClass: SharePlusWebPlugin
        fileName: src/share_plus_web.dart
      windows:
        dartPluginClass: SharePlusWindowsPlugin
        pluginClass: SharePlusWindowsPluginCApi

dependencies:
  cross_file: ^0.3.4+2
  meta: ^1.8.0
  mime: ">=1.0.4 <3.0.0"
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  share_plus_platform_interface: ^6.0.0
  file: ">=6.1.4 <8.0.0"
  url_launcher_web: ^2.3.2
  url_launcher_windows: ^3.1.2
  url_launcher_linux: ^3.1.1
  url_launcher_platform_interface: ^2.3.2
  ffi: ^2.1.2
  web: ^1.0.0
  win32: ^5.5.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ">=4.0.0 <6.0.0"

environment:
  sdk: ">=3.4.0 <4.0.0"
  flutter: ">=3.22.0"

