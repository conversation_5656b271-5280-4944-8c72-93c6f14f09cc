# دليل قاعدة البيانات الموحدة لتطبيق تاجر بلس

## مقدمة

تم توحيد قاعدة البيانات في تطبيق تاجر بلس لتحسين الأداء وتسهيل الصيانة وتجنب المشاكل المتعلقة بتكرار الجداول والتعريفات. يوفر هذا الدليل شرحًا لهيكل قاعدة البيانات الموحدة وكيفية استخدامها في التطبيق.

## الملفات الرئيسية

1. **unified_database_schema.dart**: يحتوي على تعريفات جميع جداول قاعدة البيانات في المشروع، وهو المرجع الوحيد لهيكل قاعدة البيانات.
2. **database_helper.dart**: يوفر واجهة للتعامل مع قاعدة البيانات، مثل فتح وإغلاق الاتصال وتنفيذ الاستعلامات.
3. **database_initializer.dart**: يستخدم لتهيئة قاعدة البيانات وإنشاء الجداول المطلوبة.
4. **database_unifier.dart**: يستخدم لتوحيد الجداول المتكررة وإصلاح مشاكل قاعدة البيانات.

## هيكل قاعدة البيانات

### الجداول الرئيسية

1. **users**: يحتوي على بيانات المستخدمين.
2. **roles**: يحتوي على أدوار المستخدمين.
3. **permissions**: يحتوي على صلاحيات المستخدمين.
4. **role_permissions**: يربط بين الأدوار والصلاحيات.
5. **categories**: جدول موحد للفئات (فئات المنتجات، فئات المصروفات، فئات الحسابات، إلخ).
6. **products**: يحتوي على بيانات المنتجات.
7. **units**: يحتوي على وحدات القياس.
8. **customers**: يحتوي على بيانات العملاء.
9. **suppliers**: يحتوي على بيانات الموردين.
10. **warehouses**: يحتوي على بيانات المخازن.
11. **inventory**: يحتوي على بيانات المخزون.
12. **inventory_transactions**: يحتوي على حركات المخزون.
13. **invoices**: يحتوي على بيانات الفواتير.
14. **invoice_items**: يحتوي على بيانات عناصر الفواتير.
15. **payments**: يحتوي على بيانات المدفوعات.
16. **payment_methods**: يحتوي على طرق الدفع.
17. **accounts**: يحتوي على بيانات الحسابات.
18. **expenses**: يحتوي على بيانات المصروفات.
19. **journal_entries**: يحتوي على قيود اليومية.
20. **journal_entry_details**: يحتوي على تفاصيل قيود اليومية.
21. **fiscal_periods**: يحتوي على الفترات المالية.
22. **audit_logs**: يحتوي على سجلات التدقيق.
23. **transactions**: يحتوي على المعاملات المالية.
24. **settings**: يحتوي على إعدادات التطبيق.

### جدول الفئات الموحد

تم توحيد جداول الفئات المختلفة (فئات المنتجات، فئات المصروفات، فئات الحسابات) في جدول واحد يسمى `categories`. يحتوي هذا الجدول على حقل `type` يحدد نوع الفئة (product, expense, account, etc.).

```sql
CREATE TABLE IF NOT EXISTS categories (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  parent_id TEXT,
  image_path TEXT,
  type TEXT NOT NULL, -- product, account, expense, etc.
  account_type TEXT, -- asset, liability, equity, revenue, expense (for account categories)
  is_active INTEGER NOT NULL DEFAULT 1,
  created_at TEXT NOT NULL,
  created_by TEXT,
  updated_at TEXT,
  updated_by TEXT,
  is_deleted INTEGER NOT NULL DEFAULT 0,
  FOREIGN KEY (parent_id) REFERENCES categories (id) ON DELETE SET NULL
)
```

## كيفية استخدام قاعدة البيانات الموحدة

### 1. تهيئة قاعدة البيانات

```dart
import 'package:tajer_plus/core/database/database_initializer.dart';

// تهيئة قاعدة البيانات
final databaseInitializer = DatabaseInitializer();
final dbInitialized = await databaseInitializer.initializeDatabase();

if (dbInitialized) {
  print('تم تهيئة قاعدة البيانات بنجاح');
} else {
  print('فشل في تهيئة قاعدة البيانات');
}
```

### 2. الوصول إلى قاعدة البيانات

```dart
import 'package:tajer_plus/core/database/database_helper.dart';

// الحصول على قاعدة البيانات
final dbHelper = DatabaseHelper();
final db = await dbHelper.database;

// تنفيذ استعلام
final result = await db.query('users');
```

### 3. إنشاء جدول جديد

إذا كنت بحاجة إلى إضافة جدول جديد، يجب عليك إضافته إلى ملف `unified_database_schema.dart`:

```dart
// إضافة دالة لإنشاء الجدول الجديد
static Future<void> createNewTableName(dynamic db) async {
  await db.execute('''
    CREATE TABLE IF NOT EXISTS new_table_name (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      created_at TEXT NOT NULL,
      updated_at TEXT,
      is_deleted INTEGER NOT NULL DEFAULT 0
    )
  ''');
}

// إضافة استدعاء الدالة في دالة createAllTables
static Future<void> createAllTables(dynamic db) async {
  try {
    // ...
    await createNewTableName(db);
    // ...
  } catch (e, stackTrace) {
    // ...
  }
}
```

### 4. استخدام جدول الفئات الموحد

```dart
// استعلام عن فئات المنتجات
final productCategories = await db.query(
  'categories',
  where: 'type = ? AND is_deleted = 0',
  whereArgs: ['product'],
);

// استعلام عن فئات المصروفات
final expenseCategories = await db.query(
  'categories',
  where: 'type = ? AND is_deleted = 0',
  whereArgs: ['expense'],
);

// إضافة فئة جديدة
await db.insert(
  'categories',
  {
    'id': 'category_id',
    'name': 'اسم الفئة',
    'description': 'وصف الفئة',
    'type': 'product', // نوع الفئة
    'is_active': 1,
    'created_at': DateTime.now().toIso8601String(),
  },
);
```

## إرشادات للمطورين

1. **استخدم الملف الموحد**: استخدم ملف `unified_database_schema.dart` لإنشاء جداول قاعدة البيانات بدلاً من إنشاء جداول جديدة في ملفات أخرى.

2. **استخدم جدول الفئات الموحد**: استخدم جدول `categories` الموحد بدلاً من إنشاء جداول منفصلة للفئات المختلفة.

3. **استخدم الحقول الموحدة**: استخدم أسماء الحقول الموحدة في جميع الجداول، مثل `id`, `name`, `description`, `created_at`, `updated_at`, `is_deleted`, إلخ.

4. **استخدم العلاقات بين الجداول**: استخدم العلاقات المعرفة في الملف الموحد للحفاظ على سلامة البيانات.

5. **وثق التغييرات**: وثق أي تغييرات تقوم بها في هيكل قاعدة البيانات.

## الخلاصة

استخدام قاعدة البيانات الموحدة في تطبيق تاجر بلس يساعد على تحسين أداء التطبيق وسهولة صيانته وتطويره. يجب على المطورين استخدام الملف الموحد في جميع أنحاء التطبيق لضمان اتساق البيانات وتجنب المشاكل.
