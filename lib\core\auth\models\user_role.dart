import 'dart:convert';
import '../roles_schema.dart';
import '../../utils/app_logger.dart';

/// نموذج دور المستخدم الموحد
/// تم توحيده من نموذجي UserRole و Role
class UserRole {
  final String id;
  final String name;
  final String displayName;
  final String? description; // إضافة وصف الدور من نموذج Role
  final List<String> permissions;
  final bool isCustom;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? createdBy; // إضافة منشئ الدور من نموذج Role
  final String? updatedBy; // إضافة محدث الدور من نموذج Role
  final bool isDeleted; // إضافة حالة الحذف من نموذج Role

  /// إنشاء دور مستخدم جديد
  UserRole({
    required this.id,
    required this.name,
    required this.displayName,
    this.description,
    required this.permissions,
    this.isCustom = false,
    DateTime? createdAt,
    this.updatedAt,
    this.createdBy,
    this.updatedBy,
    this.isDeleted = false,
  }) : createdAt = createdAt ?? DateTime.now();

  /// إنشاء نسخة جديدة من دور المستخدم مع تحديث بعض القيم
  UserRole copyWith({
    String? id,
    String? name,
    String? displayName,
    String? description,
    List<String>? permissions,
    bool? isCustom,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
    String? updatedBy,
    bool? isDeleted,
  }) {
    return UserRole(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      description: description ?? this.description,
      permissions: permissions ?? this.permissions,
      isCustom: isCustom ?? this.isCustom,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }

  /// التحقق من وجود صلاحية
  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }

  /// التحقق من وجود أي من الصلاحيات
  bool hasAnyPermission(List<String> permissionsList) {
    for (final permission in permissionsList) {
      if (hasPermission(permission)) {
        return true;
      }
    }

    return false;
  }

  /// التحقق من وجود جميع الصلاحيات
  bool hasAllPermissions(List<String> permissionsList) {
    for (final permission in permissionsList) {
      if (!hasPermission(permission)) {
        return false;
      }
    }

    return true;
  }

  /// تحويل دور المستخدم إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'display_name': displayName,
      'description': description,
      'permissions': permissions,
      'is_custom': isCustom ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'created_by': createdBy,
      'updated_by': updatedBy,
      'is_deleted': isDeleted ? 1 : 0,
    };
  }

  /// إنشاء دور مستخدم من Map
  factory UserRole.fromMap(Map<String, dynamic> map) {
    // معالجة الصلاحيات التي قد تكون مخزنة كنص JSON
    List<String> permissionsList = [];

    try {
      if (map['permissions'] is String) {
        // إذا كانت الصلاحيات مخزنة كنص JSON، نقوم بتحويلها
        final permissionsJson = map['permissions'] as String;
        if (permissionsJson.isNotEmpty) {
          try {
            final decoded = jsonDecode(permissionsJson);
            if (decoded is List) {
              permissionsList = List<String>.from(decoded);
            } else if (decoded is String) {
              // في بعض الحالات قد يكون النص JSON عبارة عن نص آخر
              AppLogger.warning('الصلاحيات مخزنة كنص داخل نص JSON: $decoded');
              try {
                final nestedDecoded = jsonDecode(decoded);
                if (nestedDecoded is List) {
                  permissionsList = List<String>.from(nestedDecoded);
                }
              } catch (nestedError) {
                AppLogger.error(
                    'خطأ في تحويل الصلاحيات المتداخلة: $nestedError');
                // إذا فشل التحويل، نستخدم قائمة فارغة
                permissionsList = [];
              }
            }
          } catch (jsonError) {
            AppLogger.error('خطأ في تحويل نص JSON للصلاحيات: $jsonError');
            // إذا فشل التحويل، نستخدم قائمة فارغة
            permissionsList = [];
          }
        }
      } else if (map['permissions'] is List) {
        // إذا كانت الصلاحيات مخزنة كقائمة مباشرة
        try {
          permissionsList = List<String>.from(map['permissions']);
        } catch (castError) {
          AppLogger.error('خطأ في تحويل قائمة الصلاحيات: $castError');
          // محاولة معالجة كل عنصر على حدة
          final rawList = map['permissions'] as List;
          for (var item in rawList) {
            if (item is String) {
              permissionsList.add(item);
            }
          }
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في تحويل الصلاحيات: $e');
      // استخدام قائمة فارغة في حالة الخطأ
      permissionsList = [];
    }

    // معالجة التواريخ
    DateTime createdAtDate;
    try {
      if (map['created_at'] is int) {
        createdAtDate = DateTime.fromMillisecondsSinceEpoch(map['created_at']);
      } else if (map['created_at'] is String) {
        // محاولة تحويل النص إلى تاريخ
        createdAtDate = DateTime.parse(map['created_at']);
      } else {
        createdAtDate = DateTime.now();
      }
    } catch (e) {
      AppLogger.error('خطأ في تحويل تاريخ الإنشاء: $e');
      createdAtDate = DateTime.now();
    }

    DateTime? updatedAtDate;
    if (map['updated_at'] != null) {
      try {
        if (map['updated_at'] is int) {
          updatedAtDate =
              DateTime.fromMillisecondsSinceEpoch(map['updated_at']);
        } else if (map['updated_at'] is String) {
          updatedAtDate = DateTime.parse(map['updated_at']);
        }
      } catch (e) {
        AppLogger.error('خطأ في تحويل تاريخ التحديث: $e');
        updatedAtDate = null;
      }
    }

    return UserRole(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      displayName: map['display_name'] ?? map['name'] ?? '',
      description: map['description'],
      permissions: permissionsList,
      isCustom: map['is_custom'] == 1,
      createdAt: createdAtDate,
      updatedAt: updatedAtDate,
      createdBy: map['created_by'],
      updatedBy: map['updated_by'],
      isDeleted: map['is_deleted'] == 1,
    );
  }

  /// تحويل دور المستخدم إلى JSON
  String toJson() => jsonEncode(toMap());

  /// إنشاء دور مستخدم من JSON
  factory UserRole.fromJson(String source) =>
      UserRole.fromMap(jsonDecode(source));

  /// إنشاء دور مستخدم من دور افتراضي
  factory UserRole.fromDefaultRole(String roleName) {
    if (!RolesSchema.roles.containsKey(roleName)) {
      throw ArgumentError('الدور غير موجود: $roleName');
    }

    // الحصول على وصف الدور إذا كان متاحًا
    String? description;
    if (roleName == 'admin') {
      description = 'مدير النظام مع كامل الصلاحيات';
    } else if (roleName == 'owner') {
      description = 'مالك النظام مع كامل الصلاحيات';
    } else if (roleName == 'manager') {
      description = 'مشرف مع صلاحيات إدارية محدودة';
    } else if (roleName == 'cashier') {
      description = 'كاشير مع صلاحيات نقطة البيع';
    } else if (roleName == 'salesperson') {
      description = 'مندوب مبيعات مع صلاحيات المبيعات';
    } else if (roleName == 'accountant') {
      description = 'محاسب مع صلاحيات مالية';
    } else if (roleName == 'inventory_manager') {
      description = 'مدير مخزون مع صلاحيات المخزون';
    } else if (roleName == 'viewer') {
      description = 'مشاهد مع صلاحيات عرض فقط';
    }

    return UserRole(
      id: roleName,
      name: roleName,
      displayName: RolesSchema.roles[roleName]!,
      description: description,
      permissions: RolesSchema.defaultRolePermissions[roleName] ?? [],
      isCustom: false,
      createdBy: 'system',
      isDeleted: false,
    );
  }

  /// الحصول على جميع الأدوار الافتراضية
  static List<UserRole> getDefaultRoles() {
    return RolesSchema.roles.keys.map((roleName) {
      return UserRole.fromDefaultRole(roleName);
    }).toList();
  }

  /// إضافة صلاحية
  UserRole addPermission(String permission) {
    if (!permissions.contains(permission)) {
      final newPermissions = List<String>.from(permissions)..add(permission);
      return copyWith(permissions: newPermissions);
    }
    return this;
  }

  /// إضافة مجموعة من الصلاحيات
  UserRole addPermissions(List<String> permissionsList) {
    final newPermissions = List<String>.from(permissions);
    for (final permission in permissionsList) {
      if (!newPermissions.contains(permission)) {
        newPermissions.add(permission);
      }
    }
    return copyWith(permissions: newPermissions);
  }

  /// إزالة صلاحية
  UserRole removePermission(String permission) {
    if (permissions.contains(permission)) {
      final newPermissions = List<String>.from(permissions)..remove(permission);
      return copyWith(permissions: newPermissions);
    }
    return this;
  }

  /// إزالة مجموعة من الصلاحيات
  UserRole removePermissions(List<String> permissionsList) {
    final newPermissions = List<String>.from(permissions);
    for (final permission in permissionsList) {
      newPermissions.remove(permission);
    }
    return copyWith(permissions: newPermissions);
  }

  /// مقارنة الأدوار
  bool equals(UserRole other) {
    if (id != other.id ||
        name != other.name ||
        displayName != other.displayName ||
        isCustom != other.isCustom ||
        permissions.length != other.permissions.length) {
      return false;
    }

    for (final permission in permissions) {
      if (!other.permissions.contains(permission)) {
        return false;
      }
    }

    return true;
  }

  /// تحويل كائن ديناميكي إلى UserRole
  /// هذه الدالة تستخدم للتوافق مع الكود القديم
  static UserRole fromRole(dynamic role) {
    if (role == null) {
      throw ArgumentError('الدور غير موجود');
    }

    // إذا كان الكائن من نوع UserRole، نعيده كما هو
    if (role is UserRole) {
      return role;
    }

    // إذا كان الكائن من نوع Map، نستخدم fromMap
    if (role is Map<String, dynamic>) {
      return UserRole.fromMap(role);
    }

    // محاولة استخراج البيانات من الكائن
    try {
      final id = role.id ?? '';
      final name = role.name ?? '';
      final description = role.description;

      // محاولة استخراج الصلاحيات
      List<String> permissions = [];
      if (role.permissions != null) {
        if (role.permissions is List<String>) {
          permissions = role.permissions;
        } else if (role.permissions is List) {
          // محاولة تحويل كل عنصر إلى String
          for (var perm in role.permissions) {
            if (perm is String) {
              permissions.add(perm);
            } else if (perm != null) {
              // محاولة استخراج الكود من كائن Permission
              try {
                final code = perm.code;
                if (code != null && code is String) {
                  permissions.add(code);
                }
              } catch (_) {
                // تجاهل الخطأ
              }
            }
          }
        }
      }

      return UserRole(
        id: id,
        name: name,
        displayName: role.displayName ?? name,
        description: description,
        permissions: permissions,
        isCustom: role.isCustom ?? false,
        createdAt: role.createdAt,
        updatedAt: role.updatedAt,
        createdBy: role.createdBy,
        updatedBy: role.updatedBy,
        isDeleted: role.isDeleted ?? false,
      );
    } catch (e) {
      AppLogger.error('خطأ في تحويل الدور: $e');
      // إنشاء دور افتراضي فارغ
      return UserRole(
        id: '',
        name: '',
        displayName: '',
        permissions: [],
      );
    }
  }

  @override
  String toString() {
    return 'UserRole(id: $id, name: $name, displayName: $displayName, description: $description, permissions: ${permissions.length}, isCustom: $isCustom, isDeleted: $isDeleted)';
  }
}
