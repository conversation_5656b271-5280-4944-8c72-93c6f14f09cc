import 'package:flutter/material.dart';

//import '../../../core/theme/index.dart';

class DateFilterDropdown extends StatefulWidget {
  final Function(DateTimeRange?) onFilterChanged;
  final bool showAllOption;
  final String? initialValue;

  const DateFilterDropdown({
    Key? key,
    required this.onFilterChanged,
    this.showAllOption = true,
    this.initialValue,
  }) : super(key: key);

  @override
  State<DateFilterDropdown> createState() => _DateFilterDropdownState();
}

class _DateFilterDropdownState extends State<DateFilterDropdown> {
  String? _selectedFilter;
  DateTimeRange? _customDateRange;

  @override
  void initState() {
    super.initState();
    _selectedFilter =
        widget.initialValue ?? (widget.showAllOption ? 'all' : 'today');
    _updateDateRange();
  }

  void _updateDateRange() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    switch (_selectedFilter) {
      case 'all':
        widget.onFilterChanged(null);
        break;
      case 'today':
        widget.onFilterChanged(DateTimeRange(
          start: today,
          end: today
              .add(const Duration(days: 1))
              .subtract(const Duration(seconds: 1)),
        ));
        break;
      case 'yesterday':
        final yesterday = today.subtract(const Duration(days: 1));
        widget.onFilterChanged(DateTimeRange(
          start: yesterday,
          end: yesterday
              .add(const Duration(days: 1))
              .subtract(const Duration(seconds: 1)),
        ));
        break;
      case 'this_week':
        // بداية الأسبوع (الأحد)
        final startOfWeek = today.subtract(Duration(days: today.weekday % 7));
        widget.onFilterChanged(DateTimeRange(
          start: startOfWeek,
          end: startOfWeek
              .add(const Duration(days: 7))
              .subtract(const Duration(seconds: 1)),
        ));
        break;
      case 'last_week':
        // بداية الأسبوع الماضي
        final startOfLastWeek =
            today.subtract(Duration(days: today.weekday % 7 + 7));
        widget.onFilterChanged(DateTimeRange(
          start: startOfLastWeek,
          end: startOfLastWeek
              .add(const Duration(days: 7))
              .subtract(const Duration(seconds: 1)),
        ));
        break;
      case 'this_month':
        // بداية الشهر الحالي
        final startOfMonth = DateTime(now.year, now.month, 1);
        final endOfMonth = (now.month < 12)
            ? DateTime(now.year, now.month + 1, 1)
                .subtract(const Duration(seconds: 1))
            : DateTime(now.year + 1, 1, 1).subtract(const Duration(seconds: 1));
        widget.onFilterChanged(DateTimeRange(
          start: startOfMonth,
          end: endOfMonth,
        ));
        break;
      case 'last_month':
        // بداية الشهر الماضي
        final lastMonth = now.month > 1 ? now.month - 1 : 12;
        final year = now.month > 1 ? now.year : now.year - 1;
        final startOfLastMonth = DateTime(year, lastMonth, 1);
        final endOfLastMonth = DateTime(now.year, now.month, 1)
            .subtract(const Duration(seconds: 1));
        widget.onFilterChanged(DateTimeRange(
          start: startOfLastMonth,
          end: endOfLastMonth,
        ));
        break;
      case 'this_year':
        // بداية السنة الحالية
        final startOfYear = DateTime(now.year, 1, 1);
        final endOfYear =
            DateTime(now.year + 1, 1, 1).subtract(const Duration(seconds: 1));
        widget.onFilterChanged(DateTimeRange(
          start: startOfYear,
          end: endOfYear,
        ));
        break;
      case 'last_year':
        // بداية السنة الماضية
        final startOfLastYear = DateTime(now.year - 1, 1, 1);
        final endOfLastYear =
            DateTime(now.year, 1, 1).subtract(const Duration(seconds: 1));
        widget.onFilterChanged(DateTimeRange(
          start: startOfLastYear,
          end: endOfLastYear,
        ));
        break;
      case 'custom':
        widget.onFilterChanged(_customDateRange);
        break;
    }
  }

  Future<void> _selectCustomDateRange() async {
    final initialDateRange = _customDateRange ??
        DateTimeRange(
          start: DateTime.now().subtract(const Duration(days: 7)),
          end: DateTime.now(),
        );

    final newDateRange = await showDateRangePicker(
      context: context,
      initialDateRange: initialDateRange,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context)
                      .colorScheme
                      .primary, // Use primary from current theme
                  // You might want to adjust other colors for the date picker theme if needed
                  // For example, onPrimary if the default doesn't contrast well with the new primary
                  onPrimary: Theme.of(context).colorScheme.onPrimary,
                  surface: Theme.of(context).colorScheme.surface,
                  onSurface: Theme.of(context).colorScheme.onSurface,
                ),
          ),
          child: child!,
        );
      },
    );

    if (newDateRange != null) {
      setState(() {
        _customDateRange = newDateRange;
        _selectedFilter = 'custom';
      });
      widget.onFilterChanged(newDateRange);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _selectedFilter,
                isExpanded: true,
                icon: const Icon(Icons.arrow_drop_down),
                items: [
                  if (widget.showAllOption)
                    const DropdownMenuItem<String>(
                      value: 'all',
                      child: Text('الكل'),
                    ),
                  const DropdownMenuItem<String>(
                    value: 'today',
                    child: Text('اليوم'),
                  ),
                  const DropdownMenuItem<String>(
                    value: 'yesterday',
                    child: Text('الأمس'),
                  ),
                  const DropdownMenuItem<String>(
                    value: 'this_week',
                    child: Text('هذا الأسبوع'),
                  ),
                  const DropdownMenuItem<String>(
                    value: 'last_week',
                    child: Text('الأسبوع الماضي'),
                  ),
                  const DropdownMenuItem<String>(
                    value: 'this_month',
                    child: Text('هذا الشهر'),
                  ),
                  const DropdownMenuItem<String>(
                    value: 'last_month',
                    child: Text('الشهر الماضي'),
                  ),
                  const DropdownMenuItem<String>(
                    value: 'this_year',
                    child: Text('هذه السنة'),
                  ),
                  const DropdownMenuItem<String>(
                    value: 'last_year',
                    child: Text('السنة الماضية'),
                  ),
                  const DropdownMenuItem<String>(
                    value: 'custom',
                    child: Text('مخصص'),
                  ),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedFilter = value;
                  });
                  if (value == 'custom') {
                    _selectCustomDateRange();
                  } else {
                    _updateDateRange();
                  }
                },
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        IconButton(
          icon: const Icon(Icons.calendar_today),
          tooltip: 'اختيار تاريخ مخصص',
          onPressed: _selectCustomDateRange,
        ),
      ],
    );
  }
}
