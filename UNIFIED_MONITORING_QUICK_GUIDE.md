# 🚀 دليل سريع - نظام المراقبة الموحد

## 📱 الاستخدام السريع

### 1. **عرض شاشة المراقبة**
```dart
import 'package:tajer_plus/features/unified_monitoring/index.dart';

// في أي مكان في التطبيق
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const UnifiedMonitoringScreen(),
  ),
);
```

### 2. **تسجيل خطأ**
```dart
import 'package:tajer_plus/core/utils/error_tracker.dart';

try {
  // كود قد يسبب خطأ
  await riskyOperation();
} catch (e, stackTrace) {
  ErrorTracker.captureError(
    'فشل في العملية الخطيرة',
    error: e,
    stackTrace: stackTrace,
    context: {
      'operation': 'riskyOperation',
      'userId': currentUser.id,
      'timestamp': DateTime.now().toIso8601String(),
    },
  );
}
```

### 3. **تسجيل نشاط مستخدم**
```dart
// عند إضافة منتج جديد
await ErrorTracker.logActivity(
  userId: user.id,
  userName: user.name,
  action: 'إضافة منتج',
  module: 'المنتجات',
  details: 'تم إضافة منتج: ${product.name}',
);

// عند تسجيل دخول
await ErrorTracker.logActivity(
  userId: user.id,
  userName: user.name,
  action: 'تسجيل دخول',
  module: 'المصادقة',
  details: 'تسجيل دخول ناجح من IP: ${userIP}',
);
```

## 🔧 الوظائف المتاحة

### **تتبع الأخطاء**
```dart
// تسجيل خطأ مع سياق
ErrorTracker.captureError(message, error: e, stackTrace: st, context: {});

// تسجيل خطأ بسيط
ErrorTracker.trackError(error, stackTrace, 'source');

// الحصول على الأخطاء الحديثة
List<ErrorRecord> errors = ErrorTracker.getRecentErrors();

// الحصول على إحصائيات الأخطاء
Map<String, dynamic> stats = ErrorTracker.getErrorStats();

// مسح سجل الأخطاء
ErrorTracker.clearHistory();
```

### **تسجيل النشاطات**
```dart
// تسجيل نشاط
await ErrorTracker.logActivity(
  userId: 'user123',
  userName: 'أحمد محمد',
  action: 'تعديل',
  module: 'العملاء',
  details: 'تم تعديل بيانات العميل',
);

// الحصول على النشاطات
List<dynamic> activities = await ErrorTracker.getActivities(
  startDate: DateTime.now().subtract(Duration(days: 7)),
  endDate: DateTime.now(),
  userId: 'user123',
  action: 'إضافة',
  module: 'المنتجات',
);

// تنظيف النشاطات القديمة (أكثر من 30 يوم)
bool success = await ErrorTracker.cleanupOldActivities(30);
```

## 🎨 أمثلة عملية

### **في خدمة المنتجات**
```dart
class ProductService {
  Future<bool> addProduct(Product product) async {
    try {
      // إضافة المنتج
      await database.insert('products', product.toMap());
      
      // تسجيل النشاط
      await ErrorTracker.logActivity(
        userId: currentUser.id,
        userName: currentUser.name,
        action: 'إضافة منتج',
        module: 'المنتجات',
        details: 'تم إضافة منتج: ${product.name}',
      );
      
      return true;
    } catch (e, stackTrace) {
      // تسجيل الخطأ
      ErrorTracker.captureError(
        'فشل في إضافة منتج',
        error: e,
        stackTrace: stackTrace,
        context: {
          'productName': product.name,
          'productId': product.id,
          'userId': currentUser.id,
        },
      );
      return false;
    }
  }
}
```

### **في خدمة المصادقة**
```dart
class AuthService {
  Future<User?> login(String username, String password) async {
    try {
      final user = await authenticate(username, password);
      
      if (user != null) {
        // تسجيل نشاط تسجيل دخول ناجح
        await ErrorTracker.logActivity(
          userId: user.id,
          userName: user.name,
          action: 'تسجيل دخول',
          module: 'المصادقة',
          details: 'تسجيل دخول ناجح',
        );
      }
      
      return user;
    } catch (e, stackTrace) {
      // تسجيل خطأ في المصادقة
      ErrorTracker.captureError(
        'فشل في تسجيل الدخول',
        error: e,
        stackTrace: stackTrace,
        context: {
          'username': username,
          'attemptTime': DateTime.now().toIso8601String(),
        },
      );
      return null;
    }
  }
}
```

## 📊 مراقبة الأداء

### **فحص دوري للأخطاء**
```dart
void checkSystemHealth() {
  final stats = ErrorTracker.getErrorStats();
  
  if (stats['recentErrorRate'] > 5.0) {
    // معدل أخطاء عالي - إرسال تنبيه
    sendAlert('معدل أخطاء عالي: ${stats['recentErrorRate']}/دقيقة');
  }
  
  if (stats['totalErrors'] > 50) {
    // عدد أخطاء كبير - تنظيف السجل
    ErrorTracker.clearHistory();
  }
}
```

### **تنظيف دوري للبيانات**
```dart
void dailyCleanup() async {
  // تنظيف النشاطات الأقدم من 30 يوم
  await ErrorTracker.cleanupOldActivities(30);
  
  // إذا كان عدد الأخطاء كبير، احتفظ بالأحدث فقط
  final errors = ErrorTracker.getRecentErrors();
  if (errors.length > 80) {
    ErrorTracker.clearHistory();
  }
}
```

## ⚠️ نصائح مهمة

### **✅ افعل**
- استخدم سياق مفيد عند تسجيل الأخطاء
- سجل النشاطات المهمة فقط
- نظف البيانات القديمة دورياً
- استخدم أوصاف واضحة للأخطاء

### **❌ لا تفعل**
- لا تسجل كل عملية كنشاط
- لا تضع معلومات حساسة في السياق
- لا تتجاهل الأخطاء دون تسجيلها
- لا تملأ السجل بأخطاء غير مهمة

## 🔗 روابط مفيدة

- **الكود الأساسي**: `lib/core/utils/error_tracker.dart`
- **الشاشة الموحدة**: `lib/features/unified_monitoring/screens/unified_monitoring_screen.dart`
- **الاختبارات**: `test/unified_monitoring_system_test.dart`
- **التوثيق الكامل**: `lib/features/unified_monitoring/README.md`

---

**💡 نصيحة**: استخدم النظام الموحد في جميع الميزات الجديدة لضمان التتبع الشامل والمراقبة الفعالة!
