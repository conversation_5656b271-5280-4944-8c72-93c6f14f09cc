import 'package:flutter/material.dart';
import '../theme/index.dart';

/// مكونات واجهة المستخدم المحسنة باستخدام النظام الذكي للثيمات
/// 🎨 مكونات ذكية تتكيف مع الثيم الحالي
/// 🌈 تدرجات لونية متطورة وجميلة
/// ♿ ضمان إمكانية الوصول والتباين المناسب
/// 🎭 انتقالات سلسة وحركات جميلة
class EnhancedUIComponents {
  EnhancedUIComponents._();

  // ========== البطاقات المحسنة ==========

  /// بطاقة ذكية مع تدرج لوني وظلال متطورة
  static Widget intelligentCard({
    required Widget child,
    required BuildContext context,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double? elevation,
    Color? customColor,
    GradientStyle gradientStyle = GradientStyle.subtle,
    bool enableHoverEffect = true,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          onTap: enableHoverEffect ? () {} : null,
          child: Container(
            padding: padding ?? AppDimensions.cardPadding,
            decoration: IntelligentThemeEngine.createIntelligentBackground(
              primaryColor: primaryColor,
              isDark: isDark,
              style: gradientStyle,
            ),
            child: child,
          ),
        ),
      ),
    );
  }

  /// بطاقة إحصائيات متطورة
  static Widget statsCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required BuildContext context,
    Color? customColor,
    VoidCallback? onTap,
    bool showTrend = false,
    double? trendValue,
    bool isPositiveTrend = true,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;
    final isDark = theme.brightness == Brightness.dark;
    final palette = AdvancedColorSystem.generateHarmoniousPalette(primaryColor);

    return Container(
      height: 140,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          child: Container(
            decoration: BoxDecoration(
              gradient: AdvancedColorSystem.createIntelligentGradient(
                primaryColor,
                style: GradientStyle.vibrant,
              ),
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              boxShadow: AdvancedColorSystem.createIntelligentShadow(
                primaryColor,
                intensity: ShadowIntensity.medium,
                isDarkMode: isDark,
              ),
            ),
            padding: AppDimensions.cardPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // الصف العلوي
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        icon,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    if (showTrend && trendValue != null)
                      _buildTrendIndicator(trendValue, isPositiveTrend),
                  ],
                ),
                
                // الصف السفلي
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      value,
                      style: AppTypography.lightTextTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // ========== الأزرار المحسنة ==========

  /// زر ذكي مع تدرج لوني وتأثيرات بصرية
  static Widget intelligentButton({
    required String text,
    required VoidCallback onPressed,
    required BuildContext context,
    IconData? icon,
    Color? customColor,
    ButtonStyle buttonStyle = ButtonStyle.elevated,
    ButtonSize size = ButtonSize.medium,
    bool isLoading = false,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;
    final isDark = theme.brightness == Brightness.dark;

    Widget buttonChild = isLoading
        ? const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          )
        : Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(icon, size: _getIconSize(size)),
                const SizedBox(width: 8),
              ],
              Text(text),
            ],
          );

    switch (buttonStyle) {
      case ButtonStyle.elevated:
        return ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryColor,
            foregroundColor: Colors.white,
            padding: _getButtonPadding(size),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
            ),
            elevation: 4,
            shadowColor: primaryColor.withValues(alpha: 0.3),
          ),
          child: buttonChild,
        );
      
      case ButtonStyle.outlined:
        return OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: OutlinedButton.styleFrom(
            foregroundColor: primaryColor,
            side: BorderSide(color: primaryColor, width: 2),
            padding: _getButtonPadding(size),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
            ),
          ),
          child: buttonChild,
        );
      
      case ButtonStyle.text:
        return TextButton(
          onPressed: isLoading ? null : onPressed,
          style: TextButton.styleFrom(
            foregroundColor: primaryColor,
            padding: _getButtonPadding(size),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
            ),
          ),
          child: buttonChild,
        );
    }
  }

  /// زر عائم ذكي
  static Widget intelligentFloatingActionButton({
    required VoidCallback onPressed,
    required IconData icon,
    required BuildContext context,
    Color? customColor,
    String? tooltip,
    bool mini = false,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;

    return FloatingActionButton(
      onPressed: onPressed,
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      tooltip: tooltip,
      mini: mini,
      elevation: 6,
      child: Icon(icon),
    );
  }

  // ========== حقول الإدخال المحسنة ==========

  /// حقل إدخال ذكي مع تصميم متطور
  static Widget intelligentTextField({
    required String label,
    required BuildContext context,
    TextEditingController? controller,
    String? hintText,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixIconTap,
    bool obscureText = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    Color? customColor,
    bool enabled = true,
  }) {
    final theme = Theme.of(context);
    final primaryColor = customColor ?? theme.colorScheme.primary;
    final isDark = theme.brightness == Brightness.dark;

    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      validator: validator,
      enabled: enabled,
      style: theme.textTheme.bodyLarge,
      decoration: InputDecoration(
        labelText: label,
        hintText: hintText,
        prefixIcon: prefixIcon != null ? Icon(prefixIcon, color: primaryColor) : null,
        suffixIcon: suffixIcon != null 
            ? IconButton(
                icon: Icon(suffixIcon, color: primaryColor),
                onPressed: onSuffixIconTap,
              )
            : null,
        filled: true,
        fillColor: isDark 
            ? primaryColor.withValues(alpha: 0.05)
            : primaryColor.withValues(alpha: 0.03),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          borderSide: BorderSide(color: primaryColor.withValues(alpha: 0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          borderSide: BorderSide(color: primaryColor.withValues(alpha: 0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          borderSide: BorderSide(color: primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          borderSide: BorderSide(color: AppColors.error, width: 2),
        ),
        labelStyle: TextStyle(color: primaryColor),
        hintStyle: TextStyle(color: theme.colorScheme.onSurface.withValues(alpha: 0.6)),
      ),
    );
  }

  // ========== دوال مساعدة خاصة ==========

  static Widget _buildTrendIndicator(double trendValue, bool isPositive) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isPositive ? Icons.trending_up : Icons.trending_down,
            color: Colors.white,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            '${trendValue.abs().toStringAsFixed(1)}%',
            style: AppTypography.lightTextTheme.bodySmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  static EdgeInsetsGeometry _getButtonPadding(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      case ButtonSize.medium:
        return const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
      case ButtonSize.large:
        return const EdgeInsets.symmetric(horizontal: 32, vertical: 16);
    }
  }

  static double _getIconSize(ButtonSize size) {
    switch (size) {
      case ButtonSize.small:
        return 16;
      case ButtonSize.medium:
        return 20;
      case ButtonSize.large:
        return 24;
    }
  }
}

// ========== التعدادات ==========

enum ButtonStyle {
  elevated,
  outlined,
  text,
}

enum ButtonSize {
  small,
  medium,
  large,
}
