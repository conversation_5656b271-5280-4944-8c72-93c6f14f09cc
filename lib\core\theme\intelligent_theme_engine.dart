import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'advanced_color_system.dart';
import 'app_colors.dart';
import 'app_dimensions.dart';
import 'app_typography.dart';

/// محرك الثيمات الذكي - نظام متطور لإدارة الثيمات
/// 🧠 ذكاء اصطناعي لاختيار الألوان والتصميم
/// 🎨 تطبيق أفضل ممارسات Material Design 3
/// ♿ ضمان إمكانية الوصول والتباين المناسب
/// 🌙 دعم متقدم للوضع الليلي والنهاري
/// 🎭 انتقالات سلسة بين الثيمات
class IntelligentThemeEngine {
  IntelligentThemeEngine._();

  static const Duration _transitionDuration = Duration(milliseconds: 300);

  // ========== إنشاء الثيمات الذكية ==========

  /// إنشاء ثيم ذكي كامل من لون أساسي
  static ThemeData createIntelligentTheme({
    required Color primaryColor,
    required Brightness brightness,
    String? fontFamily,
    bool enableAnimations = true,
  }) {
    final colorPalette = AdvancedColorSystem.generateHarmoniousPalette(primaryColor);
    final isDark = brightness == Brightness.dark;
    
    return ThemeData(
      useMaterial3: true,
      brightness: brightness,
      
      // نظام الألوان المتطور
      colorScheme: _createIntelligentColorScheme(colorPalette, isDark),
      
      // الطباعة المتطورة
      textTheme: _createIntelligentTextTheme(isDark, fontFamily),
      
      // مكونات الواجهة
      appBarTheme: _createIntelligentAppBarTheme(colorPalette, isDark),
      cardTheme: _createIntelligentCardTheme(colorPalette, isDark),
      elevatedButtonTheme: _createIntelligentButtonTheme(colorPalette, isDark),
      inputDecorationTheme: _createIntelligentInputTheme(colorPalette, isDark),
      bottomNavigationBarTheme: _createIntelligentBottomNavTheme(colorPalette, isDark),
      
      // الانتقالات والحركات
      pageTransitionsTheme: enableAnimations ? _createIntelligentTransitions() : null,
      
      // إعدادات إضافية
      visualDensity: VisualDensity.adaptivePlatformDensity,
      splashFactory: InkRipple.splashFactory,
    );
  }

  /// إنشاء نظام ألوان ذكي
  static ColorScheme _createIntelligentColorScheme(
    Map<String, Color> palette,
    bool isDark,
  ) {
    final primary = palette['primary']!;
    final primaryDark = palette['primaryDark']!;
    final primaryLight = palette['primaryLight']!;
    final secondary = palette['secondary']!;
    final accent = palette['accent']!;
    final surface = isDark ? palette['surfaceDark']! : palette['surface']!;
    final onSurface = isDark ? palette['onSurfaceDark']! : palette['onSurface']!;

    if (isDark) {
      return ColorScheme.dark(
        primary: primaryLight,
        primaryContainer: primary,
        secondary: secondary,
        secondaryContainer: secondary.withValues(alpha: 0.3),
        tertiary: accent,
        tertiaryContainer: accent.withValues(alpha: 0.3),
        surface: surface,
        surfaceContainerHighest: surface.withValues(alpha: 0.8),
        onPrimary: primaryDark,
        onSecondary: Colors.white,
        onSurface: onSurface,
        error: AppColors.error,
        onError: Colors.white,
        outline: onSurface.withValues(alpha: 0.2),
        shadow: Colors.black.withValues(alpha: 0.3),
      );
    } else {
      return ColorScheme.light(
        primary: primary,
        primaryContainer: primaryLight,
        secondary: secondary,
        secondaryContainer: secondary.withValues(alpha: 0.1),
        tertiary: accent,
        tertiaryContainer: accent.withValues(alpha: 0.1),
        surface: surface,
        surfaceContainerHighest: surface.withValues(alpha: 0.8),
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: onSurface,
        error: AppColors.error,
        onError: Colors.white,
        outline: onSurface.withValues(alpha: 0.2),
        shadow: primary.withValues(alpha: 0.1),
      );
    }
  }

  /// إنشاء نظام طباعة ذكي
  static TextTheme _createIntelligentTextTheme(bool isDark, String? fontFamily) {
    final baseTheme = isDark ? AppTypography.darkTextTheme : AppTypography.lightTextTheme;
    
    if (fontFamily != null) {
      return baseTheme.apply(fontFamily: fontFamily);
    }
    
    return baseTheme;
  }

  /// إنشاء ثيم شريط التطبيق الذكي
  static AppBarTheme _createIntelligentAppBarTheme(
    Map<String, Color> palette,
    bool isDark,
  ) {
    final primary = palette['primary']!;
    final onSurface = isDark ? palette['onSurfaceDark']! : palette['onSurface']!;
    
    return AppBarTheme(
      backgroundColor: isDark ? palette['surfaceDark'] : primary,
      foregroundColor: isDark ? onSurface : Colors.white,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: AppTypography.lightTextTheme.titleLarge?.copyWith(
        color: isDark ? onSurface : Colors.white,
        fontWeight: FontWeight.bold,
      ),
      systemOverlayStyle: isDark ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark,
    );
  }

  /// إنشاء ثيم البطاقات الذكي
  static CardTheme _createIntelligentCardTheme(
    Map<String, Color> palette,
    bool isDark,
  ) {
    final surface = isDark ? palette['surfaceDark']! : palette['surface']!;
    final primary = palette['primary']!;
    
    return CardTheme(
      color: surface,
      elevation: 2,
      shadowColor: primary.withValues(alpha: isDark ? 0.1 : 0.15),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
      ),
      margin: const EdgeInsets.all(8),
    );
  }

  /// إنشاء ثيم الأزرار الذكي
  static ElevatedButtonThemeData _createIntelligentButtonTheme(
    Map<String, Color> palette,
    bool isDark,
  ) {
    final primary = palette['primary']!;
    
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primary,
        foregroundColor: Colors.white,
        elevation: 2,
        shadowColor: primary.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// إنشاء ثيم حقول الإدخال الذكي
  static InputDecorationTheme _createIntelligentInputTheme(
    Map<String, Color> palette,
    bool isDark,
  ) {
    final primary = palette['primary']!;
    final onSurface = isDark ? palette['onSurfaceDark']! : palette['onSurface']!;
    
    return InputDecorationTheme(
      filled: true,
      fillColor: isDark 
          ? onSurface.withValues(alpha: 0.05)
          : primary.withValues(alpha: 0.05),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        borderSide: BorderSide(color: onSurface.withValues(alpha: 0.2)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        borderSide: BorderSide(color: onSurface.withValues(alpha: 0.2)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        borderSide: BorderSide(color: primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        borderSide: BorderSide(color: AppColors.error, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }

  /// إنشاء ثيم شريط التنقل السفلي الذكي
  static BottomNavigationBarThemeData _createIntelligentBottomNavTheme(
    Map<String, Color> palette,
    bool isDark,
  ) {
    final primary = palette['primary']!;
    final surface = isDark ? palette['surfaceDark']! : palette['surface']!;
    final onSurface = isDark ? palette['onSurfaceDark']! : palette['onSurface']!;
    
    return BottomNavigationBarThemeData(
      backgroundColor: surface,
      selectedItemColor: primary,
      unselectedItemColor: onSurface.withValues(alpha: 0.6),
      type: BottomNavigationBarType.fixed,
      elevation: 8,
      selectedLabelStyle: AppTypography.lightTextTheme.bodySmall?.copyWith(
        fontWeight: FontWeight.w600,
      ),
      unselectedLabelStyle: AppTypography.lightTextTheme.bodySmall,
    );
  }

  /// إنشاء انتقالات ذكية
  static PageTransitionsTheme _createIntelligentTransitions() {
    return const PageTransitionsTheme(
      builders: {
        TargetPlatform.android: CupertinoPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        TargetPlatform.macOS: CupertinoPageTransitionsBuilder(),
        TargetPlatform.windows: FadeUpwardsPageTransitionsBuilder(),
        TargetPlatform.linux: FadeUpwardsPageTransitionsBuilder(),
      },
    );
  }

  // ========== دوال مساعدة للثيمات ==========

  /// تطبيق ثيم ذكي على ودجت
  static Widget applyIntelligentTheme({
    required Widget child,
    required Color primaryColor,
    required Brightness brightness,
    bool enableAnimations = true,
  }) {
    final theme = createIntelligentTheme(
      primaryColor: primaryColor,
      brightness: brightness,
      enableAnimations: enableAnimations,
    );

    return AnimatedTheme(
      duration: _transitionDuration,
      data: theme,
      child: child,
    );
  }

  /// إنشاء تدرج ذكي للخلفيات
  static BoxDecoration createIntelligentBackground({
    required Color primaryColor,
    required bool isDark,
    GradientStyle style = GradientStyle.subtle,
  }) {
    final gradient = AdvancedColorSystem.createIntelligentGradient(
      primaryColor,
      style: style,
    );

    return BoxDecoration(
      gradient: gradient,
      boxShadow: AdvancedColorSystem.createIntelligentShadow(
        primaryColor,
        intensity: ShadowIntensity.subtle,
        isDarkMode: isDark,
      ),
    );
  }

  /// إنشاء بطاقة ذكية
  static Widget createIntelligentCard({
    required Widget child,
    required Color primaryColor,
    required bool isDark,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) {
    final palette = AdvancedColorSystem.generateHarmoniousPalette(primaryColor);
    final surface = isDark ? palette['surfaceDark']! : palette['surface']!;

    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: surface,
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        boxShadow: AdvancedColorSystem.createIntelligentShadow(
          primaryColor,
          intensity: ShadowIntensity.medium,
          isDarkMode: isDark,
        ),
      ),
      child: child,
    );
  }

  /// تحليل الثيم الحالي
  static ThemeAnalysis analyzeCurrentTheme(ThemeData theme) {
    final primaryColor = theme.colorScheme.primary;
    final analysis = AdvancedColorSystem.analyzeColor(primaryColor);
    
    return ThemeAnalysis(
      primaryColor: primaryColor,
      brightness: theme.brightness,
      colorAnalysis: analysis,
      isAccessible: analysis.accessibility == AccessibilityLevel.AAA,
      useMaterial3: theme.useMaterial3,
    );
  }
}

// ========== كلاسات مساعدة ==========

class ThemeAnalysis {
  final Color primaryColor;
  final Brightness brightness;
  final ColorAnalysis colorAnalysis;
  final bool isAccessible;
  final bool useMaterial3;

  const ThemeAnalysis({
    required this.primaryColor,
    required this.brightness,
    required this.colorAnalysis,
    required this.isAccessible,
    required this.useMaterial3,
  });

  @override
  String toString() {
    return 'ThemeAnalysis(primaryColor: $primaryColor, '
           'brightness: $brightness, '
           'isAccessible: $isAccessible, '
           'colorAnalysis: $colorAnalysis)';
  }
}
