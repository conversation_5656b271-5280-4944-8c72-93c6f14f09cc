import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/index.dart';
import '../../../core/widgets/enhanced_ui_components.dart';

/// شاشة تفاصيل الثيم - عرض شامل لجميع العناصر والطبقات
/// 🎨 عرض جميع مكونات الثيم بالتفصيل
/// 🔍 فحص التوافق والإمكانية الوصول
/// 📊 إحصائيات وتحليل الثيم
/// 🎭 معاينة جميع الحالات والتفاعلات
class ThemeDetailsScreen extends StatefulWidget {
  const ThemeDetailsScreen({Key? key}) : super(key: key);

  @override
  State<ThemeDetailsScreen> createState() => _ThemeDetailsScreenState();
}

class _ThemeDetailsScreenState extends State<ThemeDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ThemeManager _themeManager;
  Map<String, dynamic>? _themeInfo;
  ColorLayerSystem? _colorLayers;
  TextColorPalette? _textColors;
  ButtonColorPalette? _buttonColors;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadThemeDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadThemeDetails() {
    _themeManager = Provider.of<ThemeManager>(context, listen: false);
    _themeInfo = _themeManager.getCurrentThemeInfo();
    _colorLayers = _themeManager.createCurrentColorLayers(
      isDarkMode: _themeManager.isDarkMode,
    );
    _textColors = _themeManager.createCurrentTextColors(
      backgroundColor: _colorLayers!.backgroundLayer.primary,
      isDarkMode: _themeManager.isDarkMode,
    );
    _buttonColors = _themeManager.createCurrentButtonColors(
      backgroundColor: _colorLayers!.backgroundLayer.primary,
      isDarkMode: _themeManager.isDarkMode,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الثيم'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.palette), text: 'الألوان'),
            Tab(icon: Icon(Icons.text_fields), text: 'النصوص'),
            Tab(icon: Icon(Icons.smart_button), text: 'المكونات'),
            Tab(icon: Icon(Icons.analytics), text: 'التحليل'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildColorsTab(),
          _buildTextTab(),
          _buildComponentsTab(),
          _buildAnalysisTab(),
        ],
      ),
    );
  }

  Widget _buildColorsTab() {
    if (_colorLayers == null) return const Center(child: CircularProgressIndicator());

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildColorLayerSection('طبقة الخلفية', _colorLayers!.backgroundLayer),
          const SizedBox(height: 24),
          _buildColorLayerSection('طبقة السطح', _colorLayers!.surfaceLayer),
          const SizedBox(height: 24),
          _buildColorLayerSection('طبقة المحتوى', _colorLayers!.contentLayer),
          const SizedBox(height: 24),
          _buildColorLayerSection('طبقة التفاعل', _colorLayers!.interactionLayer),
          const SizedBox(height: 24),
          _buildColorLayerSection('طبقة التأكيد', _colorLayers!.emphasisLayer),
          const SizedBox(height: 24),
          _buildColorLayerSection('طبقة الحالة', _colorLayers!.statusLayer),
        ],
      ),
    );
  }

  Widget _buildColorLayerSection(String title, ColorLayer layer) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(child: _buildColorCard('أساسي', layer.primary)),
            const SizedBox(width: 8),
            Expanded(child: _buildColorCard('ثانوي', layer.secondary)),
            const SizedBox(width: 8),
            Expanded(child: _buildColorCard('ثالثي', layer.tertiary)),
          ],
        ),
      ],
    );
  }

  Widget _buildColorCard(String label, Color color) {
    final luminance = color.computeLuminance();
    final textColor = luminance > 0.5 ? Colors.black : Colors.white;
    
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '#${color.value.toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}',
            style: TextStyle(
              color: textColor.withValues(alpha: 0.8),
              fontSize: 10,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextTab() {
    if (_textColors == null) return const Center(child: CircularProgressIndicator());

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextColorSection('النصوص الأساسية', [
            ('نص أساسي', _textColors!.primary),
            ('نص ثانوي', _textColors!.secondary),
            ('نص مساعد', _textColors!.tertiary),
            ('نص معطل', _textColors!.disabled),
          ]),
          const SizedBox(height: 24),
          _buildTextColorSection('النصوص الخاصة', [
            ('رابط', _textColors!.link),
            ('خطأ', _textColors!.error),
            ('نجاح', _textColors!.success),
            ('تحذير', _textColors!.warning),
          ]),
          const SizedBox(height: 24),
          _buildTextSamplesSection(),
        ],
      ),
    );
  }

  Widget _buildTextColorSection(String title, List<(String, Color)> colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...colors.map((colorData) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildTextColorRow(colorData.$1, colorData.$2),
        )),
      ],
    );
  }

  Widget _buildTextColorRow(String label, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '#${color.value.toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontFamily: 'monospace',
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          Text(
            'نص تجريبي',
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextSamplesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'عينات النصوص',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('عنوان كبير', style: Theme.of(context).textTheme.headlineLarge),
              const SizedBox(height: 8),
              Text('عنوان متوسط', style: Theme.of(context).textTheme.headlineMedium),
              const SizedBox(height: 8),
              Text('عنوان صغير', style: Theme.of(context).textTheme.headlineSmall),
              const SizedBox(height: 8),
              Text('نص أساسي', style: Theme.of(context).textTheme.bodyLarge),
              const SizedBox(height: 8),
              Text('نص ثانوي', style: Theme.of(context).textTheme.bodyMedium),
              const SizedBox(height: 8),
              Text('نص صغير', style: Theme.of(context).textTheme.bodySmall),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildComponentsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildButtonsSection(),
          const SizedBox(height: 24),
          _buildInputsSection(),
          const SizedBox(height: 24),
          _buildCardsSection(),
        ],
      ),
    );
  }

  Widget _buildButtonsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأزرار',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            EnhancedUIComponents.intelligentButton(
              text: 'زر أساسي',
              onPressed: () {},
              context: context,
              icon: Icons.check,
            ),
            EnhancedUIComponents.intelligentButton(
              text: 'زر ثانوي',
              onPressed: () {},
              context: context,
              buttonStyle: ButtonStyle.outlined,
              icon: Icons.edit,
            ),
            EnhancedUIComponents.intelligentButton(
              text: 'زر نصي',
              onPressed: () {},
              context: context,
              buttonStyle: ButtonStyle.text,
              icon: Icons.info,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInputsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'حقول الإدخال',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        EnhancedUIComponents.intelligentTextField(
          label: 'حقل نصي عادي',
          context: context,
          hintText: 'أدخل النص هنا',
          prefixIcon: Icons.text_fields,
        ),
        const SizedBox(height: 12),
        EnhancedUIComponents.intelligentTextField(
          label: 'حقل كلمة المرور',
          context: context,
          hintText: 'أدخل كلمة المرور',
          prefixIcon: Icons.lock,
          suffixIcon: Icons.visibility,
          obscureText: true,
        ),
      ],
    );
  }

  Widget _buildCardsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'البطاقات',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        EnhancedUIComponents.intelligentCard(
          context: context,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'بطاقة ذكية',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'هذه بطاقة ذكية تتكيف مع الثيم الحالي وتضمن التوافق اللوني المثالي.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAnalysisTab() {
    if (_themeInfo == null) return const Center(child: CircularProgressIndicator());

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildThemeInfoCard(),
          const SizedBox(height: 24),
          _buildColorAnalysisCard(),
          const SizedBox(height: 24),
          _buildAccessibilityCard(),
        ],
      ),
    );
  }

  Widget _buildThemeInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الثيم',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('اسم الثيم', _themeInfo!['themeName']),
            _buildInfoRow('مفتاح الثيم', _themeInfo!['themeKey']),
            _buildInfoRow('اللون الأساسي', _themeInfo!['primaryColor']),
            _buildInfoRow('الوضع الداكن', _themeInfo!['isDarkMode'] ? 'مفعل' : 'معطل'),
            _buildInfoRow('الفئة', _themeInfo!['category']),
            _buildInfoRow('المزاج', _themeInfo!['mood']),
          ],
        ),
      ),
    );
  }

  Widget _buildColorAnalysisCard() {
    final analysis = _themeInfo!['colorAnalysis'] as Map<String, dynamic>;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تحليل الألوان',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('درجة اللون', '${analysis['hue'].toStringAsFixed(1)}°'),
            _buildInfoRow('التشبع', '${(analysis['saturation'] * 100).toStringAsFixed(1)}%'),
            _buildInfoRow('الإضاءة', '${(analysis['lightness'] * 100).toStringAsFixed(1)}%'),
            _buildInfoRow('اللمعان', '${(analysis['luminance'] * 100).toStringAsFixed(1)}%'),
            _buildInfoRow('درجة الحرارة', analysis['temperature']),
            _buildInfoRow('المزاج', analysis['mood']),
          ],
        ),
      ),
    );
  }

  Widget _buildAccessibilityCard() {
    final analysis = _themeInfo!['colorAnalysis'] as Map<String, dynamic>;
    final isAccessible = _themeInfo!['isAccessible'] as bool;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isAccessible ? Icons.check_circle : Icons.warning,
                  color: isAccessible ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'إمكانية الوصول',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildInfoRow('مستوى الوصول', analysis['accessibility']),
            _buildInfoRow('متوافق مع WCAG', isAccessible ? 'نعم' : 'لا'),
            const SizedBox(height: 8),
            Text(
              isAccessible 
                  ? 'هذا الثيم يلبي معايير إمكانية الوصول ويوفر تباين مناسب للجميع.'
                  : 'قد يحتاج هذا الثيم إلى تحسينات في التباين لضمان إمكانية الوصول.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isAccessible ? Colors.green : Colors.orange,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
