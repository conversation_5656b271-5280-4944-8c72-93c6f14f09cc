import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/index.dart';
import '../../../core/widgets/enhanced_ui_components.dart';

/// شاشة تفاصيل الثيم - عرض شامل لجميع العناصر والطبقات
/// 🎨 عرض جميع مكونات الثيم بالتفصيل
/// 🔍 فحص التوافق والإمكانية الوصول
/// 📊 إحصائيات وتحليل الثيم
/// 🎭 معاينة جميع الحالات والتفاعلات
class ThemeDetailsScreen extends StatefulWidget {
  const ThemeDetailsScreen({Key? key}) : super(key: key);

  @override
  State<ThemeDetailsScreen> createState() => _ThemeDetailsScreenState();
}

class _ThemeDetailsScreenState extends State<ThemeDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ThemeManager _themeManager;
  Map<String, dynamic>? _themeInfo;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadThemeDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadThemeDetails() {
    _themeManager = Provider.of<ThemeManager>(context, listen: false);
    _themeInfo = _themeManager.getCurrentThemeInfo();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الثيم'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.palette), text: 'الألوان'),
            Tab(icon: Icon(Icons.text_fields), text: 'النصوص'),
            Tab(icon: Icon(Icons.smart_button), text: 'المكونات'),
            Tab(icon: Icon(Icons.analytics), text: 'التحليل'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildColorsTab(),
          _buildTextTab(),
          _buildComponentsTab(),
          _buildAnalysisTab(),
        ],
      ),
    );
  }

  Widget _buildColorsTab() {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSimpleColorSection('الألوان الأساسية', [
            ('أساسي', theme.colorScheme.primary),
            ('ثانوي', theme.colorScheme.secondary),
            ('سطح', theme.colorScheme.surface),
            ('خلفية', theme.colorScheme.surface),
          ]),
          const SizedBox(height: 24),
          _buildSimpleColorSection('ألوان الحالة', [
            ('خطأ', theme.colorScheme.error),
            ('نجاح', AppColors.success),
            ('تحذير', AppColors.warning),
            ('معلومات', AppColors.info),
          ]),
        ],
      ),
    );
  }

  Widget _buildSimpleColorSection(String title, List<(String, Color)> colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: colors
              .map((colorData) => _buildColorCard(colorData.$1, colorData.$2))
              .toList(),
        ),
      ],
    );
  }

  Widget _buildColorCard(String label, Color color) {
    final luminance = color.computeLuminance();
    final textColor = luminance > 0.5 ? Colors.black : Colors.white;

    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '#${color.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}',
            style: TextStyle(
              color: textColor.withValues(alpha: 0.8),
              fontSize: 10,
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextTab() {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextColorSection('النصوص الأساسية', [
            ('نص أساسي', theme.colorScheme.onSurface),
            ('نص ثانوي', theme.colorScheme.onSurface.withValues(alpha: 0.7)),
            ('نص مساعد', theme.colorScheme.onSurface.withValues(alpha: 0.5)),
            ('نص معطل', theme.colorScheme.onSurface.withValues(alpha: 0.3)),
          ]),
          const SizedBox(height: 24),
          _buildTextColorSection('النصوص الخاصة', [
            ('رابط', AppColors.info),
            ('خطأ', theme.colorScheme.error),
            ('نجاح', AppColors.success),
            ('تحذير', AppColors.warning),
          ]),
          const SizedBox(height: 24),
          _buildTextSamplesSection(),
        ],
      ),
    );
  }

  Widget _buildTextColorSection(String title, List<(String, Color)> colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        ...colors.map((colorData) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: _buildTextColorRow(colorData.$1, colorData.$2),
            )),
      ],
    );
  }

  Widget _buildTextColorRow(String label, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: Theme.of(context)
                    .colorScheme
                    .outline
                    .withValues(alpha: 0.3),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                Text(
                  '#${color.toARGB32().toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.7),
                      ),
                ),
              ],
            ),
          ),
          Text(
            'نص تجريبي',
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextSamplesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'عينات النصوص',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
            border: Border.all(
              color:
                  Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('عنوان كبير',
                  style: Theme.of(context).textTheme.headlineLarge),
              const SizedBox(height: 8),
              Text('عنوان متوسط',
                  style: Theme.of(context).textTheme.headlineMedium),
              const SizedBox(height: 8),
              Text('عنوان صغير',
                  style: Theme.of(context).textTheme.headlineSmall),
              const SizedBox(height: 8),
              Text('نص أساسي', style: Theme.of(context).textTheme.bodyLarge),
              const SizedBox(height: 8),
              Text('نص ثانوي', style: Theme.of(context).textTheme.bodyMedium),
              const SizedBox(height: 8),
              Text('نص صغير', style: Theme.of(context).textTheme.bodySmall),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildComponentsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildButtonsSection(),
          const SizedBox(height: 24),
          _buildInputsSection(),
          const SizedBox(height: 24),
          _buildCardsSection(),
        ],
      ),
    );
  }

  Widget _buildButtonsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأزرار',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            ElevatedButton.icon(
              onPressed: () {},
              icon: const Icon(Icons.check),
              label: const Text('زر أساسي'),
            ),
            OutlinedButton.icon(
              onPressed: () {},
              icon: const Icon(Icons.edit),
              label: const Text('زر ثانوي'),
            ),
            TextButton.icon(
              onPressed: () {},
              icon: const Icon(Icons.info),
              label: const Text('زر نصي'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInputsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'حقول الإدخال',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        EnhancedUIComponents.intelligentTextField(
          label: 'حقل نصي عادي',
          context: context,
          hintText: 'أدخل النص هنا',
          prefixIcon: Icons.text_fields,
        ),
        const SizedBox(height: 12),
        EnhancedUIComponents.intelligentTextField(
          label: 'حقل كلمة المرور',
          context: context,
          hintText: 'أدخل كلمة المرور',
          prefixIcon: Icons.lock,
          suffixIcon: Icons.visibility,
          obscureText: true,
        ),
      ],
    );
  }

  Widget _buildCardsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'البطاقات',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 12),
        EnhancedUIComponents.intelligentCard(
          context: context,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'بطاقة ذكية',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                'هذه بطاقة ذكية تتكيف مع الثيم الحالي وتضمن التوافق اللوني المثالي.',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAnalysisTab() {
    if (_themeInfo == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildThemeInfoCard(),
          const SizedBox(height: 24),
          _buildSimpleAnalysisCard(),
        ],
      ),
    );
  }

  Widget _buildThemeInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات الثيم',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('اسم الثيم', _themeInfo!['themeName'] ?? 'غير محدد'),
            _buildInfoRow('مفتاح الثيم', _themeInfo!['themeKey'] ?? 'غير محدد'),
            _buildInfoRow(
                'اللون الأساسي', _themeInfo!['primaryColor'] ?? 'غير محدد'),
            _buildInfoRow('الوضع الداكن',
                _themeInfo!['isDarkMode'] == true ? 'مفعل' : 'معطل'),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleAnalysisCard() {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تحليل الثيم',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
                'نوع الثيم', _themeManager.isDarkMode ? 'داكن' : 'فاتح'),
            _buildInfoRow('اللون الأساسي', _themeManager.colorThemeName),
            _buildInfoRow(
                'الوضع', _themeManager.isSystemMode ? 'تلقائي' : 'يدوي'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }
}
