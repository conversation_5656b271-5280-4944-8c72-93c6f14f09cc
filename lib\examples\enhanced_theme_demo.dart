import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../core/theme/index.dart';

/// شاشة عرض تجريبية للثيمات المحسنة والألوان العصرية
/// تعرض جميع التحسينات الجديدة على نظام الثيمات
class EnhancedThemeDemo extends StatefulWidget {
  const EnhancedThemeDemo({Key? key}) : super(key: key);

  @override
  State<EnhancedThemeDemo> createState() => _EnhancedThemeDemoState();
}

class _EnhancedThemeDemoState extends State<EnhancedThemeDemo> {
  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeManager>(
      builder: (context, themeManager, child) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('عرض الثيمات المحسنة'),
            backgroundColor: DynamicColors.primary,
            foregroundColor: Colors.white,
            actions: [
              IconButton(
                icon: const Icon(Icons.palette),
                onPressed: () => _showThemeSelector(context, themeManager),
              ),
            ],
          ),
          body: SingleChildScrollView(
            padding: AppDimensions.screenPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات الثيم الحالي
                _buildCurrentThemeInfo(themeManager),
                const SizedBox(height: 24),

                // بطاقات الإحصائيات العصرية
                _buildModernStatsCards(),
                const SizedBox(height: 24),

                // أزرار الوصول السريع
                _buildQuickAccessButtons(),
                const SizedBox(height: 24),

                // عرض جميع الألوان المتاحة
                _buildColorPalette(),
              ],
            ),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () => themeManager.toggleTheme(),
            child: Icon(
              themeManager.isDarkMode ? Icons.light_mode : Icons.dark_mode,
            ),
          ),
        );
      },
    );
  }

  /// بناء معلومات الثيم الحالي
  Widget _buildCurrentThemeInfo(ThemeManager themeManager) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            DynamicColors.primary.withValues(alpha: 0.1),
            DynamicColors.primary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        border: Border.all(
          color: DynamicColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: DynamicColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.info_outline,
                  color: DynamicColors.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'الثيم الحالي',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: DynamicColors.textPrimary(context),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('اللون:', themeManager.colorThemeName),
          _buildInfoRow('الوضع:', themeManager.getThemeModeDisplayName()),
          _buildInfoRow('الحالة:', isDarkMode ? 'داكن' : 'فاتح'),
        ],
      ),
    );
  }

  /// بناء صف المعلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
              color: AppColors.lightTextSecondary,
            ),
          ),
          Text(
            value,
            style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: DynamicColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقات الإحصائيات العصرية
  Widget _buildModernStatsCards() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'بطاقات الإحصائيات العصرية',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: DynamicColors.textPrimary(context),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildModernStatsCard(
                title: 'المبيعات',
                value: '25,480 ر.س',
                subtitle: 'هذا الشهر',
                icon: Icons.shopping_cart_outlined,
                gradientColors: [
                  AppColors.moduleSales,
                  AppColors.moduleSales.withValues(alpha: 0.7)
                ],
                isDarkMode: isDarkMode,
                showTrend: true,
                trendValue: 12.5,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildModernStatsCard(
                title: 'الأرباح',
                value: '8,950 ر.س',
                subtitle: 'صافي الربح',
                icon: Icons.trending_up_outlined,
                gradientColors: [
                  AppColors.success,
                  AppColors.success.withValues(alpha: 0.7)
                ],
                isDarkMode: isDarkMode,
                showTrend: true,
                trendValue: 8.3,
                isProfit: true,
                profitValue: 8950,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائيات عصرية
  Widget _buildModernStatsCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required List<Color> gradientColors,
    required bool isDarkMode,
    bool showTrend = false,
    double? trendValue,
    bool isProfit = false,
    double? profitValue,
  }) {
    return Container(
      height: 130,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showMessage('تم النقر على $title'),
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: isDarkMode
                    ? [
                        gradientColors[0].withValues(alpha: 0.3),
                        gradientColors[1].withValues(alpha: 0.1),
                      ]
                    : gradientColors,
              ),
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              boxShadow: [
                BoxShadow(
                  color: gradientColors[0]
                      .withValues(alpha: isDarkMode ? 0.1 : 0.2),
                  blurRadius: 12,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
            padding: AppDimensions.cardPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: Colors.white
                            .withValues(alpha: isDarkMode ? 0.1 : 0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Icon(
                        icon,
                        color: Colors.white,
                        size: 26,
                      ),
                    ),
                    if (showTrend && trendValue != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              trendValue >= 0
                                  ? Icons.trending_up
                                  : Icons.trending_down,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${trendValue.abs().toStringAsFixed(1)}%',
                              style: AppTypography.lightTextTheme.bodySmall
                                  ?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      value,
                      style:
                          AppTypography.lightTextTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: AppTypography.weightBold,
                        fontSize: 20,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            subtitle,
                            style: AppTypography.lightTextTheme.bodySmall
                                ?.copyWith(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (isProfit && profitValue != null)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              profitValue >= 0 ? 'ربح' : 'خسارة',
                              style: AppTypography.lightTextTheme.bodySmall
                                  ?.copyWith(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء أزرار الوصول السريع
  Widget _buildQuickAccessButtons() {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أزرار الوصول السريع العصرية',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: DynamicColors.textPrimary(context),
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isDarkMode ? AppColors.darkSurface : AppColors.lightSurface,
            borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
            boxShadow: [
              BoxShadow(
                color: (isDarkMode ? Colors.black : Colors.grey)
                    .withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: _buildQuickAccessButton(
                  title: 'بيع جديد',
                  icon: Icons.shopping_cart_outlined,
                  color: AppColors.moduleSales,
                  isDarkMode: isDarkMode,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildQuickAccessButton(
                  title: 'إضافة منتج',
                  icon: Icons.inventory_2_outlined,
                  color: AppColors.moduleProducts,
                  isDarkMode: isDarkMode,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildQuickAccessButton(
                  title: 'عميل جديد',
                  icon: Icons.person_add_outlined,
                  color: AppColors.moduleUsers,
                  isDarkMode: isDarkMode,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء زر الوصول السريع
  Widget _buildQuickAccessButton({
    required String title,
    required IconData icon,
    required Color color,
    required bool isDarkMode,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _showMessage('تم النقر على $title'),
        borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withValues(alpha: isDarkMode ? 0.1 : 0.05),
            borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
            border: Border.all(
              color: color.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: isDarkMode ? 0.2 : 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 28,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
                  color: isDarkMode
                      ? AppColors.darkTextPrimary
                      : AppColors.lightTextPrimary,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء لوحة الألوان
  Widget _buildColorPalette() {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'لوحة الألوان المتاحة',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: DynamicColors.textPrimary(context),
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemCount: AppColors.availableThemes.length,
          itemBuilder: (context, index) {
            final themeKey = AppColors.availableThemes.keys.elementAt(index);
            final themeData = AppColors.availableThemes[themeKey]!;

            return _buildColorSample(
              color: themeData['primary'] as Color,
              name: themeData['name'] as String,
            );
          },
        ),
      ],
    );
  }

  /// بناء عينة اللون
  Widget _buildColorSample({
    required Color color,
    required String name,
  }) {
    return GestureDetector(
      onTap: () => _showMessage('لون: $name'),
      child: Container(
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: const Center(
          child: Icon(
            Icons.palette,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }

  /// عرض رسالة
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: DynamicColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
