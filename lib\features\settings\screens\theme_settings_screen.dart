import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/index.dart';

/// شاشة إعدادات الثيم والألوان
/// تسمح للمستخدم بتخصيص مظهر التطبيق
class ThemeSettingsScreen extends StatefulWidget {
  const ThemeSettingsScreen({Key? key}) : super(key: key);

  @override
  State<ThemeSettingsScreen> createState() => _ThemeSettingsScreenState();
}

class _ThemeSettingsScreenState extends State<ThemeSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeManager>(
      builder: (context, themeManager, child) {
        return Scaffold(
          appBar: CustomWidgets.customAppBar(
            title: 'إعدادات المظهر',
            onBackPressed: () => Navigator.pop(context),
          ),
          body: SingleChildScrollView(
            padding: AppDimensions.screenPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // قسم وضع الثيم (فاتح/داكن)
                _buildThemeModeSection(themeManager),

                SizedBox(height: AppDimensions.largeSpacing),

                // قسم الألوان
                _buildColorThemeSection(themeManager),

                SizedBox(height: AppDimensions.largeSpacing),

                // معلومات الثيم الحالي
                _buildCurrentThemeInfo(themeManager),
              ],
            ),
          ),
        );
      },
    );
  }

  /// بناء قسم وضع الثيم
  Widget _buildThemeModeSection(ThemeManager themeManager) {
    return CustomWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.brightness_6,
                color: DynamicColors.primary,
                size: AppDimensions.defaultIconSize,
              ),
              SizedBox(width: AppDimensions.smallSpacing),
              Text(
                'وضع الإضاءة',
                style: AppTypography.lightTextTheme.titleLarge?.copyWith(
                  fontWeight: AppTypography.weightSemiBold,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.mediumSpacing),

          // خيارات وضع الثيم
          _buildThemeModeOption(
            themeManager,
            ThemeMode.light,
            'الوضع الفاتح',
            'استخدام الوضع الفاتح دائماً',
            Icons.light_mode,
          ),

          _buildThemeModeOption(
            themeManager,
            ThemeMode.dark,
            'الوضع الداكن',
            'استخدام الوضع الداكن دائماً',
            Icons.dark_mode,
          ),

          _buildThemeModeOption(
            themeManager,
            ThemeMode.system,
            'حسب النظام',
            'يتبع إعدادات النظام تلقائياً',
            Icons.brightness_auto,
          ),
        ],
      ),
    );
  }

  /// بناء خيار وضع الثيم
  Widget _buildThemeModeOption(
    ThemeManager themeManager,
    ThemeMode mode,
    String title,
    String subtitle,
    IconData icon,
  ) {
    final isSelected = themeManager.themeMode == mode;

    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.smallSpacing),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => themeManager.setThemeMode(mode),
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          child: Container(
            padding: AppDimensions.cardPadding,
            decoration: BoxDecoration(
              color: isSelected
                  ? DynamicColors.primary.withValues(alpha: 0.1)
                  : Colors.transparent,
              borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
              border: Border.all(
                color:
                    isSelected ? DynamicColors.primary : AppColors.lightBorder,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(AppDimensions.smallSpacing),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? DynamicColors.primary
                        : AppColors.lightSurfaceVariant,
                    borderRadius:
                        BorderRadius.circular(AppDimensions.smallRadius),
                  ),
                  child: Icon(
                    icon,
                    color: isSelected
                        ? AppColors.onPrimary
                        : AppColors.lightTextSecondary,
                    size: AppDimensions.smallIconSize,
                  ),
                ),
                SizedBox(width: AppDimensions.defaultSpacing),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTypography.lightTextTheme.bodyLarge?.copyWith(
                          fontWeight: isSelected
                              ? AppTypography.weightMedium
                              : AppTypography.weightRegular,
                          color: isSelected
                              ? DynamicColors.primary
                              : AppColors.lightTextPrimary,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: AppTypography.lightTextTheme.bodySmall?.copyWith(
                          color: AppColors.secondary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: DynamicColors.primary,
                    size: AppDimensions.smallIconSize,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء قسم الألوان
  Widget _buildColorThemeSection(ThemeManager themeManager) {
    return CustomWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.palette,
                color: DynamicColors.primary,
                size: AppDimensions.defaultIconSize,
              ),
              SizedBox(width: AppDimensions.smallSpacing),
              Text(
                'لون التطبيق',
                style: AppTypography.lightTextTheme.titleLarge?.copyWith(
                  fontWeight: AppTypography.weightSemiBold,
                ),
              ),
            ],
          ),

          SizedBox(height: AppDimensions.mediumSpacing),

          // شبكة الألوان المحسنة
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5, // زيادة عدد الأعمدة لاستيعاب الألوان الجديدة
              crossAxisSpacing: AppDimensions.smallSpacing,
              mainAxisSpacing: AppDimensions.smallSpacing,
              childAspectRatio: 0.8, // تعديل النسبة لتبدو أفضل
            ),
            itemCount: AppColors.availableThemes.length,
            itemBuilder: (context, index) {
              final themeKey = AppColors.availableThemes.keys.elementAt(index);
              final themeData = AppColors.availableThemes[themeKey]!;
              final isSelected = themeManager.colorTheme == themeKey;

              return _buildEnhancedColorOption(
                themeManager,
                themeKey,
                themeData['primary'] as Color,
                themeData['name'] as String,
                isSelected,
              );
            },
          ),
        ],
      ),
    );
  }

  /// بناء خيار اللون المحسن مع تصميم عصري
  Widget _buildEnhancedColorOption(
    ThemeManager themeManager,
    String themeKey,
    Color color,
    String name,
    bool isSelected,
  ) {
    return GestureDetector(
      onTap: () => themeManager.setColorTheme(themeKey),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(AppDimensions.defaultRadius),
          border: Border.all(
            color: isSelected ? Colors.white : Colors.transparent,
            width: isSelected ? 3 : 0,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: isSelected ? 0.4 : 0.2),
              blurRadius: isSelected ? 12 : 6,
              offset: Offset(0, isSelected ? 6 : 3),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isSelected)
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 20,
                ),
              )
            else
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
              ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              child: Text(
                _getShortColorName(name),
                style: AppTypography.lightTextTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على اسم مختصر للون
  String _getShortColorName(String fullName) {
    final shortNames = {
      'أحمر تاجر بلس العصري': 'أحمر',
      'أزرق مهني هادئ': 'أزرق',
      'أخضر طبيعي منعش': 'أخضر',
      'بنفسجي أنيق وراقي': 'بنفسجي',
      'برتقالي دافئ وودود': 'برتقالي',
      'تركوازي عصري وهادئ': 'تركوازي',
      'نيلي عميق وأنيق': 'نيلي',
      'وردي جذاب ونابض': 'وردي',
      'زمردي فاخر ومميز': 'زمردي',
      'سماوي منعش وحيوي': 'سماوي',
      'بنفسجي عميق وأنيق': 'بنفسجي',
      'وردي راقي ومميز': 'وردي',
      'كهرماني دافئ وجذاب': 'كهرماني',
      'أخضر ليموني منعش': 'ليموني',
      'أزرق سماوي هادئ': 'سماوي',
    };
    return shortNames[fullName] ?? fullName.split(' ').first;
  }

  /// بناء معلومات الثيم الحالي
  Widget _buildCurrentThemeInfo(ThemeManager themeManager) {
    return CustomWidgets.gradientCard(
      gradientColors: [
        DynamicColors.primary.withValues(alpha: 0.1),
        DynamicColors.primary.withValues(alpha: 0.05),
      ],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: DynamicColors.primary,
                size: AppDimensions.defaultIconSize,
              ),
              SizedBox(width: AppDimensions.smallSpacing),
              Text(
                'الإعدادات الحالية',
                style: AppTypography.lightTextTheme.titleLarge?.copyWith(
                  fontWeight: AppTypography.weightSemiBold,
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.mediumSpacing),
          _buildInfoRow('وضع الإضاءة:', themeManager.getThemeModeDisplayName()),
          _buildInfoRow('لون التطبيق:', themeManager.colorThemeName),
          _buildInfoRow('الحالة:', themeManager.isDarkMode ? 'داكن' : 'فاتح'),
        ],
      ),
    );
  }

  /// بناء صف المعلومات
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimensions.smallSpacing),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
              color: AppColors.lightTextSecondary,
            ),
          ),
          Text(
            value,
            style: AppTypography.lightTextTheme.bodyMedium?.copyWith(
              fontWeight: AppTypography.weightMedium,
              color: DynamicColors.primary,
            ),
          ),
        ],
      ),
    );
  }
}
