## 11.0.0

> Note: This release has breaking changes.

 - **BREAKING** **FEAT**(share_plus): SharePlus refactor ([#3404](https://github.com/fluttercommunity/plus_plugins/issues/3404)). ([0a19d460](https://github.com/fluttercommunity/plus_plugins/commit/0a19d46010b6ecf5c2f10771e33a39823f7c30b7))

This version introduces the new `SharePlus` class with the `share(params)` method.
It replaces the old `Share` class, which has been deprecated but can still be used.
Check the section "Migrating from `Share` to `SharePlus`" in the `README.md`.

## 10.1.4

 - **FIX**(share_plus): fallback for shareXFiles() to use download on web ([#3388](https://github.com/fluttercommunity/plus_plugins/issues/3388)). ([95a12ee3](https://github.com/fluttercommunity/plus_plugins/commit/95a12ee3982dd61de5d07005de62f81c2e99eb08))

## 10.1.3

 - **REFACTOR**(all): Use range of flutter_lints for broader compatibility ([#3371](https://github.com/fluttercommunity/plus_plugins/issues/3371)). ([8a303add](https://github.com/fluttercommunity/plus_plugins/commit/8a303add3dee1acb8bac5838246490ed8a0fe408))
 - **FIX**(share_plus): A function declaration without a prototype is deprecated in all versions of C ([#3375](https://github.com/fluttercommunity/plus_plugins/issues/3375)). ([40f9c421](https://github.com/fluttercommunity/plus_plugins/commit/40f9c42138bf1c80e6283f46e12109a0e66c0816))
 - **FIX**(share_plus): Set correct Flutter and Dart versions requirements ([#3363](https://github.com/fluttercommunity/plus_plugins/issues/3363)). ([65616668](https://github.com/fluttercommunity/plus_plugins/commit/6561666885f547725f6e88ebaae498832b53efed))

## 10.1.2

 - **FIX**(share_plus): Update privacy manifest path ([#3349](https://github.com/fluttercommunity/plus_plugins/issues/3349)). ([d884a991](https://github.com/fluttercommunity/plus_plugins/commit/d884a9917769e11daa66c1aaa3ebd0d015506e77))

## 10.1.1

 - **FIX**(share_plus): [#3322](https://github.com/fluttercommunity/plus_plugins/issues/3322) Downscale previews on iOS to avoid issues with huge images ([#3320](https://github.com/fluttercommunity/plus_plugins/issues/3320)). ([d8c95c2c](https://github.com/fluttercommunity/plus_plugins/commit/d8c95c2cffaf882cf0670cc5daf889eebd249a77))

## 10.1.0

 - **FEAT**(share_plus): Add Swift Package Manager support ([#3169](https://github.com/fluttercommunity/plus_plugins/issues/3169)). ([b3970225](https://github.com/fluttercommunity/plus_plugins/commit/b3970225b80183548ecc5516a5c1a1ad61016860))

## 10.0.3

 - **FIX**(share_plus): `mime` compatible with v2 (v1 still supported) ([#3309](https://github.com/fluttercommunity/plus_plugins/issues/3309)). ([401db75e](https://github.com/fluttercommunity/plus_plugins/commit/401db75efa24c40fd96a05e79d12801f92666efd))
 - **FIX**(all): Clean up macOS Privacy Manifests ([#3268](https://github.com/fluttercommunity/plus_plugins/issues/3268)). ([d7b98ebd](https://github.com/fluttercommunity/plus_plugins/commit/d7b98ebd7d39b0143931f5cc6e627187576223dc))
 - **FIX**(all): Add macOS Privacy Manifests ([#3251](https://github.com/fluttercommunity/plus_plugins/issues/3251)). ([bf5dad2a](https://github.com/fluttercommunity/plus_plugins/commit/bf5dad2ad249605055bcbd5f663e42569df12d64))

## 10.0.2

 - **FIX**(share_plus): [#2910](https://github.com/fluttercommunity/plus_plugins/issues/2910) Handle user dismissing dialog on shareUri() in web ([#3175](https://github.com/fluttercommunity/plus_plugins/issues/3175)). ([bba78118](https://github.com/fluttercommunity/plus_plugins/commit/bba781187b4af5682331ed90929c61c13137809a))

## 10.0.1

- **CHORE**(share_plus): Update to package:web to ^1.0.0 ([#3105](https://github.com/fluttercommunity/plus_plugins/pull/3105)). ([1f23910a](https://github.com/fluttercommunity/plus_plugins/commit/1f23910ab50fef2e499054f35cedfd14c578976a))

## 10.0.0

> Note: This release has breaking changes.

 - **BREAKING** **FEAT**(share_plus): Introduce optional parameter `nameOverride` to `shareXFiles`. ([#3077](https://github.com/fluttercommunity/plus_plugins/issues/3077)). ([f483bce7](https://github.com/fluttercommunity/plus_plugins/commit/f483bce77f50fc03e8c6c969864dd978e46f32da))
 - **REFACTOR**(all): Remove website files, configs, mentions ([#3018](https://github.com/fluttercommunity/plus_plugins/issues/3018)). ([ecc57146](https://github.com/fluttercommunity/plus_plugins/commit/ecc57146aa8c6b1c9c332169d3cc2205bc4a700f))
 - **FIX**(all): changed homepage url in pubspec.yaml ([#3099](https://github.com/fluttercommunity/plus_plugins/issues/3099)). ([66613656](https://github.com/fluttercommunity/plus_plugins/commit/66613656a85c176ba2ad337e4d4943d1f4171129))
 - **DOCS**(share_plus): Update README.md ([#2903](https://github.com/fluttercommunity/plus_plugins/issues/2903)). ([2a547eb3](https://github.com/fluttercommunity/plus_plugins/commit/2a547eb3f0093160279fbc9de21dde3f3ff75c81))

## 9.0.0

> Note: This release has breaking changes.

 - **BREAKING** **REFACTOR**(share_plus): Share API cleanup ([#2832](https://github.com/fluttercommunity/plus_plugins/issues/2832)). ([fd0511ca](https://github.com/fluttercommunity/plus_plugins/commit/fd0511ca4d55db1e075e72af5a0832b5cfe81244))

## 8.0.3

 - **REFACTOR**(share_plus): Migrate Android example to use the new plugins declaration ([#2742](https://github.com/fluttercommunity/plus_plugins/issues/2742)). ([a73af898](https://github.com/fluttercommunity/plus_plugins/commit/a73af898ee9b73dcc53307186ac4c79e795b1277))
 - **FIX**(share_plus): Recover ShareSuccessManager state after error ([#2817](https://github.com/fluttercommunity/plus_plugins/issues/2817)). ([2b12d8a8](https://github.com/fluttercommunity/plus_plugins/commit/2b12d8a8ada0d3f00abda0467946bb241361d016))
 - **DOCS**(share_plus): Add info regarding localization on Apple to README ([#2764](https://github.com/fluttercommunity/plus_plugins/issues/2764)). ([43f9a305](https://github.com/fluttercommunity/plus_plugins/commit/43f9a3051652448868c5031a45276f9ff870a025))
 - **DOCS**(share_plus): remove typo from the changelog ([#2747](https://github.com/fluttercommunity/plus_plugins/issues/2747)). ([961c8e2d](https://github.com/fluttercommunity/plus_plugins/commit/961c8e2dbf2210947cbed898c90e2776322a0942))

## 8.0.2

> Note: This release has breaking changes.

In this release plugin migrated to package:web, meaning that it now supports WASM!

> Plugin now requires the following:
> - Flutter >=3.19.0
> - Dart >=3.3.0
> - compileSDK 34 for Android part
> - Java 17 for Android part
> - Gradle 8.4 for Android part

- **BREAKING** **FEAT**(share_plus): Migrate to package:web ([#2709](https://github.com/fluttercommunity/plus_plugins/issues/2709)). ([641e7905](https://github.com/fluttercommunity/plus_plugins/commit/641e7905b4055827f1481f036415095dc43afe5f))
- **BREAKING** **BUILD**(share_plus): Target Java 17 on Android ([#2730](https://github.com/fluttercommunity/plus_plugins/issues/2730)). ([e6853a06](https://github.com/fluttercommunity/plus_plugins/commit/e6853a06fa110d69776a85684e302a7c900a6e07))
- **BREAKING** **BUILD**(sensors_plus): Update to target and compile SDK 34 ([#2712](https://github.com/fluttercommunity/plus_plugins/pull/2712)). ([b752fc3](https://github.com/fluttercommunity/plus_plugins/commit/b752fc3bac2b7ce614faf0fbed010200d99a717b))
- **FIX**(share_plus): Resolve deprecation warning in Android part ([#2717](https://github.com/fluttercommunity/plus_plugins/issues/2717)). ([5913ac72](https://github.com/fluttercommunity/plus_plugins/commit/5913ac72f6dfcb0f02c371907871805c940a03a8))
- **FIX**(share_plus): add sharePositionOrigin parameter to shareUri ([#2517](https://github.com/fluttercommunity/plus_plugins/issues/2517)). ([f896d94e](https://github.com/fluttercommunity/plus_plugins/commit/f896d94e6c24551d9dc7d73d8fb05a0f283e0e83))
- **FIX**(share_plus): Add missing call to result for shareUri on iOS ([#2616](https://github.com/fluttercommunity/plus_plugins/issues/2616)). ([65f23a5d](https://github.com/fluttercommunity/plus_plugins/commit/65f23a5d12ac988d7424a56b1d808f2983e0459f))
- **FIX**(share_plus): Add iOS Privacy Info ([#2586](https://github.com/fluttercommunity/plus_plugins/issues/2586)). ([17fc2e05](https://github.com/fluttercommunity/plus_plugins/commit/17fc2e058f04168f17f5118bca24b02483af571b))
- **FIX**(share_plus): Ensure subject is not null before calling putExtra(Intent.EXTRA_SUBJECT, subject) ([#2518](https://github.com/fluttercommunity/plus_plugins/issues/2518)). ([f0bbbefc](https://github.com/fluttercommunity/plus_plugins/commit/f0bbbefc712e01da20e8107e8d25b2005cf7b728))
- **FEAT**(share_plus): Update min iOS target to 12 ([#2662](https://github.com/fluttercommunity/plus_plugins/issues/2662)). ([5cef2e50](https://github.com/fluttercommunity/plus_plugins/commit/5cef2e500cd10d55b749a6d53ce6e733fdb54d34))
- **DOCS**(share_plus): Fix supported platforms in README ([#2510](https://github.com/fluttercommunity/plus_plugins/issues/2510)). ([6b4b855b](https://github.com/fluttercommunity/plus_plugins/commit/6b4b855bb6f3c2897e29d0d3ae4a0c0c99ff8c2f))

## 8.0.1

> Note: DO NOT USE THIS RELEASE. It is invalid due to a publishing issue

## 8.0.0

> Note: This release was retracted due to ([#2251](https://github.com/fluttercommunity/plus_plugins/issues/2251)).

## 7.2.2

 - **FIX**(share_plus): Ensure subject is not null before calling putExtra(Intent.EXTRA_SUBJECT, subject) ([#2518](https://github.com/fluttercommunity/plus_plugins/issues/2518)). ([f0bbbefc](https://github.com/fluttercommunity/plus_plugins/commit/f0bbbefc712e01da20e8107e8d25b2005cf7b728))
 - **DOCS**(share_plus): Fix supported platforms in README ([#2510](https://github.com/fluttercommunity/plus_plugins/issues/2510)). ([6b4b855b](https://github.com/fluttercommunity/plus_plugins/commit/6b4b855bb6f3c2897e29d0d3ae4a0c0c99ff8c2f))

## 7.2.1

 - **CHORE**(share_plus): Update share_plus_platform_interface for compatibility with uuid 4.x

## 7.2.0

> Info: This release is a replacement for release 8.0.0, which was retracted due to issue ([#2251](https://github.com/fluttercommunity/plus_plugins/issues/2251)). As breaking change was reverted the major release was also reverted in favor of this one.

 - **FIX**(share_plus): Change Kotlin version from 1.9.10 to 1.7.22 ([#2252](https://github.com/fluttercommunity/plus_plugins/issues/2252)). ([d4954f36](https://github.com/fluttercommunity/plus_plugins/commit/d4954f36b633e5894cfb4aff4acc995348ce3dca))
 - **FIX**(share_plus): Revert bump to compileSDK 34 ([#2234](https://github.com/fluttercommunity/plus_plugins/issues/2234)). ([6af2328d](https://github.com/fluttercommunity/plus_plugins/commit/6af2328da997b452758e6c78f3815bcf55ac24aa))
 - **FEAT**(share_plus): Remove deprecated VALID_ARCHS iOS property ([#2024](https://github.com/fluttercommunity/plus_plugins/issues/2024)). ([bb79888e](https://github.com/fluttercommunity/plus_plugins/commit/bb79888eda6425d8e772bb4bea47f4966610240f))
 - **DOCS**(share_plus): Fix usage code snippets ([#2106](https://github.com/fluttercommunity/plus_plugins/issues/2106)). ([346e07ea](https://github.com/fluttercommunity/plus_plugins/commit/346e07ea56088321f92530e4334edab0400f4c43))

## 7.1.0

 - **FIX**(share_plus): Regenerate iOS and MacOS example apps ([#1869](https://github.com/fluttercommunity/plus_plugins/issues/1869)). ([5db20ba7](https://github.com/fluttercommunity/plus_plugins/commit/5db20ba7aebd7787a7e56df8e8e6bcb6f832f230))
 - **FEAT**(share_plus): Allow user to share URI with preview image on the iOS native share sheet ([#1779](https://github.com/fluttercommunity/plus_plugins/issues/1779)). ([c83b667e](https://github.com/fluttercommunity/plus_plugins/commit/c83b667eb12394feef69221eda0eab8716aa19d8))
 - **DOCS**(share_plus): Updated document with latest available method ([#1917](https://github.com/fluttercommunity/plus_plugins/issues/1917)). ([7fbe3de6](https://github.com/fluttercommunity/plus_plugins/commit/7fbe3de61b874b7d83d66f70c1e2928378707f86))
 - **DOCS**(all): Fix example links on pub.dev ([#1863](https://github.com/fluttercommunity/plus_plugins/issues/1863)). ([d726035a](https://github.com/fluttercommunity/plus_plugins/commit/d726035ad7631d5a1397d0a2e5df23dc7e30a4f7))

## 7.0.2

- **CHORE**(share_plus): Update file dependency constraints ([#1853](https://github.com/fluttercommunity/plus_plugins/pull/1853)). ([d8ff0cdb](https://github.com/fluttercommunity/plus_plugins/commit/d8ff0cdb665048c59c8acc5fecd1a9c099ee012f))

## 7.0.1

 - **FIX**: Add jvm target compatibility to Kotlin plugins ([#1798](https://github.com/fluttercommunity/plus_plugins/issues/1798)). ([1b7dc432](https://github.com/fluttercommunity/plus_plugins/commit/1b7dc432ffb8d0474c9be6339d20b5a2cbcbab3f))
 - **DOCS**(all): Update READMEs ([#1828](https://github.com/fluttercommunity/plus_plugins/issues/1828)). ([57d9c884](https://github.com/fluttercommunity/plus_plugins/commit/57d9c8845edfc81fdbabcef9eb1d1ca450e62e7d))
 - **CHORE**(share_plus): Win32 dependency upgrade ([#1805](https://github.com/fluttercommunity/plus_plugins/pull/1805)). ([3f68800](https://github.com/fluttercommunity/plus_plugins/commit/c8f7b6342a7c51eafafae95792775505d2b52ce9))

## 7.0.0

> Note: This release has breaking changes.

 - **CHORE**(share_plus_plus): Update Flutter dependencies, set Flutter >=3.3.0 and Dart to >=2.18.0 <4.0.0
 - **BREAKING** **FIX**(all): Add support of namespace property to support Android Gradle Plugin (AGP) 8 (#1727). Projects with AGP < 4.2 are not supported anymore. It is highly recommended to update at least to AGP 7.0 or newer.
 - **BREAKING** **CHORE**(share_plus): Bump min Android to 4.4 (API 19) and iOS to 11, update podspec file (#1773).
 - **REFACTOR**(share_plus): Remove manual dependency override in example app.

## 6.3.4

 - **FIX**(all): Revert addition of namespace to avoid build fails on old AGPs (#1725).

## 6.3.3

 - **FIX**(share_plus): Add compatibility with AGP 8 (Android Gradle Plugin) (#1706).
 - **FIX**(share_plus_android): Fix strict compilation errors in MIME reduction function (#1650).

## 6.3.2

 - **FIX**(share_plus): Set exported=false for BroadcastReceiver on Android (#1613).
 - **FIX**(package_info_plus): Make example app content scrollable (#1614).
 - **FIX**(all): Fix depreciations for flutter 3.7 and 2.19 dart (#1529).

## 6.3.1

 - **FIX**: Fix the error of requestCode value range. (#1340).
 - **FIX**: example broken on web (#1334).
 - **DOCS**: Updates for READMEs and website pages (#1389).

## 6.3.0

 - **FIX**: remove `canLaunch` check (#1315).
 - **FEAT**: Show destination for share with result in example, update example UI (#1314).

## 6.2.0

 - **FIX**: return correct share result on android (#1301).
 - **FEAT**: remove direct dependence of url_launcher (#1295).
 - **DOCS**: #1299 document XFile.fromData (#1300).

## 6.1.0

 - **FIX**: export XFile (#1286).
 - **FEAT**: share XFile created using File.fromData() (#1284).

## 6.0.1

 - **FIX**: Increase min Flutter version to fix dartPluginClass registration (#1275).

## 6.0.0

> Note: This release has breaking changes.

 - **FIX**: lint warnings - add missing dependency for tests (#1233).
 - **FIX**: Show NSSharingServicePicker asynchronously on main thread (#1223).
 - **BREAKING** **REFACTOR**: two-package federated architecture (#1238).

## 5.0.0

> Note: This release has breaking changes.

 - **BREAKING** **FEAT**: Native share UI for Windows (#1158).

## 4.5.3

 - **CHORE**: Version tagging using melos.

## 4.5.2

- Update internal dependencies

## 4.5.1

- Update internal dependencies

## 4.5.0

- iOS: Remove usage of deprecated UIApplication.keyWindow in iOS 13+
- Add `shareXFiles` implementations
- Deprecate `shareFiles*` implementations
- Enable `shareXFiles` implementations on Web

## 4.4.0

- Reverted changes in 4.2.0 due to crash issues. See #1081

## 4.3.0

- iOS: Throw PlatformException when iPad share dialog not appearing (sharePositionOrigin not in sourceView)

## 4.2.0

- iOS: Fix Instagram does not show up in provider list for web links
  - issue #459 appear again
  - put back NSURL for the shareText, when text is pure URL
  - using LPMetadataProvider to get LPLinkMetadata make the user experience better

## 4.1.0

- iOS: Fix text sharing.
  - Previously, the text was being encoded as a URL, this caused the share sheet to appear empty.
  - Now the shared text is not encoded as a URL anymore but rather shared as plain text.
  - Sharing text + subject + attachments should work on apps that support that (e.g. Mail app).
  - Example: Sharing Text + Image on Telegram is possible and both are shared.
  - Some apps still have limitations with sharing. For example, Gmail app does not support the subject field.
  - Related issue: #730

## 4.0.10+1

- Add issue_tracker link.

## 4.0.10

- iOS: Fix 'share text' not showing when share files

## 4.0.9

- iOS: Fix image file names not preserved

## 4.0.8

- iOS: Fix 'Save Image' option not showing

## 4.0.7

- Add documentation iPad

## 4.0.6

- iOS: Fix file names not preserved and poor previews for files

## 4.0.5

- Update dependencies
- Fix analyzer warnings

## 4.0.4

- iOS: Fix subject not working when sharing raw url or files via email

## 4.0.3

- Android: Revert increased minSdkVersion back to 16
- Gracefully fall back from `shareWithResult` to regular `share` methods on unsupported platforms
- Improve documentation for `shareWithResult` methods

## 4.0.2

- Fix type mismatch on Android for some users
- Set min Flutter to 1.20.0 for all platforms
- Lower Android minSdkVersion to 22

## 4.0.1

- Hotfix dependencies

## 4.0.0

- iOS, Android, MacOS: Add `shareWithResult` methods to get feedback on user action
- Android: Increased minSdkVersion to 23
- MacOS: Native sharing implementation

## 3.1.0

- Android: Migrate to Kotlin
- Android: Update dependencies, build config updates

## 3.0.5

- Fix example embedding issue

## 3.0.4

- iOS: Fixed sharing malformed URLs

## 3.0.3

- Improve documentation for `shareFiles` method

## 3.0.2

- Apply code improvements
- Update gradle for plugin
- Update flutter dependencies

## 3.0.1

- Update Android dependencies for plugin and example, bump compileSDK to 31

## 3.0.0

- Remove deprecated method `registerWith` (of Android v1 embedding)

## 2.2.0

- migrate integration_test to flutter sdk

## 2.1.5

- Fixed: Use NSURL for web links (iOS)

## 2.1.4

- Android: migrate to mavenCentral

## 2.1.3

- Update iOS share target to present on the top ViewController. This fixes "Unable to present" errors when the app is already presenting such as in an add to app scenario.

## 2.1.2

- Do not tear down method channel onDetachedFromActivity.

## 2.1.1

- Updated iOS share sheet preview title to use subject when text is not set

## 2.1.0

- Fixes #241 resolves issues with deprecations as of android API version 29 and replaces the requirement for external storage locations with an easy application cache usage.

## 2.0.3

- Improve documentation

## 2.0.2

- Fixed crash on launch when running iOS 12.x and below

## 2.0.1

- Added preview title to iOS share sheet

## 2.0.0

- Migrated to null safety
- Add macOS support (`share_plus_macos`)

## 1.2.0

- Add Web support (`share_plus_web`)
- Rename method channel to avoid conflicts

## 1.1.1

- Transfer to plus-plugins monorepo

## 0.7.0

- Add Linux support for basic share capabilities.

## 0.6.6

- Transfer package to Flutter Community under new name `share_plus`.

## 0.6.5

- Added support for sharing files

## 0.6.4+5

- Update package:e2e -> package:integration_test

## 0.6.4+4

- Update package:e2e reference to use the local version in the flutter/plugins
  repository.

## 0.6.4+3

- Post-v2 Android embedding cleanup.

## 0.6.4+2

- Update lower bound of dart dependency to 2.1.0.

## 0.6.4+1

- Declare API stability and compatibility with `1.0.0` (more details at: https://github.com/flutter/flutter/wiki/Package-migration-to-1.0.0).

## 0.6.4

- Remove Android dependencies fallback.
- Require Flutter SDK 1.12.13+hotfix.5 or greater.
- Fix CocoaPods podspec lint warnings.

## 0.6.3+8

- Replace deprecated `getFlutterEngine` call on Android.

## 0.6.3+7

- Updated gradle version of example.

## 0.6.3+6

- Make the pedantic dev_dependency explicit.

## 0.6.3+5

- Remove the deprecated `author:` field from pubspec.yaml
- Migrate the plugin to the pubspec platforms manifest.
- Require Flutter SDK 1.10.0 or greater.

## 0.6.3+4

- Fix pedantic lints. This shouldn't affect existing functionality.

## 0.6.3+3

- README update.

## 0.6.3+2

- Remove AndroidX warnings.

## 0.6.3+1

- Include lifecycle dependency as a compileOnly one on Android to resolve
  potential version conflicts with other transitive libraries.

## 0.6.3

- Support the v2 Android embedder.
- Update to AndroidX.
- Migrate to using the new e2e test binding.
- Add a e2e test.

## 0.6.2+4

- Define clang module for iOS.

## 0.6.2+3

- Fix iOS crash when setting subject to null.

## 0.6.2+2

- Update and migrate iOS example project.

## 0.6.2+1

- Specify explicit type for `invokeMethod`.
- Use `const` for `Rect`.
- Updated minimum Flutter SDK to 1.6.0.

## 0.6.2

- Add optional subject to fill email subject in case user selects email app.

## 0.6.1+2

- Update Dart code to conform to current Dart formatter.

## 0.6.1+1

- Fix analyzer warnings about `const Rect` in tests.

## 0.6.1

- Updated Android compileSdkVersion to 28 to match other plugins.

## 0.6.0+1

- Log a more detailed warning at build time about the previous AndroidX
  migration.

## 0.6.0

- **Breaking change**. Migrate from the deprecated original Android Support
  Library to AndroidX. This shouldn't result in any functional changes, but it
  requires any Android apps using this plugin to [also
  migrate](https://developer.android.com/jetpack/androidx/migrate) if they're
  using the original support library.

## 0.5.3

- Added missing test package dependency.
- Bumped version of mockito package dependency to pick up Dart 2 support.

## 0.5.2

- Fixes iOS sharing

## 0.5.1

- Updated Gradle tooling to match Android Studio 3.1.2.

## 0.5.0

- **Breaking change**. Namespaced the `share` method inside a `Share` class.
- Fixed crash when sharing on iPad.
- Added functionality to specify share sheet origin on iOS.

## 0.4.0

- **Breaking change**. Set SDK constraints to match the Flutter beta release.

## 0.3.2

- Fixed Dart 2 type error.

## 0.3.1

- Simplified and upgraded Android project template to Android SDK 27.
- Updated package description.

## 0.3.0

- **Breaking change**. Upgraded to Gradle 4.1 and Android Studio Gradle plugin
  3.0.1. Older Flutter projects need to upgrade their Gradle setup as well in
  order to use this version of the plugin. Instructions can be found
  [here](https://github.com/flutter/flutter/wiki/Updating-Flutter-projects-to-Gradle-4.1-and-Android-Studio-Gradle-plugin-3.0.1).

## 0.2.2

- Added FLT prefix to iOS types

## 0.2.1

- Updated README
- Bumped buildToolsVersion to 25.0.3

## 0.2.0

- Upgrade to new plugin registration. (https://groups.google.com/forum/#!topic/flutter-dev/zba1Ynf2OKM)

## 0.1.0

- Initial Open Source release.
