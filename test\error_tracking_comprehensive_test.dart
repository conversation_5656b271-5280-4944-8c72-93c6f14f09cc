import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/utils/error_tracker.dart';
import 'package:tajer_plus/core/utils/app_logger.dart';

/// اختبار شامل لنظام تتبع الأخطاء
/// يختبر جميع أنواع الأخطاء المختلفة ويطبعها في الترمنال
void main() {
  group('🧪 اختبار شامل لنظام تتبع الأخطاء', () {
    setUp(() {
      // تنظيف السجل قبل كل اختبار
      ErrorTracker.clearHistory();
      // تفعيل التسجيل
      ErrorTracker.setLoggingEnabled(true);
      AppLogger.info('🔄 بدء اختبار جديد لنظام تتبع الأخطاء');
    });

    test('🔍 اختبار أخطاء قاعدة البيانات', () {
      debugPrint('\n🧪 اختبار أخطاء قاعدة البيانات...');

      // محاكاة خطأ قاعدة بيانات
      try {
        throw Exception('table settings has no column named category');
      } catch (e, stackTrace) {
        ErrorTracker.captureError(
          'فشل في تنفيذ استعلام قاعدة البيانات',
          error: e,
          stackTrace: stackTrace,
          context: {
            'table': 'settings',
            'operation': 'INSERT',
            'sql':
                'INSERT INTO settings (id, key, value, description, category, type, is_required, created_at, created_by, updated_at, is_deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
            'args': [
              'e28f2577-0579-4e12-aea8-762eaf89b092',
              'company_name',
              'تاجر بلس',
              'اسم الشركة الرسمي',
              'company',
              'text',
              1
            ],
          },
        );
      }

      final errors = ErrorTracker.getRecentErrors();
      expect(errors.length, 1);
      expect(errors[0].message, contains('قاعدة البيانات'));
    });

    test('🌐 اختبار أخطاء الشبكة', () {
      debugPrint('\n🧪 اختبار أخطاء الشبكة...');

      // محاكاة خطأ شبكة
      try {
        throw Exception('SocketException: Failed host lookup');
      } catch (e, stackTrace) {
        ErrorTracker.captureError(
          'فشل في الاتصال بالخادم',
          error: e,
          stackTrace: stackTrace,
          context: {
            'url': 'https://api.example.com/data',
            'method': 'GET',
            'timeout': '30 seconds',
            'retry_count': 3,
          },
        );
      }

      final errors = ErrorTracker.getRecentErrors();
      expect(errors.length, 1);
      expect(errors[0].context!['url'], 'https://api.example.com/data');
    });

    test('📱 اختبار أخطاء Flutter', () {
      debugPrint('\n🧪 اختبار أخطاء Flutter...');

      // محاكاة خطأ Flutter
      try {
        throw FlutterError('RenderFlex overflowed by 79 pixels on the right');
      } catch (e, stackTrace) {
        ErrorTracker.captureError(
          'خطأ في تخطيط الواجهة',
          error: e,
          stackTrace: stackTrace,
          context: {
            'widget': 'Row',
            'overflow': '79 pixels',
            'direction': 'right',
            'screen': 'users_screen.dart',
            'line': 586,
          },
        );
      }

      final errors = ErrorTracker.getRecentErrors();
      expect(errors.length, 1);
      expect(errors[0].error, isA<FlutterError>());
    });

    test('🔐 اختبار أخطاء المصادقة', () {
      debugPrint('\n🧪 اختبار أخطاء المصادقة...');

      // محاكاة خطأ مصادقة
      try {
        throw Exception('Invalid credentials');
      } catch (e, stackTrace) {
        ErrorTracker.captureError(
          'فشل في تسجيل الدخول',
          error: e,
          stackTrace: stackTrace,
          context: {
            'username': 'admin',
            'attempt_count': 3,
            'ip_address': '*************',
            'user_agent': 'TajerPlus/1.0',
            'timestamp': DateTime.now().toIso8601String(),
          },
        );
      }

      final errors = ErrorTracker.getRecentErrors();
      expect(errors.length, 1);
      expect(errors[0].context!['username'], 'admin');
    });

    test('💾 اختبار أخطاء الملفات', () {
      debugPrint('\n🧪 اختبار أخطاء الملفات...');

      // محاكاة خطأ ملف
      try {
        throw Exception('FileSystemException: Cannot open file');
      } catch (e, stackTrace) {
        ErrorTracker.captureError(
          'فشل في قراءة الملف',
          error: e,
          stackTrace: stackTrace,
          context: {
            'file_path': '/storage/emulated/0/tajer_plus/backup.db',
            'operation': 'read',
            'file_size': '2.5 MB',
            'permissions': 'rw-r--r--',
          },
        );
      }

      final errors = ErrorTracker.getRecentErrors();
      expect(errors.length, 1);
      expect(errors[0].context!['operation'], 'read');
    });

    test('📊 اختبار إحصائيات الأخطاء', () {
      debugPrint('\n🧪 اختبار إحصائيات الأخطاء...');

      // إضافة عدة أخطاء مختلفة
      for (int i = 0; i < 5; i++) {
        try {
          throw Exception('Database error $i');
        } catch (e, stackTrace) {
          ErrorTracker.captureError(
            'خطأ قاعدة بيانات رقم $i',
            error: e,
            stackTrace: stackTrace,
            context: {'error_number': i},
          );
        }
      }

      for (int i = 0; i < 3; i++) {
        try {
          throw FlutterError('UI error $i');
        } catch (e, stackTrace) {
          ErrorTracker.captureError(
            'خطأ واجهة رقم $i',
            error: e,
            stackTrace: stackTrace,
            context: {'error_number': i},
          );
        }
      }

      final stats = ErrorTracker.getErrorStats();
      debugPrint('\n📊 إحصائيات الأخطاء:');
      debugPrint('   • إجمالي الأخطاء: ${stats['totalErrors']}');
      debugPrint('   • أكثر الأخطاء شيوعاً: ${stats['mostCommonError']}');
      debugPrint('   • عدد الأخطاء الشائعة: ${stats['mostCommonErrorCount']}');
      debugPrint(
          '   • معدل الأخطاء الحديثة: ${stats['recentErrorRate']} خطأ/دقيقة');

      expect(stats['totalErrors'], 8);
      expect(stats['mostCommonError'], '_Exception');
      expect(stats['mostCommonErrorCount'], 5);
    });

    test('🧹 اختبار تنظيف سجل الأخطاء', () {
      debugPrint('\n🧪 اختبار تنظيف سجل الأخطاء...');

      // إضافة بعض الأخطاء
      for (int i = 0; i < 3; i++) {
        try {
          throw Exception('Test error $i');
        } catch (e, stackTrace) {
          ErrorTracker.captureError(
            'خطأ اختبار رقم $i',
            error: e,
            stackTrace: stackTrace,
          );
        }
      }

      expect(ErrorTracker.getRecentErrors().length, 3);

      // تنظيف السجل
      ErrorTracker.clearHistory();
      expect(ErrorTracker.getRecentErrors().length, 0);

      debugPrint('✅ تم تنظيف سجل الأخطاء بنجاح');
    });

    test('⚙️ اختبار تفعيل/تعطيل التسجيل', () {
      debugPrint('\n🧪 اختبار تفعيل/تعطيل التسجيل...');

      // تعطيل التسجيل
      ErrorTracker.setLoggingEnabled(false);

      try {
        throw Exception('This error should not be logged');
      } catch (e, stackTrace) {
        ErrorTracker.captureError(
          'خطأ مع التسجيل معطل',
          error: e,
          stackTrace: stackTrace,
        );
      }

      // الخطأ يجب أن يكون في السجل ولكن لا يطبع في الترمنال
      expect(ErrorTracker.getRecentErrors().length, 1);

      // إعادة تفعيل التسجيل
      ErrorTracker.setLoggingEnabled(true);

      try {
        throw Exception('This error should be logged');
      } catch (e, stackTrace) {
        ErrorTracker.captureError(
          'خطأ مع التسجيل مفعل',
          error: e,
          stackTrace: stackTrace,
        );
      }

      expect(ErrorTracker.getRecentErrors().length, 2);
      debugPrint('✅ تم اختبار تفعيل/تعطيل التسجيل بنجاح');
    });

    tearDown(() {
      AppLogger.info('✅ انتهى الاختبار - تنظيف البيانات');
      ErrorTracker.clearHistory();
    });
  });

  group('🔧 اختبارات متقدمة لنظام تتبع الأخطاء', () {
    test('📈 اختبار الحد الأقصى لحجم السجل', () {
      debugPrint('\n🧪 اختبار الحد الأقصى لحجم السجل...');

      ErrorTracker.clearHistory();

      // إضافة أكثر من 100 خطأ (الحد الأقصى)
      for (int i = 0; i < 150; i++) {
        try {
          throw Exception('Error $i');
        } catch (e, stackTrace) {
          ErrorTracker.captureError(
            'خطأ رقم $i',
            error: e,
            stackTrace: stackTrace,
            context: {'index': i},
          );
        }
      }

      final errors = ErrorTracker.getRecentErrors();
      expect(errors.length, 100); // يجب أن يكون 100 فقط

      // التحقق من أن الأخطاء الأحدث محفوظة
      expect(errors.first.context!['index'], 149);
      expect(errors.last.context!['index'], 50);

      debugPrint('✅ تم اختبار الحد الأقصى لحجم السجل بنجاح');
    });
  });
}
