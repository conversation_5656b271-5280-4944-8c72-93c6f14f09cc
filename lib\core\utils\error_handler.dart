import 'package:flutter/material.dart';
import 'app_logger.dart';
import '../widgets/dialog_forms.dart';
import '../../../core/theme/index.dart';

/// مكتبة للتعامل مع الأخطاء في التطبيق
class ErrorHandler {
  /// عرض رسالة خطأ في شريط Snackbar
  static void showError(BuildContext context, String message,
      {Object? error, StackTrace? stackTrace}) {
    // تسجيل الخطأ
    AppLogger.error('$message: ${error ?? ''}');

    // عرض رسالة للمستخدم
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'إغلاق',
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
          textColor: AppColors.onPrimary,
        ),
      ),
    );
  }

  /// عرض رسالة خطأ في حوار
  static Future<void> showErrorDialog(
      BuildContext context, String title, String message,
      {Object? error, StackTrace? stackTrace}) async {
    // تسجيل الخطأ
    AppLogger.error('$message: ${error ?? ''}');

    // عرض حوار للمستخدم
    await InfoDialog.show(
      context: context,
      title: title,
      message: message,
      icon: Icons.error_outline,
      iconColor: AppColors.error,
    );
  }

  /// عرض رسالة نجاح في شريط Snackbar
  static void showSuccess(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض رسالة تحذير في شريط Snackbar
  static void showWarning(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.warning,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'إغلاق',
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
          textColor: AppColors.onPrimary,
        ),
      ),
    );
  }

  /// عرض رسالة معلومات في شريط Snackbar
  static void showInfo(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.info,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// عرض حوار تأكيد
  static Future<bool> showConfirmationDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    final result = await ConfirmDialog.show(
      context: context,
      title: title,
      message: message,
      confirmButtonText: 'نعم',
      cancelButtonText: 'لا',
      icon: Icons.help_outline,
      confirmButtonColor: AppColors.info,
    );

    return result == true;
  }

  /// عرض حوار إدخال
  static Future<String?> showInputDialog(
    BuildContext context,
    String title,
    String message, {
    String hintText = '',
    String initialValue = '',
  }) async {
    return await InputDialog.show(
      context: context,
      title: title,
      message: message,
      hintText: hintText,
      initialValue: initialValue,
    );
  }

  /// معالجة العمليات المستقبلية مع عرض مؤشر التحميل
  static Future<T?> handleFutureWithLoading<T>({
    required BuildContext context,
    required Future<T> future,
    String loadingMessage = 'جاري التحميل...',
    String? successMessage,
    String? errorMessage,
  }) async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text(loadingMessage),
            ],
          ),
        ),
      );

      // تنفيذ العملية
      final result = await future;

      // إغلاق مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة النجاح إذا كانت موجودة
      if (successMessage != null && context.mounted) {
        showSuccess(context, successMessage);
      }

      return result;
    } catch (e, stackTrace) {
      // إغلاق مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // عرض رسالة الخطأ
      if (context.mounted) {
        showError(
          context,
          errorMessage ?? 'حدث خطأ: $e',
          error: e,
          stackTrace: stackTrace,
        );
      }

      return null;
    }
  }

  /// معالجة الأخطاء في النماذج
  static Future<bool> handleFormSubmission({
    required BuildContext context,
    required GlobalKey<FormState> formKey,
    required Future<bool> Function() submitAction,
    required String successMessage,
    required String errorMessage,
    bool popOnSuccess = true,
  }) async {
    // التحقق من صحة النموذج
    if (!formKey.currentState!.validate()) {
      return false;
    }

    try {
      // تنفيذ العملية
      final success = await submitAction();

      if (!context.mounted) return false;

      // عرض رسالة النجاح أو الخطأ
      if (success) {
        showSuccess(context, successMessage);

        // الرجوع إلى الشاشة السابقة إذا كان مطلوبًا
        if (popOnSuccess) {
          Navigator.pop(context, true);
        }
      } else {
        showError(context, errorMessage);
      }

      return success;
    } catch (e, stackTrace) {
      if (!context.mounted) return false;

      // عرض رسالة الخطأ
      showError(
        context,
        'حدث خطأ: $e',
        error: e,
        stackTrace: stackTrace,
      );

      return false;
    }
  }

  /// معالجة الأخطاء في عمليات الحذف
  static Future<bool> handleDeleteOperation({
    required BuildContext context,
    required String itemName,
    required Future<bool> Function() deleteAction,
  }) async {
    // عرض حوار التأكيد
    final confirmed = await ConfirmDialog.show(
      context: context,
      title: 'تأكيد الحذف',
      message: 'هل أنت متأكد من حذف $itemName؟',
      confirmButtonText: 'حذف',
      cancelButtonText: 'إلغاء',
      icon: Icons.delete,
      confirmButtonColor: AppColors.error,
    );

    if (confirmed != true) {
      return false;
    }

    try {
      // حفظ السياق الحالي
      final currentContext = context;
      if (!currentContext.mounted) return false;

      // تنفيذ عملية الحذف
      final success = await handleFutureWithLoading(
        context: currentContext,
        future: deleteAction(),
        loadingMessage: 'جاري الحذف...',
        successMessage: 'تم حذف $itemName بنجاح',
        errorMessage: 'فشل في حذف $itemName',
      );

      return success ?? false;
    } catch (e, stackTrace) {
      if (!context.mounted) return false;

      // عرض رسالة الخطأ
      showError(
        context,
        'حدث خطأ أثناء الحذف: $e',
        error: e,
        stackTrace: stackTrace,
      );

      return false;
    }
  }

  /// معالجة الأخطاء في عمليات الاستعلام
  static Future<T?> handleQuery<T>({
    required BuildContext context,
    required Future<T> Function() queryAction,
    String? errorMessage,
    bool showLoadingIndicator = true,
  }) async {
    try {
      // حفظ السياق الحالي
      final currentContext = context;
      if (!currentContext.mounted) return null;

      if (showLoadingIndicator) {
        return await handleFutureWithLoading(
          context: currentContext,
          future: queryAction(),
          loadingMessage: 'جاري التحميل...',
          errorMessage: errorMessage,
        );
      } else {
        return await queryAction();
      }
    } catch (e, stackTrace) {
      if (!context.mounted) return null;

      // عرض رسالة الخطأ
      showError(
        context,
        errorMessage ?? 'حدث خطأ أثناء الاستعلام: $e',
        error: e,
        stackTrace: stackTrace,
      );

      return null;
    }
  }
}
