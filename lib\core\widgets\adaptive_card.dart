import 'package:flutter/material.dart';
import '../theme/index.dart';

/// بطاقة ذكية متكيفة مع الثيم والألوان
/// تحل مشاكل التباين والرؤية تلقائياً
class AdaptiveCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final Color? backgroundColor;
  final Color? borderColor;
  final double borderRadius;
  final double? width;
  final double? height;
  final bool useGradient;
  final List<Color>? gradientColors;
  final VoidCallback? onTap;
  final bool adaptToTheme;

  const AdaptiveCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius = 12.0,
    this.width,
    this.height,
    this.useGradient = false,
    this.gradientColors,
    this.onTap,
    this.adaptToTheme = true,
  });

  /// مُنشئ مبسط للبطاقات الصغيرة
  const AdaptiveCard.small({
    super.key,
    required this.child,
    this.onTap,
    this.backgroundColor,
    this.adaptToTheme = true,
  })  : padding = AppDimensions.cardPaddingSmall,
        margin = const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        elevation = AppDimensions.elevationLow,
        borderColor = null,
        borderRadius = AppDimensions.radiusSmall,
        width = null,
        height = AppDimensions.cardHeightSmall,
        useGradient = false,
        gradientColors = null;

  /// مُنشئ مبسط للبطاقات المتوسطة
  const AdaptiveCard.medium({
    super.key,
    required this.child,
    this.onTap,
    this.backgroundColor,
    this.adaptToTheme = true,
  })  : padding = AppDimensions.cardPaddingMedium,
        margin = const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        elevation = AppDimensions.elevationMedium,
        borderColor = null,
        borderRadius = AppDimensions.radiusMedium,
        width = null,
        height = AppDimensions.cardHeightMedium,
        useGradient = false,
        gradientColors = null;

  /// مُنشئ مبسط للبطاقات الكبيرة
  const AdaptiveCard.large({
    super.key,
    required this.child,
    this.onTap,
    this.backgroundColor,
    this.adaptToTheme = true,
  })  : padding = AppDimensions.cardPaddingLarge,
        margin = const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        elevation = AppDimensions.elevationHigh,
        borderColor = null,
        borderRadius = AppDimensions.radiusLarge,
        width = null,
        height = AppDimensions.cardHeightLarge,
        useGradient = false,
        gradientColors = null;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // تحديد لون الخلفية الذكي
    Color cardBackground;
    if (backgroundColor != null) {
      cardBackground = backgroundColor!;
    } else if (adaptToTheme) {
      cardBackground = AppColors.getAdaptiveCardBackground(isDark);
    } else {
      cardBackground = Theme.of(context).cardColor;
    }

    // تحديد لون الحدود الذكي
    Color cardBorder;
    if (borderColor != null) {
      cardBorder = borderColor!;
    } else {
      cardBorder = AppColors.getAdaptiveBorderColor(isDark);
    }

    // تحديد الظل الذكي
    Color shadowColor = AppColors.getAdaptiveShadowColor(isDark);

    Widget cardWidget = Container(
      width: width,
      height: height,
      margin: margin ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: useGradient ? null : cardBackground,
        gradient: useGradient ? _buildGradient(isDark) : null,
        borderRadius: BorderRadius.circular(borderRadius),
        border: Border.all(
          color: cardBorder.withValues(alpha: 0.2),
          width: 0.5,
        ),
        boxShadow: [
          BoxShadow(
            color: shadowColor,
            blurRadius: elevation ?? 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: Padding(
          padding: padding ?? const EdgeInsets.all(16),
          child: child,
        ),
      ),
    );

    if (onTap != null) {
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(borderRadius),
          child: cardWidget,
        ),
      );
    }

    return cardWidget;
  }

  /// إنشاء تدرج ذكي
  LinearGradient? _buildGradient(bool isDark) {
    if (!useGradient) return null;

    List<Color> colors;
    if (gradientColors != null && gradientColors!.isNotEmpty) {
      colors = gradientColors!;
    } else {
      // تدرج افتراضي متكيف مع الثيم
      colors = isDark
          ? [
              AppColors.darkSurface,
              AppColors.darkSurfaceVariant,
            ]
          : [
              AppColors.lightSurface,
              AppColors.lightSurfaceVariant,
            ];
    }

    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: colors,
    );
  }
}

/// بطاقة إحصائيات ذكية للداش بورد مع أحجام ثابتة
class AdaptiveStatsCard extends StatelessWidget {
  final String title;
  final String value;
  final String subtitle;
  final IconData icon;
  final Color? iconColor;
  final Color? valueColor;
  final VoidCallback? onTap;
  final bool showTrend;
  final double? trendValue;
  final double? width;
  final double? height;

  const AdaptiveStatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    this.iconColor,
    this.valueColor,
    this.onTap,
    this.showTrend = false,
    this.trendValue,
    this.width,
    this.height,
  });

  /// مُنشئ مبسط لبطاقة إحصائيات صغيرة
  const AdaptiveStatsCard.small({
    super.key,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    this.iconColor,
    this.valueColor,
    this.onTap,
    this.showTrend = false,
    this.trendValue,
  })  : width = AppDimensions.cardWidthSmall,
        height = AppDimensions.cardHeightSmall;

  /// مُنشئ مبسط لبطاقة إحصائيات متوسطة
  const AdaptiveStatsCard.medium({
    super.key,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    this.iconColor,
    this.valueColor,
    this.onTap,
    this.showTrend = true,
    this.trendValue,
  })  : width = AppDimensions.cardWidthMedium,
        height = AppDimensions.cardHeightMedium;

  /// مُنشئ مبسط لبطاقة إحصائيات كبيرة
  const AdaptiveStatsCard.large({
    super.key,
    required this.title,
    required this.value,
    required this.subtitle,
    required this.icon,
    this.iconColor,
    this.valueColor,
    this.onTap,
    this.showTrend = true,
    this.trendValue,
  })  : width = AppDimensions.cardWidthLarge,
        height = AppDimensions.cardHeightLarge;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final cardBackground = AppColors.getAdaptiveCardBackground(isDark);

    // ألوان ذكية متكيفة مع فحص التباين
    final titleColor = SmartThemeSystem.getSmartTextColor(context,
        backgroundColor: cardBackground);
    final subtitleColor = AppColors.getAdaptiveSecondaryTextColor(isDark);
    final cardIconColor = iconColor ?? DynamicColors.primary;
    final cardValueColor = valueColor ?? titleColor;

    // فحص التباين وإصلاحه إذا لزم الأمر
    final contrastCheck = SmartThemeSystem.fixContrastIssues(
      context,
      cardBackground,
      titleColor,
    );

    final finalTitleColor = contrastCheck['textColor']!;
    final finalValueColor =
        AppColors.ensureTextContrast(cardValueColor, cardBackground);

    return AdaptiveCard(
      onTap: onTap,
      width: width,
      height: height, // إزالة الارتفاع الثابت للسماح بالتكيف التلقائي
      child: LayoutBuilder(
        builder: (context, constraints) {
          try {
            // حساب الارتفاع المتاح مع هامش أمان أكبر
            final availableHeight = constraints.maxHeight > 0
                ? constraints.maxHeight - 16 // هامش أمان أكبر 16 بكسل
                : null;

            return SizedBox(
              height: availableHeight,
              child: SingleChildScrollView(
                physics: const NeverScrollableScrollPhysics(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // الأيقونة والعنوان
                    Flexible(
                      flex: 0,
                      child: Row(
                        children: [
                          Container(
                            width: AppDimensions.mediumIconSize,
                            height: AppDimensions.mediumIconSize,
                            padding: EdgeInsets.all(AppDimensions.tinyMargin),
                            decoration: BoxDecoration(
                              color: cardIconColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(
                                  AppDimensions.smallRadius),
                            ),
                            child: Icon(
                              icon,
                              color: cardIconColor,
                              size: AppDimensions.defaultIconSize,
                            ),
                          ),
                          SizedBox(width: AppDimensions.smallMargin),
                          Expanded(
                            child: Text(
                              title,
                              style: AppTypography(
                                fontSize: AppDimensions.defaultFontSize,
                                fontWeight: FontWeight.w500,
                                color: finalTitleColor,
                                height: 1.1, // تقليل ارتفاع السطر
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                        height: AppDimensions.tinySpacing), // تقليل المسافة

                    // القيمة
                    Flexible(
                      flex: 0,
                      child: Text(
                        value,
                        style: AppTypography(
                          fontSize: AppDimensions.titleFontSize,
                          fontWeight: FontWeight.bold,
                          color: finalValueColor,
                          height: 1.0, // تقليل ارتفاع السطر
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(
                        height: AppDimensions.tinySpacing /
                            2), // تقليل المسافة أكثر

                    // النص الفرعي
                    Flexible(
                      flex: 1,
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              subtitle,
                              style: AppTypography(
                                fontSize: AppDimensions.smallFontSize,
                                color: subtitleColor,
                                height: 1.1, // تقليل ارتفاع السطر
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (showTrend && trendValue != null) ...[
                            Icon(
                              trendValue! >= 0
                                  ? Icons.trending_up
                                  : Icons.trending_down,
                              size: AppDimensions.smallIconSize,
                              color: trendValue! >= 0
                                  ? AppColors.success
                                  : AppColors.error,
                            ),
                            SizedBox(width: AppDimensions.tinySpacing / 2),
                            Text(
                              '${trendValue!.abs().toStringAsFixed(1)}%',
                              style: AppTypography(
                                fontSize: AppDimensions.smallFontSize,
                                fontWeight: FontWeight.w500,
                                color: trendValue! >= 0
                                    ? AppColors.success
                                    : AppColors.error,
                                height: 1.1, // تقليل ارتفاع السطر
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          } catch (e, stackTrace) {
            // تسجيل الخطأ
            debugPrint('خطأ في تخطيط AdaptiveStatsCard: $e');
            debugPrint('StackTrace: $stackTrace');

            // إرجاع ودجت بديل في حالة الخطأ
            return Container(
              padding: AppDimensions.cardPadding,
              child: const Text(
                'خطأ في عرض البطاقة',
                style: AppTypography(color: AppColors.error),
              ),
            );
          }
        },
      ),
    );
  }
}

/// زر ذكي متكيف
class AdaptiveButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;
  final bool isOutlined;
  final double? borderRadius;
  final EdgeInsetsGeometry? padding;

  const AdaptiveButton({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.isOutlined = false,
    this.borderRadius,
    this.padding,
  });

  /// مُنشئ مبسط لزر صغير
  const AdaptiveButton.small({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.isOutlined = false,
  })  : borderRadius = AppDimensions.radiusSmall,
        padding = AppDimensions.buttonPaddingSmall;

  /// مُنشئ مبسط لزر متوسط
  const AdaptiveButton.medium({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.isOutlined = false,
  })  : borderRadius = AppDimensions.radiusMedium,
        padding = AppDimensions.buttonPaddingMedium;

  /// مُنشئ مبسط لزر كبير
  const AdaptiveButton.large({
    super.key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.icon,
    this.isOutlined = false,
  })  : borderRadius = AppDimensions.radiusLarge,
        padding = AppDimensions.buttonPaddingLarge;

  @override
  Widget build(BuildContext context) {
    // تحديد الألوان الذكية مع فحص التباين
    Color buttonColor = backgroundColor ?? DynamicColors.primary;
    Color buttonTextColor = textColor ??
        SmartThemeSystem.getSmartButtonTextColor(context, buttonColor);

    if (isOutlined) {
      return OutlinedButton.icon(
        onPressed: onPressed,
        icon: icon != null
            ? Icon(icon, color: buttonColor)
            : const SizedBox.shrink(),
        label: Text(
          text,
          style: AppTypography(color: buttonColor),
        ),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: buttonColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(
                borderRadius ?? AppDimensions.defaultRadius),
          ),
          padding: padding,
        ),
      );
    }

    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: icon != null
          ? Icon(icon, color: buttonTextColor)
          : const SizedBox.shrink(),
      label: Text(
        text,
        style: AppTypography(color: buttonTextColor),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: buttonColor,
        foregroundColor: buttonTextColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
              borderRadius ?? AppDimensions.defaultRadius),
        ),
        padding: padding,
      ),
    );
  }
}
