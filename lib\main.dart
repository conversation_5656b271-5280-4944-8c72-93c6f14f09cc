import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'core/theme/index.dart';
import 'core/routes/app_routes.dart';
import 'core/utils/index.dart';
import 'core/services/cache_service.dart';
import 'core/services/image_optimizer_service.dart';
import 'core/services/session_manager.dart';
import 'core/widgets/responsive_app.dart';
import 'core/database/database_helper.dart';
import 'core/auth/permission_initializer.dart';
import 'core/providers/app_providers.dart';
import 'core/providers/presenter_factory.dart';
// import 'debug/quick_error_test.dart'; // معطل حالياً

// Clave global para la navegación
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() {
  // Initialize error handling before anything else
  GlobalErrorHandler.runZonedApp(() async {
    WidgetsFlutterBinding.ensureInitialized();
    GlobalErrorHandler.init();

    // تفعيل التسجيل لتتبع الأخطاء
    AppLogger.setLoggingEnabled(true);
    ErrorTracker.setLoggingEnabled(true);
    debugPrint('🔄 تم تفعيل التسجيل لتتبع الأخطاء');

    // تشغيل اختبار سريع لنظام تتبع الأخطاء في حالة التطوير (معطل حالياً)
    // يمكن تفعيله عند الحاجة لاختبار نظام تتبع الأخطاء
    // if (kDebugMode) {
    //   Timer(const Duration(seconds: 3), () {
    //     QuickErrorTest.runQuickTest();
    //   });
    // }

    // السماح بالوضع الأفقي والعمودي للشاشة
    // ملاحظة: سيتم التحكم في اتجاه الشاشة على مستوى كل شاشة حسب الحاجة
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // تهيئة خدمة التخزين المؤقت
    final cacheService = CacheService();
    cacheService.init();

    // تهيئة خدمة تحسين الصور
    final imageOptimizerService = ImageOptimizerService();
    await imageOptimizerService.init();

    // Initialize app with error dialog for first launch
    _initializeApp().then((_) {
      runApp(const MyApp());
    }).catchError((error, stackTrace) {
      ErrorTracker.captureError(
        'Failed to initialize app',
        error: error,
        stackTrace: stackTrace,
        context: {'phase': 'initialization'},
      );
      // Still try to run the app even if initialization fails
      AppLogger.info('🚀🚀🚀 تشغيل التطبيق - MyApp');
      runApp(const MyApp());
    });
  });
}

/// Initialize app data
Future<void> _initializeApp() async {
  try {
    AppLogger.info('بدء تهيئة التطبيق...');
    debugPrint('APP INITIALIZATION: Starting app initialization');

    // تهيئة إعدادات التطبيق
    AppLogger.info('تهيئة إعدادات التطبيق...');

    // التحقق من صحة تعريفات الصلاحيات وإصلاحها
    AppLogger.info('التحقق من صحة تعريفات الصلاحيات والأدوار...');
    PermissionInitializer.initialize();

    // تهيئة مدير الثيم
    AppLogger.info('تهيئة مدير الثيم...');
    await themeManager.initialize();

    // تهيئة نمط المصنع للـ Presenters
    AppLogger.info('تهيئة نمط المصنع للـ Presenters...');
    PresenterFactory.initialize();
    AppLogger.info('تم تهيئة نمط المصنع بنجاح');

    // تم إلغاء تهيئة قاعدة البيانات في الخلفية
    // لأن هذا يتعارض مع النهج الجديد الذي يفصل المسؤوليات بين الشاشات
    AppLogger.info('تم إلغاء تهيئة قاعدة البيانات في الخلفية');
    AppLogger.info(
        'سيتم التعامل مع قاعدة البيانات في شاشة البداية وشاشة الإعداد الأولي فقط');
    AppLogger.info('تم تهيئة التطبيق بنجاح');
  } catch (error, stackTrace) {
    ErrorTracker.captureError(
      'فشل في تهيئة التطبيق',
      error: error,
      stackTrace: stackTrace,
      context: {'operation': 'app_initialization'},
    );
    rethrow; // Let the caller handle this error
  }
}

// تم إلغاء دالة _initializeDatabaseInBackground
// لأنها لم تعد مستخدمة في النهج الجديد الذي يفصل المسؤوليات بين الشاشات

// Las funciones _checkRequiredTables y _resetDatabase han sido eliminadas
// ya que ahora usamos DatabaseInitializerImproved que maneja estas operaciones

/// مدير دورة حياة التطبيق
class AppLifecycleManager extends StatefulWidget {
  final Widget child;
  const AppLifecycleManager({Key? key, required this.child}) : super(key: key);

  @override
  State<AppLifecycleManager> createState() => _AppLifecycleManagerState();
}

class _AppLifecycleManagerState extends State<AppLifecycleManager>
    with WidgetsBindingObserver {
  Timer? _inactivityTimer;
  final int _inactivityTimeoutMinutes =
      30; // وقت الخمول بالدقائق قبل تسجيل الخروج التلقائي
  DateTime? _lastUserActivity;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _resetInactivityTimer();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _inactivityTimer?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    AppLogger.info('تغيير حالة دورة حياة التطبيق: $state');

    switch (state) {
      case AppLifecycleState.resumed:
        // التطبيق في المقدمة ونشط
        _resetInactivityTimer();

        // التحقق من حالة الإعداد والتشغيل الأول عند استئناف التطبيق
        unawaited(_checkAppState());
        break;
      case AppLifecycleState.inactive:
        // التطبيق غير نشط (مثلاً عند استقبال مكالمة)
        _lastUserActivity = DateTime.now();
        break;
      case AppLifecycleState.paused:
        // التطبيق في الخلفية
        // استخدام await مع دالة تعيد Future<void>
        unawaited(_createBackup());
        break;
      case AppLifecycleState.detached:
        // التطبيق منفصل (قد يكون مغلقاً)
        unawaited(_createBackup());
        // إغلاق قاعدة البيانات عند إغلاق التطبيق
        unawaited(_closeDatabase());
        break;
      default:
        break;
    }
  }

  /// التحقق من حالة التطبيق عند استئنافه
  Future<void> _checkAppState() async {
    try {
      // التحقق من حالة الإعداد والتشغيل الأول
      final bool isSetupCompleted = await SessionManager.isSetupCompleted();
      final bool isFirstLaunch = await SessionManager.isFirstLaunch();

      AppLogger.info('=== التحقق من حالة التطبيق عند الاستئناف ===');
      AppLogger.info('- حالة الإعداد: $isSetupCompleted');
      AppLogger.info('- التشغيل الأول: $isFirstLaunch');
      AppLogger.info('==========================================');
    } catch (e) {
      AppLogger.error('❌ خطأ في التحقق من حالة التطبيق عند الاستئناف: $e');
    }
  }

  void _resetInactivityTimer() {
    _lastUserActivity = DateTime.now();
    _inactivityTimer?.cancel();
    _inactivityTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      _checkInactivity();
    });
  }

  void _checkInactivity() {
    if (_lastUserActivity == null) return;

    final now = DateTime.now();
    final difference = now.difference(_lastUserActivity!);

    if (difference.inMinutes >= _inactivityTimeoutMinutes) {
      // تسجيل الخروج التلقائي بعد فترة الخمول
      AppLogger.info('تم تسجيل الخروج تلقائياً بسبب الخمول');
      _inactivityTimer?.cancel();

      // تنفيذ تسجيل الخروج التلقائي
      _performAutoLogout();
    }
  }

  Future<void> _performAutoLogout() async {
    try {
      AppLogger.info('بدء عملية تسجيل الخروج التلقائي بسبب الخمول');

      // 1. حفظ أي بيانات مهمة قبل تسجيل الخروج
      await _createBackup();

      // 2. حذف بيانات الجلسة الحالية
      final cacheService = CacheService();
      cacheService.remove('user_session');
      cacheService.remove('auth_token');
      cacheService.remove('current_user');

      // 3. حفظ وقت تسجيل الخروج
      cacheService.put('last_logout_time', DateTime.now().toIso8601String());

      // 4. إعادة توجيه المستخدم إلى شاشة تسجيل الدخول
      if (navigatorKey.currentState != null) {
        navigatorKey.currentState!.pushNamedAndRemoveUntil(
          AppRoutes.login,
          (route) => false,
        );
        AppLogger.info('تم إعادة توجيه المستخدم إلى شاشة تسجيل الدخول');
      } else {
        AppLogger.warning(
            'فشل في إعادة التوجيه: navigatorKey.currentState هو null');
      }
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في تنفيذ تسجيل الخروج التلقائي',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'auto_logout'},
      );

      // محاولة إعادة التوجيه حتى في حالة الخطأ
      if (navigatorKey.currentState != null) {
        navigatorKey.currentState!.pushNamedAndRemoveUntil(
          AppRoutes.login,
          (route) => false,
        );
      }
    }
  }

  Future<void> _createBackup() async {
    try {
      // تنفيذ النسخ الاحتياطي التلقائي
      final cacheService = CacheService();

      // حفظ معلومات الجلسة الحالية في التخزين المؤقت
      final currentTimestamp = DateTime.now().toIso8601String();
      cacheService.put('last_backup_time', currentTimestamp);

      AppLogger.info('تم إنشاء نسخة احتياطية تلقائية بنجاح');
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إنشاء نسخة احتياطية تلقائية',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'automatic_backup'},
      );
    }
  }

  // إغلاق قاعدة البيانات عند إغلاق التطبيق
  Future<void> _closeDatabase() async {
    try {
      AppLogger.info('إغلاق قاعدة البيانات عند إغلاق التطبيق...');
      final dbHelper = DatabaseHelper();
      await dbHelper.closeDatabase();
      AppLogger.info('تم إغلاق قاعدة البيانات بنجاح عند إغلاق التطبيق');
    } catch (e, stackTrace) {
      ErrorTracker.captureError(
        'فشل في إغلاق قاعدة البيانات عند إغلاق التطبيق',
        error: e,
        stackTrace: stackTrace,
        context: {'operation': 'close_database_on_exit'},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _resetInactivityTimer,
      onPanDown: (_) => _resetInactivityTimer(),
      onScaleStart: (_) => _resetInactivityTimer(),
      child: widget.child,
    );
  }
}

/// مدير الخروج من التطبيق
class AppExitHandler extends StatelessWidget {
  final Widget child;
  const AppExitHandler({Key? key, required this.child}) : super(key: key);

  // تسجيل إنشاء الكائن للتأكد من أنه يتم استخدامه
  static bool _logged = false;
  void _logInitialization() {
    if (!_logged) {
      AppLogger.info('✅✅✅ تم إنشاء AppExitHandler وتطبيقه على التطبيق');

      // تسجيل معلومات إضافية للتشخيص
      AppLogger.info('معلومات النظام:');
      AppLogger.info('- نظام التشغيل: ${Platform.operatingSystem}');
      AppLogger.info(
          '- إصدار نظام التشغيل: ${Platform.operatingSystemVersion}');
      AppLogger.info('- إصدار Flutter: ${Platform.version}');

      _logged = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    _logInitialization(); // تسجيل إنشاء الكائن

    AppLogger.info('تنفيذ بناء AppExitHandler');
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        AppLogger.info(
            '🔄🔄🔄 تم استدعاء onPopInvokedWithResult في AppExitHandler');
        AppLogger.info('- didPop: $didPop');
        AppLogger.info('- result: $result');
        AppLogger.info('- context: ${context.toString()}');
        AppLogger.info('- mounted: ${context.mounted}');
        AppLogger.info(
            '- current route: ${ModalRoute.of(context)?.settings.name}');

        if (didPop) {
          AppLogger.info('⚠️ تم بالفعل تنفيذ الخروج، لا داعي لعرض مربع الحوار');
          return;
        }

        AppLogger.info('🔄 جاري عرض مربع حوار تأكيد الخروج');

        // عرض مربع حوار تأكيد الخروج
        AppLogger.info('تم تنشيط PopScope - عرض مربع حوار تأكيد الخروج');

        final bool shouldPop = await showDialog<bool>(
              context: context,
              barrierDismissible: false, // منع إغلاق مربع الحوار بالضغط خارجه
              builder: (context) => AlertDialog(
                title: const Text('تأكيد الخروج'),
                content: const Text('هل تريد الخروج من التطبيق؟'),
                actions: [
                  TextButton(
                    onPressed: () {
                      AppLogger.info('تم الضغط على زر الإلغاء');
                      Navigator.of(context).pop(false);
                    },
                    child: const Text('إلغاء'),
                  ),
                  TextButton(
                    onPressed: () {
                      AppLogger.info('تم الضغط على زر الخروج');
                      Navigator.of(context).pop(true);
                    },
                    child: const Text('خروج'),
                  ),
                ],
              ),
            ) ??
            false;

        AppLogger.info('نتيجة مربع حوار تأكيد الخروج: $shouldPop');

        if (shouldPop) {
          AppLogger.info('تنفيذ الخروج من التطبيق');

          // إغلاق قاعدة البيانات قبل إغلاق التطبيق
          try {
            AppLogger.info('إغلاق قاعدة البيانات قبل إغلاق التطبيق...');
            final dbHelper = DatabaseHelper();
            await dbHelper.closeDatabase();
            AppLogger.info('تم إغلاق قاعدة البيانات بنجاح قبل إغلاق التطبيق');
          } catch (e) {
            AppLogger.error(
                'فشل في إغلاق قاعدة البيانات قبل إغلاق التطبيق: $e');
          }

          SystemNavigator.pop();
        }
      },
      child: child,
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // تطبيق مدير دورة الحياة ومدير الخروج من التطبيق
    AppLogger.info(
        '🏗️🏗️🏗️ بناء MyApp - تطبيق AppLifecycleManager و AppExitHandler');
    return AppLifecycleManager(
      child: AppExitHandler(
        child: MultiProvider(
          // استخدام التحميل الأساسي فقط لتحسين وقت البدء
          // الـ Presenters الأخرى ستُحمل كسولياً عند الحاجة
          providers: AppProviders.getMinimalProviders(),
          child: Consumer<ThemeManager>(
            builder: (context, themeManager, child) {
              return ResponsiveApp(
                navigatorKey: navigatorKey, // استخدام المفتاح العالمي للتنقل
                title: AppConstants.appNameEn,
                debugShowCheckedModeBanner: false,
                theme: themeManager.createUnifiedLightTheme(),
                darkTheme: themeManager.createUnifiedDarkTheme(),
                themeMode: themeManager.themeMode,
                locale: AppConstants.defaultLocale,
                supportedLocales: const [
                  Locale('ar'),
                  Locale('en'),
                ],
                localizationsDelegates: const [
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                initialRoute: AppRoutes.splash,
                routes: AppRoutes.routes,
                onGenerateRoute: AppRoutes.onGenerateRoute,
                // ResponsiveApp سيتعامل تلقائيًا مع التكييف مع الشاشات المختلفة
                builder: (context, child) {
                  // تهيئة Layout قبل بناء الشاشة
                  Layout.init(context);
                  return child ??
                      Container(); // إضافة حالة تعامل مع child إذا كانت null
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
