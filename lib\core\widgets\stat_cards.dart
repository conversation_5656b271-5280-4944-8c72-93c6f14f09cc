import 'package:flutter/material.dart';
import '../utils/index.dart';
import '../../../core/theme/index.dart';

/// بطاقة إحصائية عامة
class StatCard extends StatelessWidget {
  /// عنوان البطاقة
  final String title;

  /// قيمة البطاقة
  final String value;

  /// أيقونة البطاقة
  final IconData icon;

  /// لون البطاقة
  final Color? color;

  /// عرض البطاقة
  final double? width;

  /// ارتفاع البطاقة
  final double? height;

  /// دالة تنفذ عند الضغط على البطاقة
  final VoidCallback? onTap;

  /// هل تظهر الأيقونة في الأعلى
  final bool iconOnTop;

  const StatCard({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    this.color,
    this.width,
    this.height,
    this.onTap,
    this.iconOnTop = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final effectiveColor = color ?? AppColors.primary;

    return SizedBox(
      width: width,
      height: height,
      child: Card(
        elevation: 2,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(Layout.defaultRadius),
        ),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(Layout.defaultSpacing),
            child: iconOnTop
                ? _buildVerticalLayout(context, effectiveColor)
                : _buildHorizontalLayout(context, effectiveColor),
          ),
        ),
      ),
    );
  }

  /// بناء تخطيط أفقي (الأيقونة بجانب العنوان)
  Widget _buildHorizontalLayout(BuildContext context, Color effectiveColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                title,
                style: const AppTypography(
                  fontSize: Layout.mediumFontSize,
                  color: AppColors.lightTextSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              icon,
              size: Layout.mediumIconSize,
              color: effectiveColor,
            ),
          ],
        ),
        const Spacer(),
        Text(
          value,
          style: const AppTypography(
            fontSize: Layout.largeFontSize,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// بناء تخطيط عمودي (الأيقونة فوق العنوان)
  Widget _buildVerticalLayout(BuildContext context, Color effectiveColor) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          icon,
          size: Layout.mediumIconSize,
          color: effectiveColor,
        ),
        const SizedBox(height: Layout.smallSpacing),
        Text(
          title,
          style: const AppTypography(
            fontSize: Layout.smallFontSize,
            color: AppColors.lightTextSecondary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: Layout.smallSpacing),
        Text(
          value,
          style: const AppTypography(
            fontSize: Layout.mediumFontSize,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

/// بطاقة إحصائية مدمجة
class CompactStatCard extends StatelessWidget {
  /// عنوان البطاقة
  final String title;

  /// قيمة البطاقة
  final String value;

  /// أيقونة البطاقة
  final IconData icon;

  /// لون البطاقة
  final Color color;

  /// دالة تنفذ عند الضغط على البطاقة
  final VoidCallback? onTap;

  const CompactStatCard({
    Key? key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
        elevation: 2,
        margin: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(Layout.defaultRadius),
        ),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
            onTap: onTap,
            child: Padding(
              padding: const EdgeInsets.all(Layout.defaultSpacing),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(icon, color: color, size: Layout.smallIconSize),
                      const SizedBox(width: Layout.smallSpacing),
                      Flexible(
                        child: Text(
                          title,
                          style: AppTypography(
                            color: color,
                            fontSize: Layout.smallFontSize,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: Layout.smallSpacing),
                  Text(
                    value,
                    style: const AppTypography(
                      fontWeight: FontWeight.bold,
                      fontSize: Layout.mediumFontSize,
                    ),
                  ),
                ],
              ),
            )));
  }
}

/// مجموعة بطاقات إحصائية
class StatCardGrid extends StatelessWidget {
  /// قائمة البطاقات
  final List<Map<String, dynamic>> stats;

  /// عدد الأعمدة
  final int crossAxisCount;

  /// نسبة العرض إلى الارتفاع
  final double childAspectRatio;

  /// المسافة بين البطاقات
  final double spacing;

  /// دالة بناء البطاقة
  final Widget Function(Map<String, dynamic> stat)? cardBuilder;

  const StatCardGrid({
    Key? key,
    required this.stats,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.5,
    this.spacing = 8.0,
    this.cardBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: Layout.isDesktop()
            ? crossAxisCount * 2
            : Layout.isTablet()
                ? crossAxisCount + 1
                : crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];

        if (cardBuilder != null) {
          return cardBuilder!(stat);
        }

        return StatCard(
          title: stat['title'] ?? '',
          value: stat['value'] ?? '',
          icon: stat['icon'] ?? Icons.analytics,
          color: stat['color'],
          onTap: stat['onTap'],
        );
      },
    );
  }
}
