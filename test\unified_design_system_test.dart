import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:tajer_plus/core/theme/app_dimensions.dart';
import 'package:tajer_plus/core/widgets/adaptive_card.dart';
import 'package:tajer_plus/features/shared/widgets/custom_card.dart';

void main() {
  group('نظام التصميم الموحد - اختبارات', () {
    test('اختبار المسافات الموحدة', () {
      // التحقق من وجود جميع المسافات
      expect(AppDimensions.spacing2, 2.0);
      expect(AppDimensions.spacing4, 4.0);
      expect(AppDimensions.spacing8, 8.0);
      expect(AppDimensions.spacing12, 12.0);
      expect(AppDimensions.spacing16, 16.0);
      expect(AppDimensions.spacing20, 20.0);
      expect(AppDimensions.spacing24, 24.0);
      expect(AppDimensions.spacing32, 32.0);
      expect(AppDimensions.spacing48, 48.0);
      expect(AppDimensions.spacing64, 64.0);
    });

    test('اختبار أحجام البطاقات الموحدة', () {
      // التحقق من أحجام البطاقات
      expect(AppDimensions.cardHeightTiny, 60.0);
      expect(AppDimensions.cardHeightSmall, 80.0);
      expect(AppDimensions.cardHeightMedium, 120.0);
      expect(AppDimensions.cardHeightLarge, 160.0);
      expect(AppDimensions.cardHeightXLarge, 200.0);
      expect(AppDimensions.cardHeightXXLarge, 240.0);
    });

    test('اختبار أحجام الأيقونات الموحدة', () {
      // التحقق من أحجام الأيقونات
      expect(AppDimensions.iconSizeTiny, 12.0);
      expect(AppDimensions.iconSizeSmall, 16.0);
      expect(AppDimensions.iconSizeMedium, 24.0);
      expect(AppDimensions.iconSizeLarge, 32.0);
      expect(AppDimensions.iconSizeXLarge, 48.0);
      expect(AppDimensions.iconSizeXXLarge, 64.0);
    });

    test('اختبار نصف الأقطار الموحدة', () {
      // التحقق من نصف الأقطار
      expect(AppDimensions.radiusTiny, 4.0);
      expect(AppDimensions.radiusSmall, 8.0);
      expect(AppDimensions.radiusMedium, 12.0);
      expect(AppDimensions.radiusLarge, 16.0);
      expect(AppDimensions.radiusXLarge, 20.0);
      expect(AppDimensions.radiusXXLarge, 24.0);
      expect(AppDimensions.radiusCircular, 50.0);
    });

    test('اختبار ارتفاعات الظلال الموحدة', () {
      // التحقق من ارتفاعات الظلال
      expect(AppDimensions.elevationNone, 0.0);
      expect(AppDimensions.elevationLow, 2.0);
      expect(AppDimensions.elevationMedium, 4.0);
      expect(AppDimensions.elevationHigh, 8.0);
      expect(AppDimensions.elevationXHigh, 16.0);
      expect(AppDimensions.elevationXXHigh, 24.0);
    });

    test('اختبار الدوال المساعدة للمسافات', () {
      // التحقق من دوال المسافات
      expect(AppDimensions.getSpacing('tiny'), AppDimensions.spacing2);
      expect(AppDimensions.getSpacing('small'), AppDimensions.spacing8);
      expect(AppDimensions.getSpacing('medium'), AppDimensions.spacing16);
      expect(AppDimensions.getSpacing('large'), AppDimensions.spacing24);
      expect(AppDimensions.getSpacing('xlarge'), AppDimensions.spacing32);
      expect(AppDimensions.getSpacing('unknown'),
          AppDimensions.spacing16); // القيمة الافتراضية
    });

    test('اختبار الدوال المساعدة للنصف أقطار', () {
      // التحقق من دوال نصف الأقطار
      expect(AppDimensions.getRadius('tiny'), AppDimensions.radiusTiny);
      expect(AppDimensions.getRadius('small'), AppDimensions.radiusSmall);
      expect(AppDimensions.getRadius('medium'), AppDimensions.radiusMedium);
      expect(AppDimensions.getRadius('large'), AppDimensions.radiusLarge);
      expect(AppDimensions.getRadius('xlarge'), AppDimensions.radiusXLarge);
      expect(AppDimensions.getRadius('xxlarge'), AppDimensions.radiusXXLarge);
      expect(AppDimensions.getRadius('circular'), AppDimensions.radiusCircular);
      expect(AppDimensions.getRadius('unknown'),
          AppDimensions.radiusMedium); // القيمة الافتراضية
    });

    test('اختبار الدوال المساعدة للظلال', () {
      // التحقق من دوال الظلال
      expect(AppDimensions.getElevation('none'), AppDimensions.elevationNone);
      expect(AppDimensions.getElevation('low'), AppDimensions.elevationLow);
      expect(
          AppDimensions.getElevation('medium'), AppDimensions.elevationMedium);
      expect(AppDimensions.getElevation('high'), AppDimensions.elevationHigh);
      expect(AppDimensions.getElevation('xhigh'), AppDimensions.elevationXHigh);
      expect(
          AppDimensions.getElevation('xxhigh'), AppDimensions.elevationXXHigh);
      expect(AppDimensions.getElevation('unknown'),
          AppDimensions.elevationMedium); // القيمة الافتراضية
    });

    test('اختبار الدوال المساعدة للأيقونات', () {
      // التحقق من دوال الأيقونات
      expect(AppDimensions.getIconSize('tiny'), AppDimensions.iconSizeTiny);
      expect(AppDimensions.getIconSize('small'), AppDimensions.iconSizeSmall);
      expect(AppDimensions.getIconSize('medium'), AppDimensions.iconSizeMedium);
      expect(AppDimensions.getIconSize('large'), AppDimensions.iconSizeLarge);
      expect(AppDimensions.getIconSize('xlarge'), AppDimensions.iconSizeXLarge);
      expect(
          AppDimensions.getIconSize('xxlarge'), AppDimensions.iconSizeXXLarge);
      expect(AppDimensions.getIconSize('unknown'),
          AppDimensions.iconSizeMedium); // القيمة الافتراضية
    });

    test('اختبار هوامش البطاقات', () {
      // التحقق من هوامش البطاقات
      expect(
          AppDimensions.getCardPadding('tiny'), AppDimensions.cardPaddingTiny);
      expect(AppDimensions.getCardPadding('small'),
          AppDimensions.cardPaddingSmall);
      expect(AppDimensions.getCardPadding('medium'),
          AppDimensions.cardPaddingMedium);
      expect(AppDimensions.getCardPadding('large'),
          AppDimensions.cardPaddingLarge);
      expect(AppDimensions.getCardPadding('xlarge'),
          AppDimensions.cardPaddingXLarge);
      expect(AppDimensions.getCardPadding('unknown'),
          AppDimensions.cardPaddingMedium); // القيمة الافتراضية
    });

    testWidgets('اختبار AdaptiveCard.small', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AdaptiveCard.small(
              child: Text('اختبار'),
            ),
          ),
        ),
      );

      expect(find.text('اختبار'), findsOneWidget);
      expect(find.byType(AdaptiveCard), findsOneWidget);
    });

    testWidgets('اختبار AdaptiveCard.medium', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AdaptiveCard.medium(
              child: Text('اختبار متوسط'),
            ),
          ),
        ),
      );

      expect(find.text('اختبار متوسط'), findsOneWidget);
      expect(find.byType(AdaptiveCard), findsOneWidget);
    });

    testWidgets('اختبار AdaptiveCard.large', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AdaptiveCard.large(
              child: Text('اختبار كبير'),
            ),
          ),
        ),
      );

      expect(find.text('اختبار كبير'), findsOneWidget);
      expect(find.byType(AdaptiveCard), findsOneWidget);
    });

    testWidgets('اختبار CustomCard.medium', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CustomCard.medium(
              child: Text('بطاقة مخصصة'),
            ),
          ),
        ),
      );

      expect(find.text('بطاقة مخصصة'), findsOneWidget);
      expect(find.byType(CustomCard), findsOneWidget);
    });

    testWidgets('اختبار AdaptiveStatsCard.medium', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: AdaptiveStatsCard.medium(
              title: 'المبيعات',
              value: '1000 ر.س',
              subtitle: 'إجمالي المبيعات',
              icon: Icons.shopping_cart,
            ),
          ),
        ),
      );

      expect(find.text('المبيعات'), findsOneWidget);
      expect(find.text('1000 ر.س'), findsOneWidget);
      expect(find.text('إجمالي المبيعات'), findsOneWidget);
      expect(find.byIcon(Icons.shopping_cart), findsOneWidget);
      expect(find.byType(AdaptiveStatsCard), findsOneWidget);
    });

    testWidgets('اختبار AdaptiveButton.medium', (WidgetTester tester) async {
      bool pressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdaptiveButton.medium(
              text: 'اضغط هنا',
              onPressed: () => pressed = true,
            ),
          ),
        ),
      );

      expect(find.text('اضغط هنا'), findsOneWidget);
      expect(find.byType(AdaptiveButton), findsOneWidget);

      await tester.tap(find.byType(AdaptiveButton));
      expect(pressed, true);
    });
  });
}
